import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/advanced_search_service.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  bool _showFilters = false;
  String _selectedCategory = 'الكل';
  RangeValues _priceRange = const RangeValues(0, 1000);
  bool _lowStockOnly = false;
  String _sortBy = 'name';
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // إضافة AdvancedSearchProvider إذا لم يكن موجوداً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final searchProvider = context.read<AdvancedSearchProvider>();
      searchProvider.updateSearchSuggestions(
        products: context.read<ProductProvider>().products,
        customers: context.read<CustomerProvider>().customers,
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('البحث المتقدم'),
          backgroundColor: Colors.deepPurple,
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
              onPressed: () {
                setState(() {
                  _showFilters = !_showFilters;
                });
              },
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            tabs: const [
              Tab(icon: Icon(Icons.inventory_2), text: 'المنتجات'),
              Tab(icon: Icon(Icons.people), text: 'العملاء'),
              Tab(icon: Icon(Icons.receipt), text: 'المبيعات'),
            ],
          ),
        ),
        body: Column(
          children: [
            // شريط البحث
            _buildSearchBar(),
            
            // الفلاتر المتقدمة
            if (_showFilters) _buildAdvancedFilters(),
            
            // النتائج
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildProductResults(),
                  _buildCustomerResults(),
                  _buildSaleResults(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Consumer<AdvancedSearchProvider>(
      builder: (context, searchProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            children: [
              TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: 'ابحث في المنتجات، العملاء، والمبيعات...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            searchProvider.clearSearch();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
                onChanged: (value) {
                  searchProvider.updateQuery(value);
                  if (value.isNotEmpty) {
                    searchProvider.updateSearchSuggestions(
                      products: context.read<ProductProvider>().products,
                      customers: context.read<CustomerProvider>().customers,
                    );
                  }
                },
                onSubmitted: (value) {
                  _performSearch();
                },
              ),
              
              // اقتراحات البحث
              if (searchProvider.searchSuggestions.isNotEmpty && _searchFocusNode.hasFocus)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: searchProvider.searchSuggestions.length,
                    itemBuilder: (context, index) {
                      final suggestion = searchProvider.searchSuggestions[index];
                      return ListTile(
                        leading: const Icon(Icons.search, size: 20),
                        title: Text(suggestion),
                        onTap: () {
                          _searchController.text = suggestion;
                          searchProvider.updateQuery(suggestion);
                          _searchFocusNode.unfocus();
                          _performSearch();
                        },
                      );
                    },
                  ),
                ),
              
              // عمليات البحث الأخيرة
              if (searchProvider.recentSearches.isNotEmpty && _searchController.text.isEmpty)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    spacing: 8,
                    children: searchProvider.recentSearches.take(5).map((search) {
                      return ActionChip(
                        label: Text(search),
                        onPressed: () {
                          _searchController.text = search;
                          searchProvider.updateQuery(search);
                          _performSearch();
                        },
                      );
                    }).toList(),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAdvancedFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلاتر متقدمة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // فلتر النطاق السعري
          const Text('النطاق السعري'),
          RangeSlider(
            values: _priceRange,
            min: 0,
            max: 1000,
            divisions: 20,
            labels: RangeLabels(
              '${_priceRange.start.round()} ر.س',
              '${_priceRange.end.round()} ر.س',
            ),
            onChanged: (values) {
              setState(() {
                _priceRange = values;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              // فلتر المخزون المنخفض
              Expanded(
                child: CheckboxListTile(
                  title: const Text('مخزون منخفض فقط'),
                  value: _lowStockOnly,
                  onChanged: (value) {
                    setState(() {
                      _lowStockOnly = value ?? false;
                    });
                  },
                ),
              ),
              
              // ترتيب النتائج
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'name', child: Text('الاسم')),
                    DropdownMenuItem(value: 'price', child: Text('السعر')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'value', child: Text('القيمة')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value ?? 'name';
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              ElevatedButton(
                onPressed: _performSearch,
                child: const Text('تطبيق الفلاتر'),
              ),
              const SizedBox(width: 16),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductResults() {
    return Consumer3<AdvancedSearchProvider, ProductProvider, CustomerProvider>(
      builder: (context, searchProvider, productProvider, customerProvider, child) {
        if (searchProvider.isSearching) {
          return const Center(child: CircularProgressIndicator());
        }

        final products = searchProvider.searchResults['products'] as List<Product>? ?? [];
        
        if (products.isEmpty && searchProvider.currentQuery.isNotEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.search_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد منتجات مطابقة للبحث',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.blue,
                  child: Text(
                    product.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(
                  product.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (product.description != null && product.description!.isNotEmpty)
                      Text(product.description!),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text('السعر: ${product.price?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                        const SizedBox(width: 16),
                        Text('الكمية: ${product.quantity?.toStringAsFixed(0) ?? '0'}'),
                      ],
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if ((product.quantity ?? 0) <= 10)
                      const Icon(Icons.warning, color: Colors.red, size: 20),
                    Text(
                      '${((product.price ?? 0) * (product.quantity ?? 0)).toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  // فتح تفاصيل المنتج
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCustomerResults() {
    return Consumer<AdvancedSearchProvider>(
      builder: (context, searchProvider, child) {
        if (searchProvider.isSearching) {
          return const Center(child: CircularProgressIndicator());
        }

        final customers = searchProvider.searchResults['customers'] as List<Customer>? ?? [];
        
        if (customers.isEmpty && searchProvider.currentQuery.isNotEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.people_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا يوجد عملاء مطابقون للبحث',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: customers.length,
          itemBuilder: (context, index) {
            final customer = customers[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.orange,
                  child: Text(
                    customer.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(
                  customer.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (customer.phone != null && customer.phone!.isNotEmpty)
                      Text('الهاتف: ${customer.phone}'),
                    if (customer.email != null && customer.email!.isNotEmpty)
                      Text('البريد: ${customer.email}'),
                    if (customer.address != null && customer.address!.isNotEmpty)
                      Text('العنوان: ${customer.address}'),
                  ],
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // فتح تفاصيل العميل
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSaleResults() {
    return Consumer<AdvancedSearchProvider>(
      builder: (context, searchProvider, child) {
        if (searchProvider.isSearching) {
          return const Center(child: CircularProgressIndicator());
        }

        final sales = searchProvider.searchResults['sales'] as List<Sale>? ?? [];
        
        if (sales.isEmpty && searchProvider.currentQuery.isNotEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد مبيعات مطابقة للبحث',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: sales.length,
          itemBuilder: (context, index) {
            final sale = sales[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.purple,
                  child: Text(
                    '${sale.id ?? 0}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(
                  'فاتورة رقم ${sale.id ?? 'غير محدد'}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('المبلغ: ${sale.total?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                    if (sale.date != null)
                      Text('التاريخ: ${DateTime.parse(sale.date!).day}/${DateTime.parse(sale.date!).month}/${DateTime.parse(sale.date!).year}'),
                  ],
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // فتح تفاصيل البيع
                },
              ),
            );
          },
        );
      },
    );
  }

  void _performSearch() {
    final searchProvider = context.read<AdvancedSearchProvider>();
    final productProvider = context.read<ProductProvider>();
    final customerProvider = context.read<CustomerProvider>();
    final saleProvider = context.read<SaleProvider>();

    searchProvider.performGlobalSearch(
      products: productProvider.products,
      customers: customerProvider.customers,
      sales: saleProvider.sales,
    );
  }

  void _resetFilters() {
    setState(() {
      _priceRange = const RangeValues(0, 1000);
      _lowStockOnly = false;
      _sortBy = 'name';
      _sortAscending = true;
      _selectedCategory = 'الكل';
    });
  }
}
