import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/screens/purchases/purchase_details_screen.dart';

class PurchasesScreen extends StatelessWidget {
  const PurchasesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PurchaseProvider purchaseProvider =
        Provider.of<PurchaseProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Purchases'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (BuildContext context) => PurchaseDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: purchaseProvider.purchases.length,
        itemBuilder: (BuildContext context, int index) {
          final Purchase purchase = purchaseProvider.purchases[index];
          return Card(
            child: ListTile(
              title: Text('Purchase ID: ${purchase.id}'),
              subtitle: Text('Supplier ID: ${purchase.supplierId}'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              PurchaseDetailsScreen(purchase: purchase),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      purchaseProvider.deletePurchase(purchase.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
