import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'database_service.dart';

/// خدمة النسخ الاحتياطي لقاعدة البيانات
class DatabaseBackupService {
  static final DatabaseBackupService _instance = DatabaseBackupService._internal();
  factory DatabaseBackupService() => _instance;
  DatabaseBackupService._internal();

  static DatabaseBackupService get instance => _instance;

  // ==================== Database Backup Operations ====================

  /// Get database file path
  Future<String> getDatabaseFilePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  /// Copy database file to a new location
  Future<void> copyDatabaseFile(String sourcePath, String destinationPath) async {
    try {
      final File sourceFile = File(sourcePath);
      final File destinationFile = File(destinationPath);

      // Ensure destination directory exists
      final Directory destinationDir = destinationFile.parent;
      if (!await destinationDir.exists()) {
        await destinationDir.create(recursive: true);
      }

      // Copy the file
      await sourceFile.copy(destinationPath);
      print('✅ Database file copied from $sourcePath to $destinationPath');
    } catch (e) {
      print('❌ Error copying database file: $e');
      rethrow;
    }
  }

  /// Restore database from a backup file
  Future<void> restoreDatabaseFromFile(String backupFilePath) async {
    try {
      final String currentDbPath = await getDatabaseFilePath();
      
      // Close current database connection
      await DatabaseService.instance.resetDatabase();
      
      // Copy backup file to current database location
      await copyDatabaseFile(backupFilePath, currentDbPath);
      
      // Verify the restored database
      await verifyDatabaseIntegrity();
      
      print('✅ Database restored successfully from $backupFilePath');
    } catch (e) {
      print('❌ Error restoring database from file: $e');
      rethrow;
    }
  }

  /// Create a timestamped backup of the current database
  Future<String> createTimestampedBackup(String backupDirectory) async {
    try {
      final String currentDbPath = await getDatabaseFilePath();
      final DateTime now = DateTime.now();
      final String timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
      final String backupFileName = 'inventory_backup_$timestamp.db';
      final String backupPath = join(backupDirectory, backupFileName);

      await copyDatabaseFile(currentDbPath, backupPath);
      
      print('✅ Timestamped backup created: $backupPath');
      return backupPath;
    } catch (e) {
      print('❌ Error creating timestamped backup: $e');
      rethrow;
    }
  }

  /// Verify database integrity
  Future<bool> verifyDatabaseIntegrity() async {
    try {
      final Database db = await DatabaseService.instance.database;
      
      // Run integrity check
      final List<Map<String, dynamic>> result = await db.rawQuery('PRAGMA integrity_check');
      
      if (result.isNotEmpty && result.first.values.first == 'ok') {
        print('✅ Database integrity check passed');
        return true;
      } else {
        print('❌ Database integrity check failed: $result');
        return false;
      }
    } catch (e) {
      print('❌ Error verifying database integrity: $e');
      return false;
    }
  }

  /// Get database statistics for backup verification
  Future<Map<String, dynamic>> getDatabaseStatistics() async {
    try {
      final Database db = await DatabaseService.instance.database;
      
      final Map<String, dynamic> stats = <String, dynamic>{};
      
      // Get database file size
      final String dbPath = await getDatabaseFilePath();
      final File dbFile = File(dbPath);
      if (await dbFile.exists()) {
        stats['fileSize'] = await dbFile.length();
        stats['lastModified'] = await dbFile.lastModified();
      }
      
      // Count records in main tables
      final List<String> tables = <String>[
        'products', 'customers', 'suppliers', 'sales', 'purchases', 'orders', 'expenses'
      ];
      
      int totalRecords = 0;
      for (final String table in tables) {
        try {
          final List<Map<String, dynamic>> result = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $table'
          );
          final int count = result.first['count'] as int;
          stats['${table}_count'] = count;
          totalRecords += count;
        } catch (e) {
          print('⚠️ Error counting $table: $e');
          stats['${table}_count'] = 0;
        }
      }
      
      stats['total_records'] = totalRecords;
      stats['database_version'] = await _getDatabaseVersion(db);
      
      print('📊 Database statistics: $stats');
      return stats;
    } catch (e) {
      print('❌ Error getting database statistics: $e');
      return <String, dynamic>{};
    }
  }

  /// Get database version
  Future<int> _getDatabaseVersion(Database db) async {
    try {
      final List<Map<String, dynamic>> result = await db.rawQuery('PRAGMA user_version');
      return result.first['user_version'] as int;
    } catch (e) {
      print('⚠️ Error getting database version: $e');
      return 0;
    }
  }

  /// Clean old backup files (keep only the most recent N backups)
  Future<void> cleanOldBackups(String backupDirectory, {int keepCount = 5}) async {
    try {
      final Directory dir = Directory(backupDirectory);
      if (!await dir.exists()) {
        return;
      }

      // Get all backup files
      final List<FileSystemEntity> files = await dir.list().toList();
      final List<File> backupFiles = files
          .whereType<File>()
          .where((File file) => file.path.contains('inventory_backup_') && file.path.endsWith('.db'))
          .toList();

      if (backupFiles.length <= keepCount) {
        print('📁 No old backups to clean (${backupFiles.length} files, keeping $keepCount)');
        return;
      }

      // Sort by last modified date (newest first)
      backupFiles.sort((File a, File b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // Delete old files
      final List<File> filesToDelete = backupFiles.skip(keepCount).toList();
      for (final File file in filesToDelete) {
        try {
          await file.delete();
          print('🗑️ Deleted old backup: ${file.path}');
        } catch (e) {
          print('⚠️ Error deleting backup file ${file.path}: $e');
        }
      }

      print('✅ Cleaned ${filesToDelete.length} old backup files');
    } catch (e) {
      print('❌ Error cleaning old backups: $e');
    }
  }

  /// Get backup file information
  Future<Map<String, dynamic>> getBackupFileInfo(String backupFilePath) async {
    try {
      final File backupFile = File(backupFilePath);
      
      if (!await backupFile.exists()) {
        throw Exception('Backup file does not exist: $backupFilePath');
      }

      final Map<String, dynamic> info = <String, dynamic>{
        'path': backupFilePath,
        'name': basename(backupFilePath),
        'size': await backupFile.length(),
        'lastModified': await backupFile.lastModified(),
        'sizeFormatted': _formatFileSize(await backupFile.length()),
      };

      // Try to get database statistics from the backup file
      try {
        // Temporarily copy and open the backup to get stats
        final String tempPath = '${backupFilePath}_temp';
        await copyDatabaseFile(backupFilePath, tempPath);
        
        final Database tempDb = await openDatabase(tempPath, readOnly: true);
        final List<Map<String, dynamic>> result = await tempDb.rawQuery(
          'SELECT COUNT(*) as count FROM products'
        );
        info['products_count'] = result.first['count'] as int;
        
        await tempDb.close();
        await File(tempPath).delete();
      } catch (e) {
        print('⚠️ Could not read backup file statistics: $e');
        info['products_count'] = 'Unknown';
      }

      return info;
    } catch (e) {
      print('❌ Error getting backup file info: $e');
      rethrow;
    }
  }

  /// Format file size in human readable format
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Test database connection
  Future<bool> testDatabaseConnection() async {
    try {
      final Database db = await DatabaseService.instance.database;
      await db.rawQuery('SELECT 1');
      print('✅ Database connection test passed');
      return true;
    } catch (e) {
      print('❌ Database connection test failed: $e');
      return false;
    }
  }

  /// Get database schema information
  Future<List<Map<String, dynamic>>> getDatabaseSchema() async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
      );
      
      print('📋 Database schema: ${tables.length} tables');
      return tables;
    } catch (e) {
      print('❌ Error getting database schema: $e');
      return <Map<String, dynamic>>[];
    }
  }
}
