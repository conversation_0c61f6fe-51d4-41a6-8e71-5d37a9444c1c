import 'package:flutter/material.dart';

/// Provider class for managing filters and search across the application
class FilterProvider extends ChangeNotifier {
  // Product filters
  String _productSearchQuery = '';
  int? _selectedCategoryId;
  int? _selectedSupplierId;
  double? _minPrice;
  double? _maxPrice;
  bool _showLowStockOnly = false;

  // Date filters
  DateTime? _startDate;
  DateTime? _endDate;

  // Customer filters
  String _customerSearchQuery = '';

  // Supplier filters
  String _supplierSearchQuery = '';

  // Sale filters
  int? _selectedCustomerId;
  double? _minSaleAmount;
  double? _maxSaleAmount;

  // Purchase filters
  int? _selectedPurchaseSupplierId;
  double? _minPurchaseAmount;
  double? _maxPurchaseAmount;

  // Product filter getters
  String get productSearchQuery => _productSearchQuery;
  int? get selectedCategoryId => _selectedCategoryId;
  int? get selectedSupplierId => _selectedSupplierId;
  double? get minPrice => _minPrice;
  double? get maxPrice => _maxPrice;
  bool get showLowStockOnly => _showLowStockOnly;

  // Date filter getters
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;

  // Customer filter getters
  String get customerSearchQuery => _customerSearchQuery;

  // Supplier filter getters
  String get supplierSearchQuery => _supplierSearchQuery;

  // Sale filter getters
  int? get selectedCustomerId => _selectedCustomerId;
  double? get minSaleAmount => _minSaleAmount;
  double? get maxSaleAmount => _maxSaleAmount;

  // Purchase filter getters
  int? get selectedPurchaseSupplierId => _selectedPurchaseSupplierId;
  double? get minPurchaseAmount => _minPurchaseAmount;
  double? get maxPurchaseAmount => _maxPurchaseAmount;

  /// Set product search query
  void setProductSearchQuery(String query) {
    _productSearchQuery = query;
    notifyListeners();
  }

  /// Set selected category filter
  void setSelectedCategory(int? categoryId) {
    _selectedCategoryId = categoryId;
    notifyListeners();
  }

  /// Set selected supplier filter
  void setSelectedSupplier(int? supplierId) {
    _selectedSupplierId = supplierId;
    notifyListeners();
  }

  /// Set price range filter
  void setPriceRange(double? minPrice, double? maxPrice) {
    _minPrice = minPrice;
    _maxPrice = maxPrice;
    notifyListeners();
  }

  /// Toggle low stock filter
  void toggleLowStockFilter() {
    _showLowStockOnly = !_showLowStockOnly;
    notifyListeners();
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    notifyListeners();
  }

  /// Set customer search query
  void setCustomerSearchQuery(String query) {
    _customerSearchQuery = query;
    notifyListeners();
  }

  /// Set supplier search query
  void setSupplierSearchQuery(String query) {
    _supplierSearchQuery = query;
    notifyListeners();
  }

  /// Set selected customer for sales filter
  void setSelectedCustomer(int? customerId) {
    _selectedCustomerId = customerId;
    notifyListeners();
  }

  /// Set sale amount range filter
  void setSaleAmountRange(double? minAmount, double? maxAmount) {
    _minSaleAmount = minAmount;
    _maxSaleAmount = maxAmount;
    notifyListeners();
  }

  /// Set selected supplier for purchases filter
  void setSelectedPurchaseSupplier(int? supplierId) {
    _selectedPurchaseSupplierId = supplierId;
    notifyListeners();
  }

  /// Set purchase amount range filter
  void setPurchaseAmountRange(double? minAmount, double? maxAmount) {
    _minPurchaseAmount = minAmount;
    _maxPurchaseAmount = maxAmount;
    notifyListeners();
  }

  /// Clear all product filters
  void clearProductFilters() {
    _productSearchQuery = '';
    _selectedCategoryId = null;
    _selectedSupplierId = null;
    _minPrice = null;
    _maxPrice = null;
    _showLowStockOnly = false;
    notifyListeners();
  }

  /// Clear all date filters
  void clearDateFilters() {
    _startDate = null;
    _endDate = null;
    notifyListeners();
  }

  /// Clear all customer filters
  void clearCustomerFilters() {
    _customerSearchQuery = '';
    notifyListeners();
  }

  /// Clear all supplier filters
  void clearSupplierFilters() {
    _supplierSearchQuery = '';
    notifyListeners();
  }

  /// Clear all sale filters
  void clearSaleFilters() {
    _selectedCustomerId = null;
    _minSaleAmount = null;
    _maxSaleAmount = null;
    notifyListeners();
  }

  /// Clear all purchase filters
  void clearPurchaseFilters() {
    _selectedPurchaseSupplierId = null;
    _minPurchaseAmount = null;
    _maxPurchaseAmount = null;
    notifyListeners();
  }

  /// Clear all filters
  void clearAllFilters() {
    clearProductFilters();
    clearDateFilters();
    clearCustomerFilters();
    clearSupplierFilters();
    clearSaleFilters();
    clearPurchaseFilters();
  }

  /// Check if any product filters are active
  bool get hasActiveProductFilters {
    return _productSearchQuery.isNotEmpty ||
        _selectedCategoryId != null ||
        _selectedSupplierId != null ||
        _minPrice != null ||
        _maxPrice != null ||
        _showLowStockOnly;
  }

  /// Check if any date filters are active
  bool get hasActiveDateFilters {
    return _startDate != null || _endDate != null;
  }

  /// Check if any filters are active
  bool get hasActiveFilters {
    return hasActiveProductFilters ||
        hasActiveDateFilters ||
        _customerSearchQuery.isNotEmpty ||
        _supplierSearchQuery.isNotEmpty ||
        _selectedCustomerId != null ||
        _minSaleAmount != null ||
        _maxSaleAmount != null ||
        _selectedPurchaseSupplierId != null ||
        _minPurchaseAmount != null ||
        _maxPurchaseAmount != null;
  }

  /// Get filter summary as a map
  Map<String, dynamic> getFilterSummary() {
    return <String, dynamic>{
      'productSearch': _productSearchQuery,
      'categoryId': _selectedCategoryId,
      'supplierId': _selectedSupplierId,
      'priceRange': _minPrice != null || _maxPrice != null
          ? <String, double?>{'min': _minPrice, 'max': _maxPrice}
          : null,
      'lowStockOnly': _showLowStockOnly,
      'dateRange': _startDate != null || _endDate != null
          ? <String, DateTime?>{'start': _startDate, 'end': _endDate}
          : null,
      'customerSearch': _customerSearchQuery,
      'supplierSearch': _supplierSearchQuery,
      'hasActiveFilters': hasActiveFilters,
    };
  }
}
