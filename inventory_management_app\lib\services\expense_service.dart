import 'package:sqflite/sqflite.dart';
import '../models/expense.dart';
import 'database_service.dart';

/// Service class for handling Expense CRUD operations
class ExpenseService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all expenses from the database
  Future<List<Expense>> getAllExpenses() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('expenses');
    
    return List.generate(maps.length, (int i) {
      return Expense.fromMap(maps[i]);
    });
  }

  /// Get an expense by its ID
  Future<Expense?> getExpenseById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Expense.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new expense into the database
  Future<int> insertExpense(Expense expense) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'expenses',
      expense.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing expense in the database
  Future<int> updateExpense(Expense expense) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'expenses',
      expense.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[expense.id],
    );
  }

  /// Delete an expense from the database
  Future<int> deleteExpense(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'expenses',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Get expenses by category
  Future<List<Expense>> getExpensesByCategory(int categoryId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'categoryId = ?',
      whereArgs: <Object?>[categoryId],
    );
    
    return List.generate(maps.length, (int i) {
      return Expense.fromMap(maps[i]);
    });
  }

  /// Get expenses by date range
  Future<List<Expense>> getExpensesByDateRange(String startDate, String endDate) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <Object?>[startDate, endDate],
    );
    
    return List.generate(maps.length, (int i) {
      return Expense.fromMap(maps[i]);
    });
  }

  /// Get total expenses amount
  Future<double> getTotalExpensesAmount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result = await db.rawQuery('SELECT SUM(amount) as total FROM expenses');
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total number of expenses
  Future<int> getExpenseCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result = await db.rawQuery('SELECT COUNT(*) as count FROM expenses');
    return result.first['count'] as int;
  }

  /// Get expenses by amount range
  Future<List<Expense>> getExpensesByAmountRange(double minAmount, double maxAmount) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'amount BETWEEN ? AND ?',
      whereArgs: <Object?>[minAmount, maxAmount],
    );
    
    return List.generate(maps.length, (int i) {
      return Expense.fromMap(maps[i]);
    });
  }

  /// Search expenses by notes
  Future<List<Expense>> searchExpensesByNotes(String notes) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'notes LIKE ?',
      whereArgs: <Object?>['%$notes%'],
    );
    
    return List.generate(maps.length, (int i) {
      return Expense.fromMap(maps[i]);
    });
  }
}
