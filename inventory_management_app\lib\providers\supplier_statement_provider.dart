import 'package:flutter/material.dart';
import '../models/supplier.dart';
import '../models/purchase.dart';
import '../models/expense.dart';
import '../services/database_service.dart';

class SupplierStatementTransaction {
  final DateTime date;
  final String description;
  final double amount;
  final String type; // 'debit' or 'credit'
  final double balance;
  final int? referenceId;

  SupplierStatementTransaction({
    required this.date,
    required this.description,
    required this.amount,
    required this.type,
    required this.balance,
    this.referenceId,
  });

  String get formattedAmount {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color get amountColor {
    return type == 'debit' ? Colors.red : Colors.green;
  }

  String get typeLabel {
    return type == 'debit' ? 'مدين' : 'دائن';
  }
}

class SupplierStatementProvider extends ChangeNotifier {
  List<SupplierStatementTransaction> _transactions = [];
  Supplier? _selectedSupplier;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;
  String? _error;
  double _totalDebit = 0.0;
  double _totalCredit = 0.0;
  double _finalBalance = 0.0;

  List<SupplierStatementTransaction> get transactions => _transactions;
  Supplier? get selectedSupplier => _selectedSupplier;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get totalDebit => _totalDebit;
  double get totalCredit => _totalCredit;
  double get finalBalance => _finalBalance;

  String get formattedTotalDebit => '${_totalDebit.toStringAsFixed(2)} ر.س';
  String get formattedTotalCredit => '${_totalCredit.toStringAsFixed(2)} ر.س';
  String get formattedFinalBalance => '${_finalBalance.toStringAsFixed(2)} ر.س';

  void setSupplier(Supplier supplier) {
    _selectedSupplier = supplier;
    notifyListeners();
  }

  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  void clearSelection() {
    _selectedSupplier = null;
    _startDate = null;
    _endDate = null;
    _transactions.clear();
    _totalDebit = 0.0;
    _totalCredit = 0.0;
    _finalBalance = 0.0;
    _error = null;
    notifyListeners();
  }

  Future<void> loadSupplierStatement() async {
    if (_selectedSupplier == null) {
      _error = 'يرجى اختيار مورد أولاً';
      notifyListeners();
      return;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _transactions.clear();
      _totalDebit = 0.0;
      _totalCredit = 0.0;
      _finalBalance = 0.0;

      // Get supplier purchases within date range
      final purchases = await _getSupplierPurchases();
      
      // Get supplier-related expenses within date range
      final expenses = await _getSupplierExpenses();
      
      // Convert purchases and expenses to transactions
      double runningBalance = 0.0;
      
      // Add purchase transactions (credit - we owe the supplier)
      for (final purchase in purchases) {
        final amount = purchase.totalAmount ?? 0.0;
        runningBalance += amount;
        
        _transactions.add(SupplierStatementTransaction(
          date: purchase.date is DateTime ? purchase.date as DateTime : DateTime.now(),
          description: 'فاتورة توريد #${purchase.id}',
          amount: amount,
          type: 'credit',
          balance: runningBalance,
          referenceId: purchase.id,
        ));
        
        _totalCredit += amount;
      }

      // Add expense transactions (debit - payments to supplier)
      for (final expense in expenses) {
        final amount = expense.amount ?? 0.0;
        runningBalance -= amount;
        
        _transactions.add(SupplierStatementTransaction(
          date: expense.expenseDate is DateTime ? expense.expenseDate as DateTime : DateTime.now(),
          description: 'دفعة للمورد - ${expense.description}',
          amount: amount,
          type: 'debit',
          balance: runningBalance,
          referenceId: expense.id,
        ));
        
        _totalDebit += amount;
      }

      _finalBalance = runningBalance;

      // Sort transactions by date
      _transactions.sort((a, b) => a.date.compareTo(b.date));

      // Recalculate running balance after sorting
      double balance = 0.0;
      for (int i = 0; i < _transactions.length; i++) {
        if (_transactions[i].type == 'credit') {
          balance += _transactions[i].amount;
        } else {
          balance -= _transactions[i].amount;
        }
        _transactions[i] = SupplierStatementTransaction(
          date: _transactions[i].date,
          description: _transactions[i].description,
          amount: _transactions[i].amount,
          type: _transactions[i].type,
          balance: balance,
          referenceId: _transactions[i].referenceId,
        );
      }

    } catch (e) {
      _error = 'حدث خطأ في تحميل كشف الحساب: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<Purchase>> _getSupplierPurchases() async {
    try {
      final db = await DatabaseService.instance.database;
      
      String query = '''
        SELECT * FROM purchases 
        WHERE supplierId = ?
      ''';
      
      List<dynamic> params = [_selectedSupplier!.id];
      
      if (_startDate != null && _endDate != null) {
        query += ' AND date BETWEEN ? AND ?';
        params.addAll([
          _startDate!.toIso8601String(),
          _endDate!.toIso8601String(),
        ]);
      }
      
      query += ' ORDER BY date ASC';
      
      final result = await db.rawQuery(query, params);
      
      return result.map((map) => Purchase.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في جلب مشتريات المورد: $e');
    }
  }

  Future<List<Expense>> _getSupplierExpenses() async {
    try {
      final db = await DatabaseService.instance.database;
      
      String query = '''
        SELECT * FROM expenses 
        WHERE description LIKE ? OR notes LIKE ?
      ''';
      
      List<dynamic> params = [
        '%${_selectedSupplier!.name}%',
        '%${_selectedSupplier!.name}%'
      ];
      
      if (_startDate != null && _endDate != null) {
        query += ' AND expenseDate BETWEEN ? AND ?';
        params.addAll([
          _startDate!.toIso8601String(),
          _endDate!.toIso8601String(),
        ]);
      }
      
      query += ' ORDER BY expenseDate ASC';
      
      final result = await db.rawQuery(query, params);
      
      return result.map((map) => Expense.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في جلب مصروفات المورد: $e');
    }
  }

  void filterByPeriod(String period) {
    final now = DateTime.now();
    DateTime? start;
    DateTime? end = now;

    switch (period) {
      case 'اليوم':
        start = DateTime(now.year, now.month, now.day);
        break;
      case 'الأسبوع':
        start = now.subtract(Duration(days: now.weekday - 1));
        start = DateTime(start.year, start.month, start.day);
        break;
      case 'الشهر':
        start = DateTime(now.year, now.month, 1);
        break;
      case 'السنة':
        start = DateTime(now.year, 1, 1);
        break;
      default:
        start = null;
        end = null;
    }

    setDateRange(start, end);
  }

  // Export functionality
  String generateStatementText() {
    if (_selectedSupplier == null) return '';

    final buffer = StringBuffer();
    buffer.writeln('كشف حساب المورد');
    buffer.writeln('================');
    buffer.writeln('اسم المورد: ${_selectedSupplier!.name}');
    buffer.writeln('رقم الهاتف: ${_selectedSupplier!.phone ?? 'غير محدد'}');
    
    if (_startDate != null && _endDate != null) {
      buffer.writeln('الفترة: من ${_startDate!.day}/${_startDate!.month}/${_startDate!.year} إلى ${_endDate!.day}/${_endDate!.month}/${_endDate!.year}');
    }
    
    buffer.writeln('');
    buffer.writeln('الحركات:');
    buffer.writeln('--------');
    
    for (final transaction in _transactions) {
      buffer.writeln('${transaction.formattedDate} - ${transaction.description} - ${transaction.formattedAmount} (${transaction.typeLabel})');
    }
    
    buffer.writeln('');
    buffer.writeln('الملخص:');
    buffer.writeln('-------');
    buffer.writeln('إجمالي المدين: $formattedTotalDebit');
    buffer.writeln('إجمالي الدائن: $formattedTotalCredit');
    buffer.writeln('الرصيد النهائي: $formattedFinalBalance');
    
    return buffer.toString();
  }
}
