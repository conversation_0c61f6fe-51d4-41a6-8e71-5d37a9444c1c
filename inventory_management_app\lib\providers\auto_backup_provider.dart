import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

/// تردد النسخ الاحتياطي التلقائي
enum BackupFrequency {
  daily('يومي'),
  weekly('أسبوعي'),
  monthly('شهري');

  const BackupFrequency(this.displayName);
  final String displayName;
}

/// مزود النسخ الاحتياطي التلقائي
class AutoBackupProvider extends ChangeNotifier {
  // إعدادات النسخ الاحتياطي التلقائي
  bool _autoBackupEnabled = false;
  BackupFrequency _autoBackupFrequency = BackupFrequency.weekly;
  bool _autoBackupToCloud = false;

  // Getters
  bool get autoBackupEnabled => _autoBackupEnabled;
  BackupFrequency get autoBackupFrequency => _autoBackupFrequency;
  bool get autoBackupToCloud => _autoBackupToCloud;

  /// تهيئة المزود
  Future<void> initialize() async {
    await _loadSettings();
    notifyListeners();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      _autoBackupEnabled = prefs.getBool('auto_backup_enabled') ?? false;
      _autoBackupToCloud = prefs.getBool('auto_backup_to_cloud') ?? false;

      final String frequencyString = prefs.getString('auto_backup_frequency') ?? 'weekly';
      _autoBackupFrequency = BackupFrequency.values.firstWhere(
        (freq) => freq.name == frequencyString,
        orElse: () => BackupFrequency.weekly,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات النسخ الاحتياطي: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_backup_enabled', _autoBackupEnabled);
      await prefs.setBool('auto_backup_to_cloud', _autoBackupToCloud);
      await prefs.setString('auto_backup_frequency', _autoBackupFrequency.name);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات النسخ الاحتياطي: $e');
    }
  }

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupEnabled(bool enabled) async {
    _autoBackupEnabled = enabled;
    await _saveSettings();

    if (enabled) {
      await _scheduleAutoBackup();
    } else {
      await _cancelAutoBackup();
    }

    notifyListeners();
  }

  /// تغيير تردد النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupFrequency(BackupFrequency frequency) async {
    _autoBackupFrequency = frequency;
    await _saveSettings();

    if (_autoBackupEnabled) {
      await _scheduleAutoBackup();
    }

    notifyListeners();
  }

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي للسحابة
  Future<void> setAutoBackupToCloud(bool enabled) async {
    _autoBackupToCloud = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// جدولة النسخ الاحتياطي التلقائي
  Future<void> _scheduleAutoBackup() async {
    try {
      // إلغاء الجدولة السابقة
      await Workmanager().cancelByUniqueName('auto_backup');

      // تحديد التردد بالدقائق
      Duration frequency;
      switch (_autoBackupFrequency) {
        case BackupFrequency.daily:
          frequency = const Duration(days: 1);
          break;
        case BackupFrequency.weekly:
          frequency = const Duration(days: 7);
          break;
        case BackupFrequency.monthly:
          frequency = const Duration(days: 30);
          break;
      }

      // جدولة المهمة
      await Workmanager().registerPeriodicTask(
        'auto_backup',
        'autoBackupTask',
        frequency: frequency,
        initialDelay: const Duration(minutes: 5),
        inputData: {
          'backup_to_cloud': _autoBackupToCloud,
        },
      );

      debugPrint('تم جدولة النسخ الاحتياطي التلقائي: ${_autoBackupFrequency.displayName}');
    } catch (e) {
      debugPrint('خطأ في جدولة النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// إلغاء النسخ الاحتياطي التلقائي
  Future<void> _cancelAutoBackup() async {
    try {
      await Workmanager().cancelByUniqueName('auto_backup');
      debugPrint('تم إلغاء النسخ الاحتياطي التلقائي');
    } catch (e) {
      debugPrint('خطأ في إلغاء النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// الحصول على وصف الحالة الحالية
  String get statusDescription {
    if (!_autoBackupEnabled) {
      return 'النسخ الاحتياطي التلقائي معطل';
    }

    String description = 'النسخ الاحتياطي التلقائي مفعل - ${_autoBackupFrequency.displayName}';
    
    if (_autoBackupToCloud) {
      description += ' (مع النسخ السحابي)';
    } else {
      description += ' (محلي فقط)';
    }

    return description;
  }

  /// التحقق من إمكانية تفعيل النسخ السحابي
  bool canEnableCloudBackup(bool isSignedIn) {
    return isSignedIn;
  }

  /// الحصول على معلومات التردد
  String getFrequencyDescription(BackupFrequency frequency) {
    switch (frequency) {
      case BackupFrequency.daily:
        return 'نسخة احتياطية كل يوم';
      case BackupFrequency.weekly:
        return 'نسخة احتياطية كل أسبوع';
      case BackupFrequency.monthly:
        return 'نسخة احتياطية كل شهر';
    }
  }

  /// إعادة تعيين الإعدادات للقيم الافتراضية
  Future<void> resetToDefaults() async {
    _autoBackupEnabled = false;
    _autoBackupFrequency = BackupFrequency.weekly;
    _autoBackupToCloud = false;
    
    await _saveSettings();
    await _cancelAutoBackup();
    
    notifyListeners();
  }

  /// التحقق من صحة الإعدادات
  bool validateSettings() {
    // التحقق من أن التردد صحيح
    if (!BackupFrequency.values.contains(_autoBackupFrequency)) {
      return false;
    }

    return true;
  }

  /// الحصول على معلومات مفصلة عن الإعدادات
  Map<String, dynamic> getSettingsInfo() {
    return {
      'enabled': _autoBackupEnabled,
      'frequency': _autoBackupFrequency.name,
      'frequencyDisplay': _autoBackupFrequency.displayName,
      'cloudBackup': _autoBackupToCloud,
      'status': statusDescription,
    };
  }
}
