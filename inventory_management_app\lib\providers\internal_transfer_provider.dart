import 'package:flutter/material.dart';
import '../models/internal_transfer.dart';
import '../services/database_service.dart';
import '../providers/product_provider.dart';

/// Provider class for managing internal transfer state and operations
class InternalTransferProvider extends ChangeNotifier {
  List<InternalTransfer> _transfers = <InternalTransfer>[];
  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of internal transfers
  List<InternalTransfer> get transfers => _transfers;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load transfers from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة InternalTransferProvider...');
      await loadInternalTransfers();
      debugPrint('✅ تم تهيئة InternalTransferProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة InternalTransferProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل التحويلات الداخلية: $e';
      notifyListeners();
    }
  }

  /// Load all internal transfers from database
  Future<void> loadInternalTransfers() async {
    _setLoading(true);
    _clearError();

    try {
      final List<Map<String, dynamic>> transferMaps = await _databaseService.getInternalTransfers();
      _transfers = transferMaps.map((map) => InternalTransfer.fromMap(map)).toList();
      notifyListeners();
      debugPrint('📦 تم تحميل ${_transfers.length} تحويل داخلي');
    } catch (e) {
      _setError('فشل في تحميل التحويلات الداخلية: $e');
      debugPrint('❌ خطأ في تحميل التحويلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new internal transfer
  Future<bool> addInternalTransfer(
    InternalTransfer transfer, 
    ProductProvider productProvider
  ) async {
    _setLoading(true);
    _clearError();

    try {
      // First, check if the product has sufficient warehouse quantity
      if (!productProvider.canTransferFromWarehouse(transfer.productId, transfer.transferredQuantity)) {
        throw Exception('الكمية المطلوبة أكبر من المتوفر في المخزن');
      }

      // Perform the warehouse to store transfer
      final bool transferSuccess = await productProvider.transferWarehouseToStore(
        transfer.productId, 
        transfer.transferredQuantity
      );

      if (!transferSuccess) {
        throw Exception('فشل في تحديث كميات المنتج');
      }

      // Save the transfer record to database
      final int transferId = await _databaseService.insertInternalTransfer(transfer.toMap());
      
      // Create the transfer with the new ID
      final InternalTransfer savedTransfer = transfer.copyWith(id: transferId);
      
      // Add to local list
      _transfers.insert(0, savedTransfer); // Insert at beginning for newest first
      notifyListeners();

      debugPrint('✅ تم إضافة التحويل الداخلي بنجاح - ID: $transferId');
      return true;

    } catch (e) {
      _setError('فشل في إضافة التحويل الداخلي: $e');
      debugPrint('❌ خطأ في إضافة التحويل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing internal transfer
  Future<bool> updateInternalTransfer(InternalTransfer transfer) async {
    _setLoading(true);
    _clearError();

    try {
      // Update in database
      await _databaseService.updateInternalTransfer(transfer.toMap());

      // Update in local list
      final int index = _transfers.indexWhere((t) => t.id == transfer.id);
      if (index != -1) {
        _transfers[index] = transfer;
        notifyListeners();
      }

      debugPrint('✅ تم تحديث التحويل الداخلي بنجاح');
      return true;

    } catch (e) {
      _setError('فشل في تحديث التحويل الداخلي: $e');
      debugPrint('❌ خطأ في تحديث التحويل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an internal transfer
  /// Note: This does NOT reverse the inventory changes
  Future<bool> deleteInternalTransfer(int id) async {
    _setLoading(true);
    _clearError();

    try {
      // Delete from database
      await _databaseService.deleteInternalTransfer(id);

      // Remove from local list
      _transfers.removeWhere((transfer) => transfer.id == id);
      notifyListeners();

      debugPrint('✅ تم حذف التحويل الداخلي بنجاح');
      return true;

    } catch (e) {
      _setError('فشل في حذف التحويل الداخلي: $e');
      debugPrint('❌ خطأ في حذف التحويل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get transfers by date range
  Future<void> getTransfersByDateRange(DateTime startDate, DateTime endDate) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Map<String, dynamic>> transferMaps = await _databaseService
          .getInternalTransfersByDateRange(startDate, endDate);
      _transfers = transferMaps.map((map) => InternalTransfer.fromMap(map)).toList();
      notifyListeners();
      debugPrint('📦 تم تحميل ${_transfers.length} تحويل للفترة المحددة');
    } catch (e) {
      _setError('فشل في تحميل التحويلات للفترة المحددة: $e');
      debugPrint('❌ خطأ في تحميل التحويلات بالتاريخ: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get transfers by product
  Future<void> getTransfersByProduct(int productId) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Map<String, dynamic>> transferMaps = await _databaseService
          .getInternalTransfersByProduct(productId);
      _transfers = transferMaps.map((map) => InternalTransfer.fromMap(map)).toList();
      notifyListeners();
      debugPrint('📦 تم تحميل ${_transfers.length} تحويل للمنتج $productId');
    } catch (e) {
      _setError('فشل في تحميل تحويلات المنتج: $e');
      debugPrint('❌ خطأ في تحميل تحويلات المنتج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get total transferred quantity for a product
  Future<int> getTotalTransferredQuantity(int productId) async {
    try {
      return await _databaseService.getTotalTransferredQuantity(productId);
    } catch (e) {
      _setError('فشل في حساب إجمالي الكمية المحولة: $e');
      return 0;
    }
  }

  /// Get transfers count
  Future<int> getTransfersCount() async {
    try {
      return await _databaseService.getInternalTransfersCount();
    } catch (e) {
      _setError('فشل في حساب عدد التحويلات: $e');
      return 0;
    }
  }

  /// Clear all transfers (useful for filtering)
  void clearTransfers() {
    _transfers.clear();
    notifyListeners();
  }

  /// Reset to show all transfers
  Future<void> resetTransfers() async {
    await loadInternalTransfers();
  }

  /// Get transfers for today
  Future<void> getTodayTransfers() async {
    final DateTime today = DateTime.now();
    final DateTime startOfDay = DateTime(today.year, today.month, today.day);
    final DateTime endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
    
    await getTransfersByDateRange(startOfDay, endOfDay);
  }

  /// Get transfers for this week
  Future<void> getWeekTransfers() async {
    final DateTime now = DateTime.now();
    final DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final DateTime endOfWeek = startOfWeek.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
    
    await getTransfersByDateRange(startOfWeek, endOfWeek);
  }

  /// Get transfers for this month
  Future<void> getMonthTransfers() async {
    final DateTime now = DateTime.now();
    final DateTime startOfMonth = DateTime(now.year, now.month, 1);
    final DateTime endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    
    await getTransfersByDateRange(startOfMonth, endOfMonth);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
