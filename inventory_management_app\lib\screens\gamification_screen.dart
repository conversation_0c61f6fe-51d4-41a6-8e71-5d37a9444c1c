import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/gamification_service.dart';

class GamificationScreen extends StatefulWidget {
  const GamificationScreen({super.key});

  @override
  State<GamificationScreen> createState() => _GamificationScreenState();
}

class _GamificationScreenState extends State<GamificationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('الإنجازات والتحديات'),
          backgroundColor: Colors.deepPurple,
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            tabs: const [
              Tab(icon: Icon(Icons.emoji_events), text: 'الإحصائيات'),
              Tab(icon: Icon(Icons.military_tech), text: 'الشارات'),
              Tab(icon: Icon(Icons.flag), text: 'الإنجازات'),
            ],
          ),
        ),
        body: Consumer<GamificationProvider>(
          builder: (context, gamificationProvider, child) {
            if (gamificationProvider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return TabBarView(
              controller: _tabController,
              children: [
                _buildStatsTab(gamificationProvider),
                _buildBadgesTab(gamificationProvider),
                _buildAchievementsTab(gamificationProvider),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => _showTestActions(context),
          backgroundColor: Colors.deepPurple,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text('اختبار النقاط', style: TextStyle(color: Colors.white)),
        ),
      ),
    );
  }

  Widget _buildStatsTab(GamificationProvider provider) {
    final levelProgress = provider.levelProgress;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // بطاقة المستوى والنقاط
          Card(
            elevation: 4,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [Colors.deepPurple, Colors.purple.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المستوى ${provider.level}',
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '${provider.points} نقطة',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: const Icon(
                          Icons.emoji_events,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // شريط التقدم
                  if (levelProgress.isNotEmpty) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'التقدم نحو المستوى ${levelProgress['next_level']}',
                          style: const TextStyle(color: Colors.white),
                        ),
                        Text(
                          '${levelProgress['progress_points']}/${levelProgress['total_points_needed']}',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: levelProgress['progress_percentage'] / 100,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'تحتاج ${levelProgress['points_to_next_level']} نقطة للمستوى التالي',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // إحصائيات سريعة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'السلسلة اليومية',
                  '${provider.dailyStreak} يوم',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الشارات',
                  '${provider.badges.length}',
                  Icons.military_tech,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // نصائح لكسب النقاط
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.lightbulb, color: Colors.amber),
                      const SizedBox(width: 8),
                      const Text(
                        'طرق كسب النقاط',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildTipItem('إضافة منتج جديد', '100 نقطة', Icons.add_box),
                  _buildTipItem('إضافة عميل جديد', '75 نقطة', Icons.person_add),
                  _buildTipItem('إتمام عملية بيع', '1 نقطة لكل 10 ريال', Icons.point_of_sale),
                  _buildTipItem('تسجيل دخول يومي', '50 نقطة', Icons.login),
                  _buildTipItem('إنشاء نسخة احتياطية', '200 نقطة', Icons.backup),
                  _buildTipItem('استخدام ميزة متقدمة', '25 نقطة', Icons.star),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBadgesTab(GamificationProvider provider) {
    final availableBadges = GamificationService.instance.getAvailableBadges();
    final userBadges = provider.badges;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
        ),
        itemCount: availableBadges.length,
        itemBuilder: (context, index) {
          final badgeId = availableBadges.keys.elementAt(index);
          final badge = availableBadges[badgeId]!;
          final isUnlocked = userBadges.contains(badgeId);

          return Card(
            elevation: isUnlocked ? 4 : 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: isUnlocked ? Colors.amber.shade50 : Colors.grey.shade100,
                border: isUnlocked 
                    ? Border.all(color: Colors.amber, width: 2)
                    : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    badge['icon']!,
                    style: TextStyle(
                      fontSize: 40,
                      color: isUnlocked ? null : Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    badge['name']!,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isUnlocked ? Colors.amber.shade800 : Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    badge['description']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: isUnlocked ? Colors.grey.shade700 : Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (isUnlocked) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'مفتوح',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAchievementsTab(GamificationProvider provider) {
    final availableAchievements = GamificationService.instance.getAvailableAchievements();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: availableAchievements.length,
      itemBuilder: (context, index) {
        final achievementId = availableAchievements.keys.elementAt(index);
        final achievement = availableAchievements[achievementId]!;
        final isCompleted = false; // TODO: التحقق من الإنجازات المكتملة

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isCompleted ? Colors.green : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isCompleted ? Icons.check : Icons.flag,
                color: isCompleted ? Colors.white : Colors.grey.shade600,
              ),
            ),
            title: Text(
              achievement['name']!,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isCompleted ? Colors.green : null,
              ),
            ),
            subtitle: Text(achievement['description']!),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${achievement['points']} نقطة',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.amber,
                  ),
                ),
                if (isCompleted)
                  const Icon(Icons.emoji_events, color: Colors.amber, size: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String action, String points, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Expanded(child: Text(action)),
          Text(
            points,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  void _showTestActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'اختبار النقاط والشارات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.add_box),
              title: const Text('محاكاة إضافة منتج'),
              onTap: () {
                context.read<GamificationProvider>().onProductAdded();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('محاكاة إضافة عميل'),
              onTap: () {
                context.read<GamificationProvider>().onCustomerAdded();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.point_of_sale),
              title: const Text('محاكاة بيع (500 ريال)'),
              onTap: () {
                context.read<GamificationProvider>().onSaleCompleted(500);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('محاكاة نسخة احتياطية'),
              onTap: () {
                context.read<GamificationProvider>().onBackupCreated();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('إعادة تعيين البيانات'),
              onTap: () {
                context.read<GamificationProvider>().resetAllData();
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
