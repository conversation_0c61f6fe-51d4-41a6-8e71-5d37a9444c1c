import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/screens/sales/sale_details_screen.dart';

class SalesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final SaleProvider saleProvider = Provider.of<SaleProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Sales'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: Implement navigation to add sale screen
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: saleProvider.sales.length,
        itemBuilder: (BuildContext context, int index) {
          final Sale sale = saleProvider.sales[index];
          return Card(
            child: ListTile(
              title: Text('Sale ID: ${sale.id}'),
              subtitle: Text('Customer ID: ${sale.customerId}'),
              trailing: const Icon(Icons.arrow_forward),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) => SaleDetailsScreen(sale: sale),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
