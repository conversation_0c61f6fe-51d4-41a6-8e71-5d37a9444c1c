import 'package:flutter/material.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../services/database_service.dart';

class CustomerStatementTransaction {
  final DateTime date;
  final String description;
  final double amount;
  final String type; // 'debit' or 'credit'
  final double balance;
  final int? referenceId;

  CustomerStatementTransaction({
    required this.date,
    required this.description,
    required this.amount,
    required this.type,
    required this.balance,
    this.referenceId,
  });

  String get formattedAmount {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color get amountColor {
    return type == 'debit' ? Colors.red : Colors.green;
  }

  String get typeLabel {
    return type == 'debit' ? 'مدين' : 'دائن';
  }
}

class CustomerStatementProvider extends ChangeNotifier {
  List<CustomerStatementTransaction> _transactions = [];
  Customer? _selectedCustomer;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;
  String? _error;
  double _totalDebit = 0.0;
  double _totalCredit = 0.0;
  double _finalBalance = 0.0;

  List<CustomerStatementTransaction> get transactions => _transactions;
  Customer? get selectedCustomer => _selectedCustomer;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get totalDebit => _totalDebit;
  double get totalCredit => _totalCredit;
  double get finalBalance => _finalBalance;

  String get formattedTotalDebit => '${_totalDebit.toStringAsFixed(2)} ر.س';
  String get formattedTotalCredit => '${_totalCredit.toStringAsFixed(2)} ر.س';
  String get formattedFinalBalance => '${_finalBalance.toStringAsFixed(2)} ر.س';

  void setCustomer(Customer customer) {
    _selectedCustomer = customer;
    notifyListeners();
  }

  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  void clearSelection() {
    _selectedCustomer = null;
    _startDate = null;
    _endDate = null;
    _transactions.clear();
    _totalDebit = 0.0;
    _totalCredit = 0.0;
    _finalBalance = 0.0;
    _error = null;
    notifyListeners();
  }

  Future<void> loadCustomerStatement() async {
    if (_selectedCustomer == null) {
      _error = 'يرجى اختيار عميل أولاً';
      notifyListeners();
      return;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _transactions.clear();
      _totalDebit = 0.0;
      _totalCredit = 0.0;
      _finalBalance = 0.0;

      // Get customer sales within date range
      final sales = await _getCustomerSales();
      
      // Convert sales to transactions
      double runningBalance = 0.0;
      
      for (final sale in sales) {
        final amount = sale.totalAmount ?? 0.0;
        runningBalance += amount;
        
        _transactions.add(CustomerStatementTransaction(
          date: sale.date is DateTime ? sale.date as DateTime : DateTime.now(),
          description: 'فاتورة بيع #${sale.id}',
          amount: amount,
          type: 'debit',
          balance: runningBalance,
          referenceId: sale.id,
        ));
        
        _totalDebit += amount;
      }

      // TODO: Add payment transactions when payment system is implemented
      // For now, we'll just show sales transactions

      _finalBalance = runningBalance;

      // Sort transactions by date
      _transactions.sort((a, b) => a.date.compareTo(b.date));

    } catch (e) {
      _error = 'حدث خطأ في تحميل كشف الحساب: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<Sale>> _getCustomerSales() async {
    try {
      final db = await DatabaseService.instance.database;
      
      String query = '''
        SELECT * FROM sales 
        WHERE customerId = ?
      ''';
      
      List<dynamic> params = [_selectedCustomer!.id];
      
      if (_startDate != null && _endDate != null) {
        query += ' AND date BETWEEN ? AND ?';
        params.addAll([
          _startDate!.toIso8601String(),
          _endDate!.toIso8601String(),
        ]);
      }
      
      query += ' ORDER BY date ASC';
      
      final result = await db.rawQuery(query, params);
      
      return result.map((map) => Sale.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في جلب مبيعات العميل: $e');
    }
  }

  void filterByPeriod(String period) {
    final now = DateTime.now();
    DateTime? start;
    DateTime? end = now;

    switch (period) {
      case 'اليوم':
        start = DateTime(now.year, now.month, now.day);
        break;
      case 'الأسبوع':
        start = now.subtract(Duration(days: now.weekday - 1));
        start = DateTime(start.year, start.month, start.day);
        break;
      case 'الشهر':
        start = DateTime(now.year, now.month, 1);
        break;
      case 'السنة':
        start = DateTime(now.year, 1, 1);
        break;
      default:
        start = null;
        end = null;
    }

    setDateRange(start, end);
  }

  // Export functionality
  String generateStatementText() {
    if (_selectedCustomer == null) return '';

    final buffer = StringBuffer();
    buffer.writeln('كشف حساب العميل');
    buffer.writeln('================');
    buffer.writeln('اسم العميل: ${_selectedCustomer!.name}');
    buffer.writeln('رقم الهاتف: ${_selectedCustomer!.phone ?? 'غير محدد'}');
    
    if (_startDate != null && _endDate != null) {
      buffer.writeln('الفترة: من ${_startDate!.day}/${_startDate!.month}/${_startDate!.year} إلى ${_endDate!.day}/${_endDate!.month}/${_endDate!.year}');
    }
    
    buffer.writeln('');
    buffer.writeln('الحركات:');
    buffer.writeln('--------');
    
    for (final transaction in _transactions) {
      buffer.writeln('${transaction.formattedDate} - ${transaction.description} - ${transaction.formattedAmount} (${transaction.typeLabel})');
    }
    
    buffer.writeln('');
    buffer.writeln('الملخص:');
    buffer.writeln('-------');
    buffer.writeln('إجمالي المدين: $formattedTotalDebit');
    buffer.writeln('إجمالي الدائن: $formattedTotalCredit');
    buffer.writeln('الرصيد النهائي: $formattedFinalBalance');
    
    return buffer.toString();
  }
}
