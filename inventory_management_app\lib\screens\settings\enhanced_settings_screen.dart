import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_constants.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/settings_helper.dart';
import '../../utils/snackbar_helper.dart';
import '../../providers/theme_provider.dart';

/// شاشة الإعدادات المحسنة
class EnhancedSettingsScreen extends StatefulWidget {
  const EnhancedSettingsScreen({super.key});

  @override
  State<EnhancedSettingsScreen> createState() => _EnhancedSettingsScreenState();
}

class _EnhancedSettingsScreenState extends State<EnhancedSettingsScreen> {
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _companyPhoneController = TextEditingController();
  final _companyEmailController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _commercialRegisterController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    _taxNumberController.dispose();
    _commercialRegisterController.dispose();
    super.dispose();
  }

  void _loadSettings() {
    _companyNameController.text = SettingsHelper.getCompanyName();
    _companyAddressController.text = SettingsHelper.getCompanyAddress();
    _companyPhoneController.text = SettingsHelper.getCompanyPhone();
    _companyEmailController.text = SettingsHelper.getCompanyEmail();
    _taxNumberController.text = SettingsHelper.getTaxNumber();
    _commercialRegisterController.text = SettingsHelper.getCommercialRegister();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('إعدادات المظهر'),
              _buildAppearanceSettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات الشركة'),
              _buildCompanySettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات الفواتير'),
              _buildInvoiceSettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات المخزون'),
              _buildInventorySettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات النسخ الاحتياطي'),
              _buildBackupSettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات الإشعارات'),
              _buildNotificationSettings(),
              
              const SizedBox(height: AppDimensions.paddingL),
              
              _buildSectionTitle('إعدادات التطبيق'),
              _buildAppSettings(),
              
              const SizedBox(height: AppDimensions.paddingXL),
              
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Text(
        title,
        style: AppStyles.titleLarge.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildAppearanceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return ListTile(
                  leading: const Icon(Icons.palette, color: AppColors.primary),
                  title: const Text('المظهر'),
                  subtitle: Text(_getThemeModeText(themeProvider.themeMode)),
                  trailing: DropdownButton<ThemeMode>(
                    value: themeProvider.themeMode,
                    onChanged: (ThemeMode? mode) {
                      if (mode != null) {
                        themeProvider.setThemeMode(mode);
                        SettingsHelper.setThemeMode(mode);
                      }
                    },
                    items: const [
                      DropdownMenuItem(
                        value: ThemeMode.system,
                        child: Text('تلقائي'),
                      ),
                      DropdownMenuItem(
                        value: ThemeMode.light,
                        child: Text('فاتح'),
                      ),
                      DropdownMenuItem(
                        value: ThemeMode.dark,
                        child: Text('داكن'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            TextFormField(
              controller: _companyNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة',
                prefixIcon: Icon(Icons.business),
              ),
              onChanged: (value) => SettingsHelper.setCompanyName(value),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _companyAddressController,
              decoration: const InputDecoration(
                labelText: 'عنوان الشركة',
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
              onChanged: (value) => SettingsHelper.setCompanyAddress(value),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _companyPhoneController,
              decoration: const InputDecoration(
                labelText: 'هاتف الشركة',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              onChanged: (value) => SettingsHelper.setCompanyPhone(value),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _companyEmailController,
              decoration: const InputDecoration(
                labelText: 'بريد الشركة الإلكتروني',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) => SettingsHelper.setCompanyEmail(value),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _taxNumberController,
              decoration: const InputDecoration(
                labelText: 'الرقم الضريبي',
                prefixIcon: Icon(Icons.receipt),
              ),
              onChanged: (value) => SettingsHelper.setTaxNumber(value),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _commercialRegisterController,
              decoration: const InputDecoration(
                labelText: 'السجل التجاري',
                prefixIcon: Icon(Icons.assignment),
              ),
              onChanged: (value) => SettingsHelper.setCommercialRegister(value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('طباعة تلقائية بعد البيع'),
              subtitle: const Text('طباعة الفاتورة تلقائياً بعد إتمام البيع'),
              value: SettingsHelper.getPrintAfterSale(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setPrintAfterSale(value);
                });
              },
              secondary: const Icon(Icons.print, color: AppColors.primary),
            ),
            SwitchListTile(
              title: const Text('إظهار العميل في الفاتورة'),
              subtitle: const Text('إظهار معلومات العميل في الفاتورة المطبوعة'),
              value: SettingsHelper.getShowCustomerInInvoice(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setShowCustomerInInvoice(value);
                });
              },
              secondary: const Icon(Icons.person, color: AppColors.primary),
            ),
            SwitchListTile(
              title: const Text('تقريب الأسعار'),
              subtitle: const Text('تقريب الأسعار لأقرب رقم صحيح'),
              value: SettingsHelper.getRoundPrices(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setRoundPrices(value);
                });
              },
              secondary: const Icon(Icons.calculate, color: AppColors.primary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.warning, color: AppColors.warning),
              title: const Text('حد التنبيه للمخزون المنخفض'),
              subtitle: Text('${SettingsHelper.getLowStockThreshold()} قطعة'),
              trailing: IconButton(
                icon: const Icon(Icons.edit),
                onPressed: _showLowStockThresholdDialog,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخة احتياطية تلقائياً'),
              value: SettingsHelper.getAutoBackup(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setAutoBackup(value);
                });
              },
              secondary: const Icon(Icons.backup, color: AppColors.primary),
            ),
            if (SettingsHelper.getAutoBackup())
              ListTile(
                leading: const Icon(Icons.schedule, color: AppColors.primary),
                title: const Text('تكرار النسخ الاحتياطي'),
                subtitle: Text(_getBackupFrequencyText(SettingsHelper.getBackupFrequency())),
                trailing: DropdownButton<String>(
                  value: SettingsHelper.getBackupFrequency(),
                  onChanged: (String? frequency) {
                    if (frequency != null) {
                      setState(() {
                        SettingsHelper.setBackupFrequency(frequency);
                      });
                    }
                  },
                  items: const [
                    DropdownMenuItem(value: 'daily', child: Text('يومياً')),
                    DropdownMenuItem(value: 'weekly', child: Text('أسبوعياً')),
                    DropdownMenuItem(value: 'monthly', child: Text('شهرياً')),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('الإشعارات'),
              subtitle: const Text('تفعيل الإشعارات'),
              value: SettingsHelper.getShowNotifications(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setShowNotifications(value);
                });
              },
              secondary: const Icon(Icons.notifications, color: AppColors.primary),
            ),
            SwitchListTile(
              title: const Text('الأصوات'),
              subtitle: const Text('تفعيل أصوات الإشعارات'),
              value: SettingsHelper.getSoundEnabled(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setSoundEnabled(value);
                });
              },
              secondary: const Icon(Icons.volume_up, color: AppColors.primary),
            ),
            SwitchListTile(
              title: const Text('الاهتزاز'),
              subtitle: const Text('تفعيل اهتزاز الإشعارات'),
              value: SettingsHelper.getVibrationEnabled(),
              onChanged: (value) {
                setState(() {
                  SettingsHelper.setVibrationEnabled(value);
                });
              },
              secondary: const Icon(Icons.vibration, color: AppColors.primary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.info, color: AppColors.info),
              title: const Text('إصدار التطبيق'),
              subtitle: Text(AppConstants.appVersion),
            ),
            ListTile(
              leading: const Icon(Icons.help, color: AppColors.info),
              title: const Text('المساعدة والدعم'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: فتح شاشة المساعدة
              },
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip, color: AppColors.info),
              title: const Text('سياسة الخصوصية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: فتح سياسة الخصوصية
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        CustomPrimaryButton(
          text: 'حفظ الإعدادات',
          onPressed: _saveSettings,
          icon: Icons.save,
          isFullWidth: true,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        CustomSecondaryButton(
          text: 'إعادة تعيين الإعدادات',
          onPressed: _resetSettings,
          icon: Icons.restore,
          isFullWidth: true,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        CustomDangerButton(
          text: 'حذف جميع البيانات',
          onPressed: _deleteAllData,
          icon: Icons.delete_forever,
          isFullWidth: true,
        ),
      ],
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      default:
        return 'تلقائي';
    }
  }

  String _getBackupFrequencyText(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'يومياً';
      case 'weekly':
        return 'أسبوعياً';
      case 'monthly':
        return 'شهرياً';
      default:
        return 'أسبوعياً';
    }
  }

  void _showLowStockThresholdDialog() {
    final controller = TextEditingController(
      text: SettingsHelper.getLowStockThreshold().toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حد التنبيه للمخزون المنخفض'),
        content: TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'الكمية',
            suffixText: 'قطعة',
          ),
          keyboardType: TextInputType.number,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final value = int.tryParse(controller.text);
              if (value != null && value > 0) {
                SettingsHelper.setLowStockThreshold(value);
                setState(() {});
                Navigator.pop(context);
                SnackBarHelper.showSuccess(context, 'تم تحديث حد التنبيه');
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    SnackBarHelper.showSuccess(context, 'تم حفظ الإعدادات بنجاح');
  }

  void _resetSettings() async {
    final confirmed = await EnhancedConfirmationDialog.show(
      context,
      title: 'إعادة تعيين الإعدادات',
      message: 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
      icon: Icons.restore,
    );

    if (confirmed == true) {
      await SettingsHelper.resetToDefaults();
      _loadSettings();
      setState(() {});
      SnackBarHelper.showSuccess(context, 'تم إعادة تعيين الإعدادات');
    }
  }

  void _deleteAllData() async {
    final confirmed = await EnhancedConfirmationDialog.show(
      context,
      title: 'حذف جميع البيانات',
      message: 'هل أنت متأكد من حذف جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.',
      icon: Icons.delete_forever,
      isDanger: true,
    );

    if (confirmed == true) {
      // TODO: تنفيذ حذف جميع البيانات
      SnackBarHelper.showSuccess(context, 'تم حذف جميع البيانات');
    }
  }
}
