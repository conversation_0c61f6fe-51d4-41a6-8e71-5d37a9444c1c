1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.inventory_management_app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:15:5-67
15-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:15:22-64
16    <!-- أذونات الوصول لجهات الاتصال -->
17    <uses-permission android:name="android.permission.READ_CONTACTS" />
17-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:3:5-72
17-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:3:22-69
18    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
18-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:4:5-73
18-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:4:22-70
19    <uses-permission android:name="android.permission.GET_ACCOUNTS" /> <!-- أذونات الوصول للملفات (للاستيراد من إكسل) -->
19-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:5:5-71
19-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:5:22-68
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:8:5-80
20-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:8:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:9:5-81
21-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:9:22-78
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:10:5-76
22-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:10:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:11:5-75
23-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:11:22-72
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:12:5-75
24-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:12:22-72
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:16:5-79
25-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:16:22-76
26    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
26-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:17:5-76
26-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:17:22-73
27    <!--
28 Required to query activities that can process text, see:
29         https://developer.android.com/training/package-visibility and
30         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
31
32         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
33    -->
34    <queries>
34-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:56:5-61:15
35        <intent>
35-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:57:9-60:18
36            <action android:name="android.intent.action.PROCESS_TEXT" />
36-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:58:13-72
36-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:58:21-70
37
38            <data android:mimeType="text/plain" />
38-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:59:13-50
38-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:59:19-48
39        </intent>
40        <intent>
40-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
41            <action android:name="android.intent.action.GET_CONTENT" />
41-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
41-->[:file_picker] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
42
43            <data android:mimeType="*/*" />
43-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:59:13-50
43-->C:\Users\<USER>\Downloads\new invent\inventory_management_app\android\app\src\main\AndroidManifest.xml:59:19-48
44        </intent>
45    </queries>
46
47    <uses-permission android:name="android.permission.WAKE_LOCK" />
47-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
47-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:22-65
48    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
48-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
48-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
49    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
49-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
49-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
50
51    <permission
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
52        android:name="com.example.inventory_management_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.example.inventory_management_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
56
57    <application
58        android:name="android.app.Application"
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
60        android:debuggable="true"
61        android:extractNativeLibs="true"
62        android:icon="@mipmap/ic_launcher"
63        android:label="inventory_management_app" >
64        <activity
65            android:name="com.example.inventory_management_app.MainActivity"
66            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
67            android:exported="true"
68            android:hardwareAccelerated="true"
69            android:launchMode="singleTop"
70            android:taskAffinity=""
71            android:theme="@style/LaunchTheme"
72            android:windowSoftInputMode="adjustResize" >
73
74            <!--
75                 Specifies an Android theme to apply to this Activity as soon as
76                 the Android process has started. This theme is visible to the user
77                 while the Flutter UI initializes. After that, this theme continues
78                 to determine the Window background behind the Flutter UI.
79            -->
80            <meta-data
81                android:name="io.flutter.embedding.android.NormalTheme"
82                android:resource="@style/NormalTheme" />
83
84            <intent-filter>
85                <action android:name="android.intent.action.MAIN" />
86
87                <category android:name="android.intent.category.LAUNCHER" />
88            </intent-filter>
89        </activity>
90        <!--
91             Don't delete the meta-data below.
92             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
93        -->
94        <meta-data
95            android:name="flutterEmbedding"
96            android:value="2" />
97
98        <provider
98-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
99            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
99-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
100            android:authorities="com.example.inventory_management_app.flutter.image_provider"
100-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
101            android:exported="false"
101-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
102            android:grantUriPermissions="true" >
102-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
103            <meta-data
103-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
104                android:name="android.support.FILE_PROVIDER_PATHS"
104-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
105                android:resource="@xml/flutter_image_picker_file_paths" />
105-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
106        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
107        <service
107-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
108            android:name="com.google.android.gms.metadata.ModuleDependencies"
108-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
109            android:enabled="false"
109-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
110            android:exported="false" >
110-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
111            <intent-filter>
111-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
112                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
112-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
112-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
113            </intent-filter>
114
115            <meta-data
115-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
116                android:name="photopicker_activity:0:required"
116-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
117                android:value="" />
117-->[:image_picker_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
118        </service>
119
120        <activity
120-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
121            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
121-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
122            android:exported="false"
122-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
123            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
123-->[:url_launcher_android] C:\Users\<USER>\Downloads\new invent\inventory_management_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
124        <activity
124-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
125            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
125-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
126            android:excludeFromRecents="true"
126-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
127            android:exported="false"
127-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
128            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
128-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
129        <!--
130            Service handling Google Sign-In user revocation. For apps that do not integrate with
131            Google Sign-In, this service will never be started.
132        -->
133        <service
133-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
134            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
134-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
135            android:exported="true"
135-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
136            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
136-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
137            android:visibleToInstantApps="true" />
137-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
138
139        <activity
139-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
140            android:name="com.google.android.gms.common.api.GoogleApiActivity"
140-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
141            android:exported="false"
141-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
142-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
143
144        <meta-data
144-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
145            android:name="com.google.android.gms.version"
145-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
146            android:value="@integer/google_play_services_version" />
146-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
147
148        <provider
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
149            android:name="androidx.startup.InitializationProvider"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
150            android:authorities="com.example.inventory_management_app.androidx-startup"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
151            android:exported="false" >
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
152            <meta-data
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
153                android:name="androidx.work.WorkManagerInitializer"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
154                android:value="androidx.startup" />
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
155            <meta-data
155-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
156-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
157                android:value="androidx.startup" />
157-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
158            <meta-data
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
159                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
160                android:value="androidx.startup" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
161        </provider>
162
163        <service
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
164            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
166            android:enabled="@bool/enable_system_alarm_service_default"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
167            android:exported="false" />
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
168        <service
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
169            android:name="androidx.work.impl.background.systemjob.SystemJobService"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
171            android:enabled="@bool/enable_system_job_service_default"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
172            android:exported="true"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
173            android:permission="android.permission.BIND_JOB_SERVICE" />
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
174        <service
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
175            android:name="androidx.work.impl.foreground.SystemForegroundService"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
177            android:enabled="@bool/enable_system_foreground_service_default"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
178            android:exported="false" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
179
180        <receiver
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
181            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
183            android:enabled="true"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
184            android:exported="false" />
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
185        <receiver
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
186            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
188            android:enabled="false"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
191                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
192                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
193            </intent-filter>
194        </receiver>
195        <receiver
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
196            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
197            android:directBootAware="false"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
198            android:enabled="false"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
199            android:exported="false" >
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
200            <intent-filter>
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
201                <action android:name="android.intent.action.BATTERY_OKAY" />
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
202                <action android:name="android.intent.action.BATTERY_LOW" />
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
206            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
208            android:enabled="false"
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
209            android:exported="false" >
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
210            <intent-filter>
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
211                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
212                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
213            </intent-filter>
214        </receiver>
215        <receiver
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
216            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
217            android:directBootAware="false"
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
218            android:enabled="false"
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
219            android:exported="false" >
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
220            <intent-filter>
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
221                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
222            </intent-filter>
223        </receiver>
224        <receiver
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
225            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
227            android:enabled="false"
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
228            android:exported="false" >
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
229            <intent-filter>
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
230                <action android:name="android.intent.action.BOOT_COMPLETED" />
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
231                <action android:name="android.intent.action.TIME_SET" />
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
232                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
233            </intent-filter>
234        </receiver>
235        <receiver
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
236            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
236-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
238            android:enabled="@bool/enable_system_alarm_service_default"
238-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
239            android:exported="false" >
239-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
240            <intent-filter>
240-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
241                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
242            </intent-filter>
243        </receiver>
244        <receiver
244-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
245            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
245-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
246            android:directBootAware="false"
246-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
247            android:enabled="true"
247-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
248            android:exported="true"
248-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
249            android:permission="android.permission.DUMP" >
249-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
250            <intent-filter>
250-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
251                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
252            </intent-filter>
253        </receiver>
254
255        <uses-library
255-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
256            android:name="androidx.window.extensions"
256-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
257            android:required="false" />
257-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
258        <uses-library
258-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
259            android:name="androidx.window.sidecar"
259-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
260            android:required="false" />
260-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
261
262        <receiver
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
263            android:name="androidx.profileinstaller.ProfileInstallReceiver"
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
264            android:directBootAware="false"
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
265            android:enabled="true"
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
266            android:exported="true"
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
267            android:permission="android.permission.DUMP" >
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
269                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
270            </intent-filter>
271            <intent-filter>
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
272                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
273            </intent-filter>
274            <intent-filter>
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
275                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
276            </intent-filter>
277            <intent-filter>
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
278                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
279            </intent-filter>
280        </receiver>
281
282        <service
282-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
283            android:name="androidx.room.MultiInstanceInvalidationService"
283-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
284            android:directBootAware="true"
284-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
285            android:exported="false" />
285-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
286    </application>
287
288</manifest>
