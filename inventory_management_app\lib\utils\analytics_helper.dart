import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../utils/settings_helper.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة مساعدة للتحليلات والإحصائيات
class AnalyticsHelper {
  static final Map<String, dynamic> _analyticsData = {};
  static bool _isEnabled = true;

  /// تهيئة التحليلات
  static Future<void> initialize() async {
    _isEnabled = SettingsHelper.getAnalytics();
    if (_isEnabled) {
      await _loadAnalyticsData();
    }
  }

  /// تسجيل حدث
  static Future<void> logEvent(String eventName, {Map<String, dynamic>? parameters}) async {
    if (!_isEnabled) return;
    
    try {
      final event = {
        'name': eventName,
        'timestamp': DateTime.now().toIso8601String(),
        'parameters': parameters ?? {},
      };
      
      _addEventToData(event);
      await _saveAnalyticsData();
      
      if (kDebugMode) {
        print('Analytics Event: $eventName - $parameters');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تسجيل الحدث: $e');
      }
    }
  }

  /// تسجيل عرض الشاشة
  static Future<void> logScreenView(String screenName) async {
    await logEvent('screen_view', parameters: {
      'screen_name': screenName,
    });
  }

  /// تسجيل إضافة منتج
  static Future<void> logProductAdded(Product product) async {
    await logEvent('product_added', parameters: {
      'product_id': product.id,
      'product_name': product.name,
      'category': product.category,
      'price': product.salePrice,
    });
  }

  /// تسجيل تعديل منتج
  static Future<void> logProductUpdated(Product product) async {
    await logEvent('product_updated', parameters: {
      'product_id': product.id,
      'product_name': product.name,
      'category': product.category,
    });
  }

  /// تسجيل حذف منتج
  static Future<void> logProductDeleted(int productId) async {
    await logEvent('product_deleted', parameters: {
      'product_id': productId,
    });
  }

  /// تسجيل إضافة عميل
  static Future<void> logCustomerAdded(Customer customer) async {
    await logEvent('customer_added', parameters: {
      'customer_id': customer.id,
      'customer_name': customer.name,
    });
  }

  /// تسجيل بيع جديد
  static Future<void> logSaleCreated(Sale sale) async {
    await logEvent('sale_created', parameters: {
      'sale_id': sale.id,
      'customer_id': sale.customerId,
      'total_amount': sale.total,
      'payment_method': sale.paymentMethod,
    });
  }

  /// تسجيل شراء جديد
  static Future<void> logPurchaseCreated(Purchase purchase) async {
    await logEvent('purchase_created', parameters: {
      'purchase_id': purchase.id,
      'supplier_id': purchase.supplierId,
      'total_amount': purchase.total,
    });
  }

  /// تسجيل البحث
  static Future<void> logSearch(String query, String category) async {
    await logEvent('search', parameters: {
      'search_query': query,
      'search_category': category,
    });
  }

  /// تسجيل التصدير
  static Future<void> logExport(String exportType, String format) async {
    await logEvent('export', parameters: {
      'export_type': exportType,
      'format': format,
    });
  }

  /// تسجيل النسخ الاحتياطي
  static Future<void> logBackup(String backupType, bool success) async {
    await logEvent('backup', parameters: {
      'backup_type': backupType,
      'success': success,
    });
  }

  /// تسجيل تغيير الإعدادات
  static Future<void> logSettingsChanged(String settingName, dynamic value) async {
    await logEvent('settings_changed', parameters: {
      'setting_name': settingName,
      'new_value': value.toString(),
    });
  }

  /// تسجيل خطأ
  static Future<void> logError(String error, {String? context}) async {
    await logEvent('error', parameters: {
      'error_message': error,
      'context': context,
    });
  }

  /// تسجيل الأداء
  static Future<void> logPerformance(String operation, Duration duration) async {
    await logEvent('performance', parameters: {
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
    });
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStatistics() {
    if (!_isEnabled) return {};
    
    final events = _analyticsData['events'] as List? ?? [];
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = today.subtract(Duration(days: now.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);
    
    final todayEvents = events.where((e) {
      final eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(today);
    }).toList();
    
    final weekEvents = events.where((e) {
      final eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(thisWeek);
    }).toList();
    
    final monthEvents = events.where((e) {
      final eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(thisMonth);
    }).toList();
    
    return {
      'total_events': events.length,
      'today_events': todayEvents.length,
      'week_events': weekEvents.length,
      'month_events': monthEvents.length,
      'most_used_features': _getMostUsedFeatures(events),
      'screen_views': _getScreenViews(events),
      'error_count': _getErrorCount(events),
    };
  }

  /// الحصول على إحصائيات المبيعات
  static Map<String, dynamic> getSalesAnalytics(List<Sale> sales) {
    if (sales.isEmpty) return {};
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = today.subtract(Duration(days: now.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);
    
    final todaySales = sales.where((s) => 
        s.date != null && s.date!.isAfter(today)).toList();
    final weekSales = sales.where((s) => 
        s.date != null && s.date!.isAfter(thisWeek)).toList();
    final monthSales = sales.where((s) => 
        s.date != null && s.date!.isAfter(thisMonth)).toList();
    
    final totalRevenue = sales.fold<double>(0, (sum, sale) => sum + (sale.total ?? 0));
    final todayRevenue = todaySales.fold<double>(0, (sum, sale) => sum + (sale.total ?? 0));
    final weekRevenue = weekSales.fold<double>(0, (sum, sale) => sum + (sale.total ?? 0));
    final monthRevenue = monthSales.fold<double>(0, (sum, sale) => sum + (sale.total ?? 0));
    
    return {
      'total_sales': sales.length,
      'today_sales': todaySales.length,
      'week_sales': weekSales.length,
      'month_sales': monthSales.length,
      'total_revenue': totalRevenue,
      'today_revenue': todayRevenue,
      'week_revenue': weekRevenue,
      'month_revenue': monthRevenue,
      'average_sale': sales.isNotEmpty ? totalRevenue / sales.length : 0,
      'payment_methods': _getPaymentMethodsAnalytics(sales),
      'sales_by_day': _getSalesByDay(sales),
    };
  }

  /// الحصول على إحصائيات المنتجات
  static Map<String, dynamic> getProductAnalytics(List<Product> products) {
    if (products.isEmpty) return {};
    
    final totalProducts = products.length;
    final lowStockProducts = products.where((p) => 
        (p.quantity ?? 0) <= (p.minLevel ?? 10)).length;
    final outOfStockProducts = products.where((p) => 
        (p.quantity ?? 0) <= 0).length;
    
    final totalValue = products.fold<double>(0, (sum, product) => 
        sum + ((product.quantity ?? 0) * (product.purchasePrice ?? 0)));
    
    final categories = <String, int>{};
    for (final product in products) {
      final category = product.category ?? 'غير محدد';
      categories[category] = (categories[category] ?? 0) + 1;
    }
    
    return {
      'total_products': totalProducts,
      'low_stock_products': lowStockProducts,
      'out_of_stock_products': outOfStockProducts,
      'total_inventory_value': totalValue,
      'categories_distribution': categories,
      'average_price': products.isNotEmpty 
          ? products.fold<double>(0, (sum, p) => sum + (p.salePrice ?? 0)) / products.length
          : 0,
    };
  }

  /// الحصول على إحصائيات العملاء
  static Map<String, dynamic> getCustomerAnalytics(List<Customer> customers) {
    if (customers.isEmpty) return {};
    
    final totalCustomers = customers.length;
    final customersWithDebt = customers.where((c) => (c.balance ?? 0) < 0).length;
    final customersWithCredit = customers.where((c) => (c.balance ?? 0) > 0).length;
    
    final totalDebt = customers.fold<double>(0, (sum, customer) => 
        sum + (customer.balance ?? 0 < 0 ? (customer.balance ?? 0).abs() : 0));
    final totalCredit = customers.fold<double>(0, (sum, customer) => 
        sum + (customer.balance ?? 0 > 0 ? customer.balance ?? 0 : 0));
    
    return {
      'total_customers': totalCustomers,
      'customers_with_debt': customersWithDebt,
      'customers_with_credit': customersWithCredit,
      'total_debt': totalDebt,
      'total_credit': totalCredit,
      'net_balance': totalCredit - totalDebt,
    };
  }

  /// تصدير البيانات التحليلية
  static Future<String?> exportAnalytics() async {
    if (!_isEnabled) return null;
    
    try {
      final data = {
        'export_date': DateTime.now().toIso8601String(),
        'app_name': AppConstants.appName,
        'version': AppConstants.appVersion,
        'analytics_data': _analyticsData,
        'usage_statistics': getUsageStatistics(),
      };
      
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      // TODO: حفظ الملف
      return jsonString;
    } catch (e) {
      return null;
    }
  }

  /// مسح البيانات التحليلية
  static Future<void> clearAnalytics() async {
    _analyticsData.clear();
    await _saveAnalyticsData();
  }

  /// تفعيل/إلغاء التحليلات
  static Future<void> setAnalyticsEnabled(bool enabled) async {
    _isEnabled = enabled;
    await SettingsHelper.setAnalytics(enabled);
    
    if (!enabled) {
      await clearAnalytics();
    }
  }

  // دوال مساعدة خاصة
  static void _addEventToData(Map<String, dynamic> event) {
    if (!_analyticsData.containsKey('events')) {
      _analyticsData['events'] = <Map<String, dynamic>>[];
    }
    
    final events = _analyticsData['events'] as List<Map<String, dynamic>>;
    events.add(event);
    
    // الاحتفاظ بآخر 1000 حدث فقط
    if (events.length > 1000) {
      events.removeRange(0, events.length - 1000);
    }
  }

  static Future<void> _loadAnalyticsData() async {
    try {
      final data = SettingsHelper.getAnalyticsData();
      if (data != null) {
        _analyticsData.addAll(data);
      }
    } catch (e) {
      // تجاهل أخطاء التحميل
    }
  }

  static Future<void> _saveAnalyticsData() async {
    try {
      await SettingsHelper.setAnalyticsData(_analyticsData);
    } catch (e) {
      // تجاهل أخطاء الحفظ
    }
  }

  static Map<String, int> _getMostUsedFeatures(List events) {
    final features = <String, int>{};
    
    for (final event in events) {
      final name = event['name'] as String;
      features[name] = (features[name] ?? 0) + 1;
    }
    
    final sortedFeatures = Map.fromEntries(
      features.entries.toList()..sort((a, b) => b.value.compareTo(a.value))
    );
    
    return Map.fromEntries(sortedFeatures.entries.take(10));
  }

  static Map<String, int> _getScreenViews(List events) {
    final screens = <String, int>{};
    
    for (final event in events) {
      if (event['name'] == 'screen_view') {
        final screenName = event['parameters']['screen_name'] as String;
        screens[screenName] = (screens[screenName] ?? 0) + 1;
      }
    }
    
    return screens;
  }

  static int _getErrorCount(List events) {
    return events.where((e) => e['name'] == 'error').length;
  }

  static Map<String, int> _getPaymentMethodsAnalytics(List<Sale> sales) {
    final methods = <String, int>{};
    
    for (final sale in sales) {
      final method = sale.paymentMethod ?? 'غير محدد';
      methods[method] = (methods[method] ?? 0) + 1;
    }
    
    return methods;
  }

  static Map<String, double> _getSalesByDay(List<Sale> sales) {
    final salesByDay = <String, double>{};
    
    for (final sale in sales) {
      if (sale.date != null) {
        final day = DateHelper.formatDate(sale.date!);
        salesByDay[day] = (salesByDay[day] ?? 0) + (sale.total ?? 0);
      }
    }
    
    return salesByDay;
  }
}
