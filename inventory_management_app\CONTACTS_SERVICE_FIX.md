# 🔧 إصلاح مشاكل contacts_service

## **المشكلة:**
```
Because inventory_management_app depends on contacts_service ^0.9.13+6 which doesn't match any versions, version solving failed.
```

## **الحل المطبق:**

### **1. العودة للإصدار المستقر:**
```yaml
contacts_service: ^0.6.3  # الإصدار المستقر والمتاح
```

### **2. إصلاح مشاكل namespace المحتملة:**

#### **في android/app/build.gradle.kts:**
```kotlin
android {
    namespace = "com.example.inventory_management_app"  // ✅ مضاف
    compileSdk = 34                                     // ✅ محدث
    
    packagingOptions {                                  // ✅ جديد
        pickFirst("**/libc++_shared.so")
        pickFirst("**/libjsc.so")
    }
    
    defaultConfig {
        minSdk = 21                                     // ✅ محدث
        targetSdk = 34                                  // ✅ محدث
        multiDexEnabled = true                          // ✅ مضاف
    }
}

dependencies {
    implementation("androidx.multidex:multidex:2.0.1") // ✅ مضاف
}
```

### **3. الأذونات المطلوبة في AndroidManifest.xml:**
```xml
<uses-permission android:name="android.permission.READ_CONTACTS" />
<uses-permission android:name="android.permission.WRITE_CONTACTS" />
<uses-permission android:name="android.permission.GET_ACCOUNTS" />
```

## **خطوات التشغيل:**

### **الطريقة الأولى: ملف batch**
```cmd
run_app.bat
```

### **الطريقة الثانية: يدوياً**
```cmd
flutter clean
flutter pub get
flutter run
```

## **إذا استمرت المشاكل:**

### **1. تحديث Flutter:**
```cmd
flutter channel stable
flutter upgrade
flutter doctor
```

### **2. حل مشاكل Android licenses:**
```cmd
flutter doctor --android-licenses
```

### **3. تشغيل على الويب بدلاً من Android:**
```cmd
flutter config --enable-web
flutter run -d chrome
```

### **4. استخدام بديل لـ contacts_service:**
إذا استمرت المشاكل، يمكن إزالة contacts_service مؤقتاً:

```yaml
# في pubspec.yaml - علق على هذا السطر مؤقتاً
# contacts_service: ^0.6.3
```

ثم في الكود، علق على أي استخدام لـ contacts_service:
```dart
// import 'package:contacts_service/contacts_service.dart';

// Future<void> getContacts() async {
//   // كود جهات الاتصال معلق مؤقتاً
// }
```

## **النتيجة المتوقعة:**
بعد تطبيق هذه الإصلاحات، يجب أن يعمل:
```cmd
flutter pub get  ✅
flutter run       ✅
```

وستظهر شاشة Splash Screen العصرية لـ "أسامة ماركت"!
