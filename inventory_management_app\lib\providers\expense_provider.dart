import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/expense.dart';
import 'package:inventory_management_app/services/expense_service.dart';

/// Provider class for managing expense state and operations
class ExpenseProvider extends ChangeNotifier {
  List<Expense> _expenses = <Expense>[];
  final ExpenseService _expenseService = ExpenseService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of expenses
  List<Expense> get expenses => _expenses;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all expenses from the database
  Future<void> fetchExpenses() async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.getAllExpenses();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch expenses: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new expense
  Future<void> addExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.insertExpense(expense);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to add expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing expense
  Future<void> updateExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.updateExpense(expense);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to update expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an expense
  Future<void> deleteExpense(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _expenseService.deleteExpense(id);
      await fetchExpenses();
    } catch (e) {
      _setError('Failed to delete expense: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get expenses by category
  Future<void> getExpensesByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.getExpensesByCategory(categoryId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get expenses by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get expenses by date range
  Future<void> getExpensesByDateRange(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses =
          await _expenseService.getExpensesByDateRange(startDate, endDate);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get expenses by date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search expenses by notes
  Future<void> searchExpensesByNotes(String notes) async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _expenseService.searchExpensesByNotes(notes);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search expenses by notes: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get total expenses amount
  Future<double> getTotalExpensesAmount() async {
    try {
      return await _expenseService.getTotalExpensesAmount();
    } catch (e) {
      _setError('Failed to get total expenses amount: $e');
      return 0.0;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
