import 'package:sqflite/sqflite.dart';
import '../models/order.dart';
import '../models/order_item.dart';
import 'database_service.dart';

/// Service class for handling Order CRUD operations
class OrderService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all orders from the database
  Future<List<Order>> getAllOrders() async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('orders');
    
    return List.generate(maps.length, (int i) {
      return Order.fromMap(maps[i]);
    });
  }

  /// Get an order by its ID
  Future<Order?> getOrderById(int id) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );

    if (maps.isNotEmpty) {
      return Order.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new order into the database
  Future<int> insertOrder(Order order) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'orders',
      order.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing order in the database
  Future<int> updateOrder(Order order) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'orders',
      order.toMap(),
      where: 'id = ?',
      whereArgs: <Object?>[order.id],
    );
  }

  /// Delete an order from the database
  Future<int> deleteOrder(int id) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'orders',
      where: 'id = ?',
      whereArgs: <Object?>[id],
    );
  }

  /// Get orders by customer
  Future<List<Order>> getOrdersByCustomer(int customerId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'customerId = ?',
      whereArgs: <Object?>[customerId],
    );
    
    return List.generate(maps.length, (int i) {
      return Order.fromMap(maps[i]);
    });
  }

  /// Get orders by status
  Future<List<Order>> getOrdersByStatus(String status) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'status = ?',
      whereArgs: <Object?>[status],
    );
    
    return List.generate(maps.length, (int i) {
      return Order.fromMap(maps[i]);
    });
  }

  /// Get orders by date range
  Future<List<Order>> getOrdersByDateRange(String startDate, String endDate) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'date BETWEEN ? AND ?',
      whereArgs: <Object?>[startDate, endDate],
    );
    
    return List.generate(maps.length, (int i) {
      return Order.fromMap(maps[i]);
    });
  }

  /// Get total orders amount
  Future<double> getTotalOrdersAmount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result = await db.rawQuery('SELECT SUM(total) as total FROM orders');
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total number of orders
  Future<int> getOrderCount() async {
    final Database db = await _databaseService.database;
    final List<Map<String, Object?>> result = await db.rawQuery('SELECT COUNT(*) as count FROM orders');
    return result.first['count'] as int;
  }

  /// Get order items for a specific order
  Future<List<OrderItem>> getOrderItems(int orderId) async {
    final Database db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'order_items',
      where: 'orderId = ?',
      whereArgs: <Object?>[orderId],
    );
    
    return List.generate(maps.length, (int i) {
      return OrderItem.fromMap(maps[i]);
    });
  }

  /// Insert order item
  Future<int> insertOrderItem(OrderItem orderItem) async {
    final Database db = await _databaseService.database;
    return await db.insert(
      'order_items',
      orderItem.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Delete order items for a specific order
  Future<int> deleteOrderItems(int orderId) async {
    final Database db = await _databaseService.database;
    return await db.delete(
      'order_items',
      where: 'orderId = ?',
      whereArgs: <Object?>[orderId],
    );
  }

  /// Update order status
  Future<int> updateOrderStatus(int orderId, String status) async {
    final Database db = await _databaseService.database;
    return await db.update(
      'orders',
      <String, Object?>{'status': status},
      where: 'id = ?',
      whereArgs: <Object?>[orderId],
    );
  }
}
