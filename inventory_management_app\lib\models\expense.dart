class Expense {
  int? id;
  DateTime? expenseDate;
  String? category;
  double? amount;
  String? description;

  Expense({
    this.id,
    required this.expenseDate,
    required this.category,
    required this.amount,
    this.description,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'expenseDate': expenseDate?.toIso8601String(),
      'category': category,
      'amount': amount,
      'description': description,
    };
  }

  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      expenseDate: map['expenseDate'] != null
          ? DateTime.parse(map['expenseDate'])
          : null,
      category: map['category'],
      amount: map['amount']?.toDouble(),
      description: map['description'],
    );
  }
}
