import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/mobile_features_service.dart';
import '../services/print_export_service.dart';

class MobileFeaturesScreen extends StatefulWidget {
  const MobileFeaturesScreen({super.key});

  @override
  State<MobileFeaturesScreen> createState() => _MobileFeaturesScreenState();
}

class _MobileFeaturesScreenState extends State<MobileFeaturesScreen> {
  String? _scannedBarcode;
  String? _capturedImage;
  Map<String, double>? _currentLocation;
  Map<String, dynamic>? _batteryInfo;
  Map<String, int>? _storageInfo;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('ميزات الهاتف المحمول'),
          backgroundColor: Colors.deepOrange,
          foregroundColor: Colors.white,
        ),
        body: Consumer2<MobileFeaturesProvider, PrintExportProvider>(
          builder: (context, mobileProvider, printProvider, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // الكاميرا والمسح
                _buildSectionCard(
                  'الكاميرا والمسح',
                  Icons.camera_alt,
                  Colors.blue,
                  [
                    ListTile(
                      title: const Text('مسح الباركود'),
                      subtitle: Text(_scannedBarcode ?? 'لم يتم مسح أي باركود'),
                      leading: const Icon(Icons.qr_code_scanner),
                      trailing: mobileProvider.isScanning
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: mobileProvider.isScanning ? null : _scanBarcode,
                    ),
                    ListTile(
                      title: const Text('تصوير المنتج'),
                      subtitle: Text(_capturedImage ?? 'لم يتم التقاط أي صورة'),
                      leading: const Icon(Icons.camera),
                      trailing: mobileProvider.isCapturing
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: mobileProvider.isCapturing ? null : _captureImage,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // الموقع والخرائط
                _buildSectionCard(
                  'الموقع والخرائط',
                  Icons.location_on,
                  Colors.green,
                  [
                    ListTile(
                      title: const Text('الحصول على الموقع الحالي'),
                      subtitle: Text(_currentLocation != null
                          ? 'خط العرض: ${_currentLocation!['latitude']?.toStringAsFixed(4)}\nخط الطول: ${_currentLocation!['longitude']?.toStringAsFixed(4)}'
                          : 'لم يتم تحديد الموقع'),
                      leading: const Icon(Icons.my_location),
                      trailing: mobileProvider.isLocating
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: mobileProvider.isLocating ? null : _getCurrentLocation,
                    ),
                    if (_currentLocation != null)
                      ListTile(
                        title: const Text('فتح في خرائط جوجل'),
                        subtitle: const Text('عرض الموقع في الخرائط'),
                        leading: const Icon(Icons.map),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () => _openInMaps(),
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // التواصل
                _buildSectionCard(
                  'التواصل',
                  Icons.communication,
                  Colors.purple,
                  [
                    ListTile(
                      title: const Text('مشاركة عبر WhatsApp'),
                      subtitle: const Text('مشاركة الفواتير مع العملاء'),
                      leading: const Icon(Icons.message),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _shareViaWhatsApp(),
                    ),
                    ListTile(
                      title: const Text('إرسال SMS'),
                      subtitle: const Text('إرسال رسائل نصية للعملاء'),
                      leading: const Icon(Icons.sms),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _sendSMS(),
                    ),
                    ListTile(
                      title: const Text('الاتصال بالعميل'),
                      subtitle: const Text('اتصال مباشر بالعميل'),
                      leading: const Icon(Icons.call),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _callCustomer(),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // الطباعة والتصدير
                _buildSectionCard(
                  'الطباعة والتصدير',
                  Icons.print,
                  Colors.orange,
                  [
                    ListTile(
                      title: const Text('طباعة الفاتورة'),
                      subtitle: const Text('طباعة فاتورة المبيعات'),
                      leading: const Icon(Icons.receipt),
                      trailing: printProvider.isPrinting
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: printProvider.isPrinting ? null : _printInvoice,
                    ),
                    ListTile(
                      title: const Text('إنشاء PDF'),
                      subtitle: const Text('تحويل الفاتورة إلى PDF'),
                      leading: const Icon(Icons.picture_as_pdf),
                      trailing: printProvider.isGeneratingPDF
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: printProvider.isGeneratingPDF ? null : _generatePDF,
                    ),
                    ListTile(
                      title: const Text('تصدير البيانات'),
                      subtitle: const Text('تصدير المنتجات إلى Excel/CSV'),
                      leading: const Icon(Icons.file_download),
                      trailing: printProvider.isExporting
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.arrow_forward_ios),
                      onTap: printProvider.isExporting ? null : _exportData,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // معلومات النظام
                _buildSectionCard(
                  'معلومات النظام',
                  Icons.info,
                  Colors.teal,
                  [
                    ListTile(
                      title: const Text('معلومات البطارية'),
                      subtitle: Text(_batteryInfo != null
                          ? 'المستوى: ${_batteryInfo!['level']}% - ${_batteryInfo!['isCharging'] ? 'يشحن' : 'لا يشحن'}'
                          : 'اضغط للحصول على معلومات البطارية'),
                      leading: const Icon(Icons.battery_full),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _getBatteryInfo,
                    ),
                    ListTile(
                      title: const Text('مساحة التخزين'),
                      subtitle: Text(_storageInfo != null
                          ? 'المساحة المتاحة: ${(_storageInfo!['freeSpace']! / **********).toStringAsFixed(1)} GB'
                          : 'اضغط للحصول على معلومات التخزين'),
                      leading: const Icon(Icons.storage),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _getStorageInfo,
                    ),
                    ListTile(
                      title: const Text('فحص الاتصال'),
                      subtitle: const Text('التحقق من اتصال الإنترنت'),
                      leading: const Icon(Icons.wifi),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _checkConnection,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // الصوت والاهتزاز
                _buildSectionCard(
                  'الصوت والاهتزاز',
                  Icons.vibration,
                  Colors.red,
                  [
                    ListTile(
                      title: const Text('اهتزاز الهاتف'),
                      subtitle: const Text('تشغيل الاهتزاز للتنبيه'),
                      leading: const Icon(Icons.vibration),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _vibratePhone,
                    ),
                    ListTile(
                      title: const Text('صوت التنبيه'),
                      subtitle: const Text('تشغيل صوت التنبيه'),
                      leading: const Icon(Icons.volume_up),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _playSound,
                    ),
                    ListTile(
                      title: const Text('تحويل النص إلى كلام'),
                      subtitle: const Text('قراءة النصوص بالصوت'),
                      leading: const Icon(Icons.record_voice_over),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _speakText,
                    ),
                    ListTile(
                      title: const Text('التعرف على الكلام'),
                      subtitle: const Text('تحويل الكلام إلى نص'),
                      leading: const Icon(Icons.mic),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _recognizeSpeech,
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, Color color, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Future<void> _scanBarcode() async {
    final provider = context.read<MobileFeaturesProvider>();
    final barcode = await provider.scanBarcode();
    if (barcode != null) {
      setState(() {
        _scannedBarcode = barcode;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم مسح الباركود: $barcode')),
      );
    }
  }

  Future<void> _captureImage() async {
    final provider = context.read<MobileFeaturesProvider>();
    final image = await provider.captureProductImage();
    if (image != null) {
      setState(() {
        _capturedImage = image;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم التقاط الصورة: $image')),
      );
    }
  }

  Future<void> _getCurrentLocation() async {
    final provider = context.read<MobileFeaturesProvider>();
    final location = await provider.getCurrentLocation();
    if (location != null) {
      setState(() {
        _currentLocation = location;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديد الموقع بنجاح')),
      );
    }
  }

  Future<void> _openInMaps() async {
    if (_currentLocation != null) {
      final success = await MobileFeaturesService.instance.openLocationInMaps(
        _currentLocation!['latitude']!,
        _currentLocation!['longitude']!,
      );
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم فتح الموقع في الخرائط')),
        );
      }
    }
  }

  Future<void> _shareViaWhatsApp() async {
    final provider = context.read<MobileFeaturesProvider>();
    final success = await provider.shareInvoice(
      '966501234567',
      'فاتورة رقم 123\nالمبلغ: 150.00 ر.س\nشكراً لتعاملكم معنا',
    );
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم فتح WhatsApp لمشاركة الفاتورة')),
      );
    }
  }

  Future<void> _sendSMS() async {
    final provider = context.read<MobileFeaturesProvider>();
    final success = await provider.sendSMS(
      '966501234567',
      'شكراً لتعاملكم معنا. فاتورة رقم 123 بمبلغ 150.00 ر.س',
    );
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم فتح تطبيق الرسائل')),
      );
    }
  }

  Future<void> _callCustomer() async {
    final provider = context.read<MobileFeaturesProvider>();
    final success = await provider.callCustomer('966501234567');
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم بدء الاتصال')),
      );
    }
  }

  Future<void> _printInvoice() async {
    final provider = context.read<PrintExportProvider>();
    // محاكاة بيانات الفاتورة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال الفاتورة للطابعة')),
    );
  }

  Future<void> _generatePDF() async {
    final provider = context.read<PrintExportProvider>();
    // محاكاة إنشاء PDF
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إنشاء ملف PDF بنجاح')),
    );
  }

  Future<void> _exportData() async {
    final provider = context.read<PrintExportProvider>();
    // محاكاة تصدير البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تصدير البيانات بنجاح')),
    );
  }

  Future<void> _getBatteryInfo() async {
    final batteryInfo = await MobileFeaturesService.instance.getBatteryInfo();
    setState(() {
      _batteryInfo = batteryInfo;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('مستوى البطارية: ${batteryInfo['level']}%')),
    );
  }

  Future<void> _getStorageInfo() async {
    final storageInfo = await MobileFeaturesService.instance.getStorageInfo();
    setState(() {
      _storageInfo = storageInfo;
    });
    final freeGB = (storageInfo['freeSpace']! / **********).toStringAsFixed(1);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('المساحة المتاحة: $freeGB GB')),
    );
  }

  Future<void> _checkConnection() async {
    final isConnected = await MobileFeaturesService.instance.checkInternetConnection();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isConnected ? 'متصل بالإنترنت' : 'غير متصل بالإنترنت'),
        backgroundColor: isConnected ? Colors.green : Colors.red,
      ),
    );
  }

  Future<void> _vibratePhone() async {
    await MobileFeaturesService.instance.vibratePhone();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تشغيل الاهتزاز')),
    );
  }

  Future<void> _playSound() async {
    await MobileFeaturesService.instance.playNotificationSound();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تشغيل صوت التنبيه')),
    );
  }

  Future<void> _speakText() async {
    await MobileFeaturesService.instance.speakText('مرحباً بكم في تطبيق إدارة المخزون');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تشغيل تحويل النص إلى كلام')),
    );
  }

  Future<void> _recognizeSpeech() async {
    final text = await MobileFeaturesService.instance.recognizeSpeech();
    if (text != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم التعرف على: $text')),
      );
    }
  }
}
