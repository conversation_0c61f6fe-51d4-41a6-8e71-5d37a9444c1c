import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/supplier_statement_provider.dart';
import '../../providers/supplier_provider.dart';
import '../../models/supplier.dart';

class SupplierStatementScreen extends StatefulWidget {
  const SupplierStatementScreen({super.key});

  @override
  State<SupplierStatementScreen> createState() => _SupplierStatementScreenState();
}

class _SupplierStatementScreenState extends State<SupplierStatementScreen> {
  final TextEditingController _supplierController = TextEditingController();
  String _selectedPeriod = 'الكل';
  
  final List<String> _periodOptions = <String>[
    'الكل',
    'اليوم',
    'الأسبوع',
    'الشهر',
    'السنة',
    'تحديد يدوي',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SupplierProvider>().fetchSuppliers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('كشف حساب مورد'),
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Consumer<SupplierStatementProvider>(
          builder: (BuildContext context, SupplierStatementProvider statementProvider, Widget? child) {
            return Column(
              children: <Widget>[
                // Filter Section
                _buildFilterSection(statementProvider),
                
                // Content Section
                Expanded(
                  child: _buildContent(statementProvider),
                ),
              ],
            );
          },
        ),
        bottomNavigationBar: _buildBottomActions(),
      ),
    );
  }

  Widget _buildFilterSection(SupplierStatementProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: <Widget>[
          // Supplier Selection
          TextFormField(
            controller: _supplierController,
            decoration: InputDecoration(
              labelText: 'اختر المورد',
              prefixIcon: const Icon(Icons.local_shipping, color: Colors.green),
              suffixIcon: IconButton(
                icon: const Icon(Icons.search),
                onPressed: _selectSupplier,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.green),
              ),
            ),
            readOnly: true,
            onTap: _selectSupplier,
          ),
          
          const SizedBox(height: 16),
          
          // Period Selection
          Row(
            children: <Widget>[
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: InputDecoration(
                    labelText: 'الفترة الزمنية',
                    prefixIcon: const Icon(Icons.date_range, color: Colors.green),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: _periodOptions.map((String period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedPeriod = value;
                      });
                      if (value == 'تحديد يدوي') {
                        _showDateRangePicker(provider);
                      } else {
                        provider.filterByPeriod(value);
                      }
                    }
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Generate Statement Button
              ElevatedButton.icon(
                onPressed: provider.selectedSupplier != null
                    ? () => provider.loadSupplierStatement()
                    : null,
                icon: const Icon(Icons.search),
                label: const Text('عرض الكشف'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(SupplierStatementProvider provider) {
    if (provider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.green),
      );
    }

    if (provider.error != null) {
      return _buildErrorWidget(provider.error!);
    }

    if (provider.selectedSupplier == null) {
      return _buildEmptyState('يرجى اختيار مورد لعرض كشف الحساب');
    }

    if (provider.transactions.isEmpty) {
      return _buildEmptyState('لا توجد حركات للمورد في الفترة المحددة');
    }

    return Column(
      children: <Widget>[
        // Summary Section
        _buildSummarySection(provider),
        
        // Transactions List
        Expanded(
          child: _buildTransactionsList(provider),
        ),
      ],
    );
  }

  Widget _buildSummarySection(SupplierStatementProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        children: <Widget>[
          Text(
            'ملخص حساب: ${provider.selectedSupplier!.name}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: <Widget>[
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي المدين',
                  provider.formattedTotalDebit,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي الدائن',
                  provider.formattedTotalCredit,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: provider.finalBalance >= 0 ? Colors.green.shade100 : Colors.red.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: provider.finalBalance >= 0 ? Colors.green : Colors.red,
              ),
            ),
            child: Column(
              children: <Widget>[
                const Text(
                  'الرصيد النهائي',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  provider.formattedFinalBalance,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: provider.finalBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: <Widget>[
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(SupplierStatementProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: provider.transactions.length,
      itemBuilder: (BuildContext context, int index) {
        final SupplierStatementTransaction transaction = provider.transactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildTransactionCard(SupplierStatementTransaction transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: transaction.amountColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                transaction.type == 'debit' ? Icons.arrow_upward : Icons.arrow_downward,
                color: transaction.amountColor,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    transaction.description,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    transaction.formattedDate,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Text(
                  transaction.formattedAmount,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: transaction.amountColor,
                  ),
                ),
                Text(
                  'الرصيد: ${transaction.balance.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.business_center_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Consumer<SupplierStatementProvider>(
      builder: (BuildContext context, SupplierStatementProvider provider, Widget? child) {
        if (provider.transactions.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _printStatement(provider),
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _saveAsPDF(provider),
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('حفظ PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _shareStatement(provider),
                  icon: const Icon(Icons.share),
                  label: const Text('مشاركة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectSupplier() async {
    final List<Supplier> suppliers = context.read<SupplierProvider>().suppliers;

    final Supplier? supplier = await showDialog<Supplier>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('اختر مورد'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: suppliers.length,
            itemBuilder: (BuildContext context, int index) {
              final Supplier supplier = suppliers[index];
              return ListTile(
                title: Text(supplier.name ?? ''),
                subtitle: Text(supplier.phone ?? ''),
                onTap: () => Navigator.pop(context, supplier),
              );
            },
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (supplier != null) {
      setState(() {
        _supplierController.text = supplier.name ?? '';
      });
      context.read<SupplierStatementProvider>().setSupplier(supplier);
    }
  }

  Future<void> _showDateRangePicker(SupplierStatementProvider provider) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: provider.startDate != null && provider.endDate != null
          ? DateTimeRange(start: provider.startDate!, end: provider.endDate!)
          : null,
    );

    if (picked != null) {
      provider.setDateRange(picked.start, picked.end);
    }
  }

  void _printStatement(SupplierStatementProvider provider) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الطباعة قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _saveAsPDF(SupplierStatementProvider provider) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة حفظ PDF قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _shareStatement(SupplierStatementProvider provider) {
    final String statementText = provider.generateStatementText();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ كشف الحساب: ${statementText.length} حرف'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  void dispose() {
    _supplierController.dispose();
    super.dispose();
  }
}
