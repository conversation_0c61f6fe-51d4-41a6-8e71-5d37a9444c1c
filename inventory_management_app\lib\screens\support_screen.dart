import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/gamification_service.dart';

class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _subjectController = TextEditingController();

  // بيانات وهمية للتذاكر
  final List<Map<String, dynamic>> _tickets = [
    {
      'id': 'T001',
      'subject': 'مشكلة في تسجيل الدخول',
      'status': 'مفتوح',
      'priority': 'عالي',
      'date': '2024-06-01',
      'lastUpdate': '2024-06-01 14:30',
      'messages': [
        {'sender': 'أنت', 'message': 'لا أستطيع تسجيل الدخول للتطبيق', 'time': '2024-06-01 10:00'},
        {'sender': 'الدعم الفني', 'message': 'مرحباً، سنساعدك في حل هذه المشكلة', 'time': '2024-06-01 10:30'},
      ],
    },
    {
      'id': 'T002',
      'subject': 'طلب ميزة جديدة',
      'status': 'قيد المراجعة',
      'priority': 'متوسط',
      'date': '2024-05-30',
      'lastUpdate': '2024-05-31 09:15',
      'messages': [
        {'sender': 'أنت', 'message': 'أريد إضافة ميزة تصدير البيانات إلى Excel', 'time': '2024-05-30 16:00'},
        {'sender': 'الدعم الفني', 'message': 'شكراً لاقتراحك، سيتم دراسته', 'time': '2024-05-31 09:15'},
      ],
    },
  ];

  final List<Map<String, dynamic>> _faqs = [
    {
      'question': 'كيف يمكنني إضافة منتج جديد؟',
      'answer': 'اذهب إلى شاشة المنتجات واضغط على زر "إضافة منتج" ثم املأ البيانات المطلوبة.',
      'category': 'المنتجات',
    },
    {
      'question': 'كيف يمكنني إنشاء نسخة احتياطية؟',
      'answer': 'اذهب إلى الإعدادات المتقدمة واختر "النسخ الاحتياطي" ثم اضغط على "إنشاء نسخة احتياطية الآن".',
      'category': 'النسخ الاحتياطي',
    },
    {
      'question': 'كيف يمكنني تغيير كلمة المرور؟',
      'answer': 'اذهب إلى الإعدادات المتقدمة ثم "الأمان والخصوصية" واختر "تغيير كلمة المرور".',
      'category': 'الأمان',
    },
    {
      'question': 'كيف يمكنني طباعة الفواتير؟',
      'answer': 'في شاشة المبيعات، اختر الفاتورة المطلوبة واضغط على "طباعة" أو استخدم ميزات الهاتف المحمول.',
      'category': 'الطباعة',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _messageController.dispose();
    _subjectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('الدعم الفني'),
          backgroundColor: Colors.teal,
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            isScrollable: true,
            tabs: const [
              Tab(icon: Icon(Icons.help), text: 'الأسئلة الشائعة'),
              Tab(icon: Icon(Icons.support_agent), text: 'تذاكر الدعم'),
              Tab(icon: Icon(Icons.add_circle), text: 'تذكرة جديدة'),
              Tab(icon: Icon(Icons.contact_support), text: 'اتصل بنا'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildFAQTab(),
            _buildTicketsTab(),
            _buildNewTicketTab(),
            _buildContactTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQTab() {
    final categories = _faqs.map((faq) => faq['category'] as String).toSet().toList();
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // شريط البحث
        TextField(
          decoration: InputDecoration(
            hintText: 'ابحث في الأسئلة الشائعة...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        
        // الفئات
        ...categories.map((category) {
          final categoryFAQs = _faqs.where((faq) => faq['category'] == category).toList();
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ExpansionTile(
              title: Text(
                category,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              leading: Icon(
                _getCategoryIcon(category),
                color: Colors.teal,
              ),
              children: categoryFAQs.map((faq) {
                return ExpansionTile(
                  title: Text(
                    faq['question'],
                    style: const TextStyle(fontSize: 14),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        faq['answer'],
                        style: const TextStyle(
                          color: Colors.grey,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildTicketsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _tickets.length,
      itemBuilder: (context, index) {
        final ticket = _tickets[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(ticket['status']),
              child: Text(
                ticket['id'].toString().substring(1),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            title: Text(
              ticket['subject'],
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getStatusColor(ticket['status']).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        ticket['status'],
                        style: TextStyle(
                          fontSize: 12,
                          color: _getStatusColor(ticket['status']),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(ticket['priority']).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        ticket['priority'],
                        style: TextStyle(
                          fontSize: 12,
                          color: _getPriorityColor(ticket['priority']),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'آخر تحديث: ${ticket['lastUpdate']}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showTicketDetails(ticket),
          ),
        );
      },
    );
  }

  Widget _buildNewTicketTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إنشاء تذكرة دعم جديدة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          TextField(
            controller: _subjectController,
            decoration: InputDecoration(
              labelText: 'موضوع التذكرة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'الأولوية',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            items: const [
              DropdownMenuItem(value: 'منخفض', child: Text('منخفض')),
              DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
              DropdownMenuItem(value: 'عالي', child: Text('عالي')),
              DropdownMenuItem(value: 'عاجل', child: Text('عاجل')),
            ],
            onChanged: (value) {},
          ),
          const SizedBox(height: 16),
          
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'الفئة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            items: const [
              DropdownMenuItem(value: 'مشكلة تقنية', child: Text('مشكلة تقنية')),
              DropdownMenuItem(value: 'طلب ميزة', child: Text('طلب ميزة جديدة')),
              DropdownMenuItem(value: 'استفسار', child: Text('استفسار عام')),
              DropdownMenuItem(value: 'تدريب', child: Text('طلب تدريب')),
            ],
            onChanged: (value) {},
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: TextField(
              controller: _messageController,
              maxLines: null,
              expands: true,
              decoration: InputDecoration(
                labelText: 'وصف المشكلة أو الطلب',
                alignLabelWithHint: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 20),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _submitTicket(),
                  icon: const Icon(Icons.send),
                  label: const Text('إرسال التذكرة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () => _attachFile(),
                icon: const Icon(Icons.attach_file),
                label: const Text('إرفاق ملف'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Text(
            'معلومات الاتصال',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 30),
          
          _buildContactCard(
            'البريد الإلكتروني',
            '<EMAIL>',
            Icons.email,
            Colors.blue,
            () => _contactViaEmail(),
          ),
          
          _buildContactCard(
            'الهاتف',
            '+966 50 123 4567',
            Icons.phone,
            Colors.green,
            () => _contactViaPhone(),
          ),
          
          _buildContactCard(
            'واتساب',
            '+966 50 123 4567',
            Icons.message,
            Colors.green,
            () => _contactViaWhatsApp(),
          ),
          
          _buildContactCard(
            'الموقع الإلكتروني',
            'www.inventoryapp.com',
            Icons.web,
            Colors.purple,
            () => _openWebsite(),
          ),
          
          const SizedBox(height: 30),
          
          const Text(
            'ساعات العمل',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'الأحد - الخميس: 8:00 ص - 6:00 م\nالجمعة - السبت: 10:00 ص - 4:00 م',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'المنتجات': return Icons.inventory;
      case 'النسخ الاحتياطي': return Icons.backup;
      case 'الأمان': return Icons.security;
      case 'الطباعة': return Icons.print;
      default: return Icons.help;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مفتوح': return Colors.green;
      case 'قيد المراجعة': return Colors.orange;
      case 'مغلق': return Colors.grey;
      case 'محلول': return Colors.blue;
      default: return Colors.grey;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'عاجل': return Colors.red;
      case 'عالي': return Colors.orange;
      case 'متوسط': return Colors.blue;
      case 'منخفض': return Colors.green;
      default: return Colors.grey;
    }
  }

  void _showTicketDetails(Map<String, dynamic> ticket) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تذكرة ${ticket['id']}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                ticket['subject'],
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: (ticket['messages'] as List).length,
                  itemBuilder: (context, index) {
                    final message = ticket['messages'][index];
                    final isUser = message['sender'] == 'أنت';
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isUser ? Colors.blue.shade50 : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            message['sender'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(message['message']),
                          const SizedBox(height: 4),
                          Text(
                            message['time'],
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // إضافة رد جديد
            },
            child: const Text('إضافة رد'),
          ),
        ],
      ),
    );
  }

  void _submitTicket() {
    if (_subjectController.text.isEmpty || _messageController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول المطلوبة')),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال التذكرة بنجاح')),
    );
    
    // إضافة نقاط للتلعيب
    context.read<GamificationProvider>().addPoints(50, 'إرسال تذكرة دعم');
    
    _subjectController.clear();
    _messageController.clear();
    _tabController.animateTo(1); // الانتقال لتبويب التذاكر
  }

  void _attachFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة إرفاق الملفات قيد التطوير')),
    );
  }

  void _contactViaEmail() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح تطبيق البريد الإلكتروني')),
    );
  }

  void _contactViaPhone() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء الاتصال')),
    );
  }

  void _contactViaWhatsApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح واتساب')),
    );
  }

  void _openWebsite() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح الموقع الإلكتروني')),
    );
  }
}
