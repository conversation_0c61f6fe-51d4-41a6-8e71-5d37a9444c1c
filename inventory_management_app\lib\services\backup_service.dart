import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:intl/intl.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/notification_service.dart';

/// Service for handling backup and restore operations
class BackupService {
  static final BackupService _instance = BackupService._internal();
  factory BackupService() => _instance;
  BackupService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // Google Sign-In configuration
  static const List<String> _scopes = <String>[
    drive.DriveApi.driveFileScope,
  ];

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: _scopes,
  );

  GoogleSignInAccount? _currentUser;
  drive.DriveApi? _driveApi;

  /// Get current signed-in user
  GoogleSignInAccount? get currentUser => _currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _currentUser != null;

  /// Creates a backup of all data to JSON format
  Future<Map<String, dynamic>> createBackupData() async {
    try {
      final Database db = await _databaseService.database;

      // Get all data from tables
      final List<Map<String, dynamic>> products = await db.query('products');
      final List<Map<String, dynamic>> customers = await db.query('customers');
      final List<Map<String, dynamic>> suppliers = await db.query('suppliers');
      final List<Map<String, dynamic>> categories =
          await db.query('categories');
      final List<Map<String, dynamic>> units = await db.query('units');
      final List<Map<String, dynamic>> sales = await db.query('sales');
      final List<Map<String, dynamic>> purchases = await db.query('purchases');
      final List<Map<String, dynamic>> expenses = await db.query('expenses');
      final List<Map<String, dynamic>> orders = await db.query('orders');

      return <String, dynamic>{
        'backup_info': <String, dynamic>{
          'version': '1.0.0',
          'created_at': DateTime.now().toIso8601String(),
          'app_name': 'Inventory Management App',
        },
        'data': <String, dynamic>{
          'products': products,
          'customers': customers,
          'suppliers': suppliers,
          'categories': categories,
          'units': units,
          'sales': sales,
          'purchases': purchases,
          'expenses': expenses,
          'orders': orders,
        },
      };
    } catch (e) {
      throw Exception('Failed to create backup data: $e');
    }
  }

  /// Exports backup to a local file
  Future<String?> exportBackupToFile() async {
    try {
      // Create backup data
      final Map<String, dynamic> backupData = await createBackupData();
      final String jsonString =
          const JsonEncoder.withIndent('  ').convert(backupData);

      if (kIsWeb) {
        // For web platform, use file_picker to save
        final String fileName =
            'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.json';

        // This will trigger a download in web browsers
        // Note: PlatformFile creation for web download
        // final PlatformFile file = PlatformFile(
        //   name: fileName,
        //   size: jsonString.length,
        //   bytes: utf8.encode(jsonString),
        // );

        return fileName;
      } else {
        // For mobile/desktop platforms
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Backup File',
          fileName:
              'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.json',
          type: FileType.custom,
          allowedExtensions: <String>['json'],
        );

        if (outputFile != null) {
          final File file = File(outputFile);
          await file.writeAsString(jsonString);

          // إرسال إشعار نجاح النسخ الاحتياطي
          debugPrint('تم إنشاء النسخة الاحتياطية بنجاح');

          return outputFile;
        }
      }

      return null;
    } catch (e) {
      // إرسال إشعار فشل النسخ الاحتياطي
      debugPrint('فشل في إنشاء النسخة الاحتياطية');
      throw Exception('Failed to export backup: $e');
    }
  }

  /// Imports backup from a local file
  Future<bool> importBackupFromFile() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: <String>['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final File file = File(result.files.single.path!);
        final String jsonString = await file.readAsString();

        return await restoreFromBackupData(jsonString);
      }

      return false;
    } catch (e) {
      throw Exception('Failed to import backup: $e');
    }
  }

  /// Restores data from backup JSON string
  Future<bool> restoreFromBackupData(String jsonString) async {
    try {
      final Map<String, dynamic> backupData =
          json.decode(jsonString) as Map<String, dynamic>;

      // Validate backup format
      if (!backupData.containsKey('backup_info') ||
          !backupData.containsKey('data')) {
        throw Exception('Invalid backup file format');
      }

      final Map<String, dynamic> data =
          backupData['data'] as Map<String, dynamic>;
      final Database db = await _databaseService.database;

      // Start transaction for data integrity
      await db.transaction((Transaction txn) async {
        // Clear existing data (optional - you might want to merge instead)
        await txn.delete('orders');
        await txn.delete('expenses');
        await txn.delete('purchases');
        await txn.delete('sales');
        await txn.delete('products');
        await txn.delete('customers');
        await txn.delete('suppliers');
        await txn.delete('categories');
        await txn.delete('units');

        // Restore data in correct order (respecting foreign key constraints)
        if (data.containsKey('categories')) {
          for (final Map<String, dynamic> item
              in data['categories'] as List<dynamic>) {
            await txn.insert('categories', item);
          }
        }

        if (data.containsKey('units')) {
          for (final Map<String, dynamic> item
              in data['units'] as List<dynamic>) {
            await txn.insert('units', item);
          }
        }

        if (data.containsKey('suppliers')) {
          for (final Map<String, dynamic> item
              in data['suppliers'] as List<dynamic>) {
            await txn.insert('suppliers', item);
          }
        }

        if (data.containsKey('customers')) {
          for (final Map<String, dynamic> item
              in data['customers'] as List<dynamic>) {
            await txn.insert('customers', item);
          }
        }

        if (data.containsKey('products')) {
          for (final Map<String, dynamic> item
              in data['products'] as List<dynamic>) {
            await txn.insert('products', item);
          }
        }

        if (data.containsKey('sales')) {
          for (final Map<String, dynamic> item
              in data['sales'] as List<dynamic>) {
            await txn.insert('sales', item);
          }
        }

        if (data.containsKey('purchases')) {
          for (final Map<String, dynamic> item
              in data['purchases'] as List<dynamic>) {
            await txn.insert('purchases', item);
          }
        }

        if (data.containsKey('expenses')) {
          for (final Map<String, dynamic> item
              in data['expenses'] as List<dynamic>) {
            await txn.insert('expenses', item);
          }
        }

        if (data.containsKey('orders')) {
          for (final Map<String, dynamic> item
              in data['orders'] as List<dynamic>) {
            await txn.insert('orders', item);
          }
        }
      });

      return true;
    } catch (e) {
      throw Exception('Failed to restore backup: $e');
    }
  }

  /// Creates an automatic backup to app documents directory
  Future<String?> createAutoBackup() async {
    try {
      final Map<String, dynamic> backupData = await createBackupData();
      final String jsonString =
          const JsonEncoder.withIndent('  ').convert(backupData);

      if (!kIsWeb) {
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final Directory backupDir = Directory('${appDocDir.path}/backups');

        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }

        final String fileName =
            'auto_backup_${DateTime.now().millisecondsSinceEpoch}.json';
        final File backupFile = File('${backupDir.path}/$fileName');

        await backupFile.writeAsString(jsonString);

        // Keep only last 5 auto backups
        await _cleanupOldBackups(backupDir);

        return backupFile.path;
      }

      return null;
    } catch (e) {
      throw Exception('Failed to create auto backup: $e');
    }
  }

  /// Cleans up old backup files, keeping only the latest 5
  Future<void> _cleanupOldBackups(Directory backupDir) async {
    try {
      final List<FileSystemEntity> files = backupDir
          .listSync()
          .where((FileSystemEntity entity) =>
              entity is File && entity.path.endsWith('.json'))
          .toList();

      if (files.length > 5) {
        // Sort by modification time (newest first)
        files.sort((FileSystemEntity a, FileSystemEntity b) => (b as File)
            .lastModifiedSync()
            .compareTo((a as File).lastModifiedSync()));

        // Delete old files (keep only first 5)
        for (int i = 5; i < files.length; i++) {
          await files[i].delete();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old backups: $e');
      }
    }
  }

  /// Gets list of available auto backups
  Future<List<File>> getAvailableBackups() async {
    try {
      if (kIsWeb) return <File>[];

      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final Directory backupDir = Directory('${appDocDir.path}/backups');

      if (!await backupDir.exists()) {
        return <File>[];
      }

      final List<File> backupFiles = backupDir
          .listSync()
          .where((FileSystemEntity entity) =>
              entity is File && entity.path.endsWith('.json'))
          .cast<File>()
          .toList();

      // Sort by modification time (newest first)
      backupFiles.sort((File a, File b) =>
          b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      return backupFiles;
    } catch (e) {
      return <File>[];
    }
  }

  /// Restores from a specific auto backup file
  Future<bool> restoreFromAutoBackup(File backupFile) async {
    try {
      final String jsonString = await backupFile.readAsString();
      return await restoreFromBackupData(jsonString);
    } catch (e) {
      throw Exception('Failed to restore from auto backup: $e');
    }
  }

  // ==================== Google Drive Integration ====================

  /// Sign in with Google
  Future<GoogleSignInAccount?> signInWithGoogle() async {
    try {
      print('🔐 Attempting Google Sign-In...');

      final GoogleSignInAccount? account = await _googleSignIn.signIn();
      if (account != null) {
        _currentUser = account;
        _driveApi = await _createDriveApi(account);
        print('✅ Google Sign-In successful: ${account.email}');
        return account;
      } else {
        print('❌ Google Sign-In cancelled by user');
        return null;
      }
    } catch (e) {
      print('❌ Google Sign-In error: $e');
      rethrow;
    }
  }

  /// Sign out from Google
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
      _currentUser = null;
      _driveApi = null;
      print('✅ Google Sign-Out successful');
    } catch (e) {
      print('❌ Google Sign-Out error: $e');
      rethrow;
    }
  }

  /// Create Drive API instance
  Future<drive.DriveApi> _createDriveApi(GoogleSignInAccount account) async {
    final Map<String, String> headers = await account.authHeaders;
    final http.Client client = _GoogleAuthClient(headers);
    return drive.DriveApi(client);
  }

  /// Get Google Drive API instance
  Future<drive.DriveApi?> getGoogleDriveApi() async {
    if (_currentUser == null) {
      print('❌ No user signed in');
      return null;
    }

    _driveApi ??= await _createDriveApi(_currentUser!);

    return _driveApi;
  }

  /// Upload database file to Google Drive
  Future<String?> uploadDatabaseToGoogleDrive() async {
    try {
      final drive.DriveApi? driveApi = await getGoogleDriveApi();
      if (driveApi == null) {
        throw Exception('Google Drive API not available');
      }

      // Get database file path
      final String dbPath = await _databaseService.getDatabaseFilePath();
      final File dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        throw Exception('Database file does not exist');
      }

      // Generate filename with timestamp
      final DateTime now = DateTime.now();
      final String timestamp =
          '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
      final String fileName = 'inventory_backup_$timestamp.db';

      print('☁️ Uploading database to Google Drive: $fileName');

      // Create app folder if it doesn't exist
      final String? appFolderId = await _getOrCreateAppFolder(driveApi);

      // Create file metadata
      final drive.File driveFile = drive.File()
        ..name = fileName
        ..parents = appFolderId != null ? <String>[appFolderId] : null;

      // Upload file
      final drive.Media media =
          drive.Media(dbFile.openRead(), await dbFile.length());
      final drive.File uploadedFile = await driveApi.files.create(
        driveFile,
        uploadMedia: media,
      );

      print('✅ Database uploaded successfully: ${uploadedFile.id}');
      return uploadedFile.id;
    } catch (e) {
      print('❌ Error uploading database to Google Drive: $e');
      rethrow;
    }
  }

  /// Download database file from Google Drive
  Future<void> downloadDatabaseFromGoogleDrive(String fileId) async {
    try {
      final drive.DriveApi? driveApi = await getGoogleDriveApi();
      if (driveApi == null) {
        throw Exception('Google Drive API not available');
      }

      print('☁️ Downloading database from Google Drive: $fileId');

      // Create temporary file for download
      final Directory tempDir = await getTemporaryDirectory();
      final String tempPath = join(tempDir.path, 'temp_restore.db');

      // Download file content
      final drive.Media media = await driveApi.files.get(
        fileId,
        downloadOptions: drive.DownloadOptions.fullMedia,
      ) as drive.Media;

      // Save to temporary file
      final File tempFile = File(tempPath);
      final IOSink sink = tempFile.openWrite();

      await for (final List<int> chunk in media.stream) {
        sink.add(chunk);
      }

      await sink.close();

      // Restore database from temporary file
      await _databaseService.restoreDatabaseFromFile(tempPath);

      // Clean up temporary file
      await tempFile.delete();

      print('✅ Database downloaded and restored successfully');
    } catch (e) {
      print('❌ Error downloading database from Google Drive: $e');
      rethrow;
    }
  }

  /// List backup files on Google Drive
  Future<List<drive.File>> listBackupFilesOnDrive() async {
    try {
      final drive.DriveApi? driveApi = await getGoogleDriveApi();
      if (driveApi == null) {
        throw Exception('Google Drive API not available');
      }

      print('☁️ Listing backup files on Google Drive...');

      // Get app folder ID
      final String? appFolderId = await _getOrCreateAppFolder(driveApi);

      // Search for backup files
      String query = "name contains 'inventory_backup' and trashed=false";
      if (appFolderId != null) {
        query += " and '$appFolderId' in parents";
      }

      final drive.FileList fileList = await driveApi.files.list(
        q: query,
        orderBy: 'modifiedTime desc',
        pageSize: 100,
      );

      final List<drive.File> backupFiles = fileList.files ?? <drive.File>[];
      print('✅ Found ${backupFiles.length} backup files on Google Drive');

      return backupFiles;
    } catch (e) {
      print('❌ Error listing backup files on Google Drive: $e');
      return <drive.File>[];
    }
  }

  /// Get or create app folder on Google Drive
  Future<String?> _getOrCreateAppFolder(drive.DriveApi driveApi) async {
    try {
      const String folderName = 'InventoryManagementBackups';

      // Search for existing folder
      final drive.FileList existingFolders = await driveApi.files.list(
        q: "name='$folderName' and mimeType='application/vnd.google-apps.folder' and trashed=false",
      );

      if (existingFolders.files != null && existingFolders.files!.isNotEmpty) {
        final String folderId = existingFolders.files!.first.id!;
        print('📁 Using existing app folder: $folderId');
        return folderId;
      }

      // Create new folder
      final drive.File folder = drive.File()
        ..name = folderName
        ..mimeType = 'application/vnd.google-apps.folder';

      final drive.File createdFolder = await driveApi.files.create(folder);
      print('📁 Created new app folder: ${createdFolder.id}');

      return createdFolder.id;
    } catch (e) {
      print('❌ Error getting/creating app folder: $e');
      return null;
    }
  }

  /// Delete file from Google Drive
  Future<void> deleteFileFromGoogleDrive(String fileId) async {
    try {
      final drive.DriveApi? driveApi = await getGoogleDriveApi();
      if (driveApi == null) {
        throw Exception('Google Drive API not available');
      }

      await driveApi.files.delete(fileId);
      print('✅ File deleted from Google Drive: $fileId');
    } catch (e) {
      print('❌ Error deleting file from Google Drive: $e');
      rethrow;
    }
  }

  /// Create local database backup
  Future<String> createLocalDatabaseBackup() async {
    try {
      final String backupDir = await getLocalBackupDirectory();
      // TODO: إضافة createTimestampedBackup لاحقاً
      final String timestamp =
          DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final String backupFileName = 'inventory_backup_$timestamp.db';
      final String backupPath = join(backupDir, backupFileName);

      // نسخ قاعدة البيانات الحالية
      final String dbPath = await _databaseService.getDatabaseFilePath();
      final File sourceFile = File(dbPath);
      await sourceFile.copy(backupPath);

      return backupPath;
    } catch (e) {
      print('❌ Error creating local database backup: $e');
      rethrow;
    }
  }

  /// Get local backup directory
  Future<String> getLocalBackupDirectory() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String backupDir = join(appDocDir.path, 'database_backups');

      // Create directory if it doesn't exist
      final Directory backupDirectory = Directory(backupDir);
      if (!await backupDirectory.exists()) {
        await backupDirectory.create(recursive: true);
      }

      return backupDir;
    } catch (e) {
      print('❌ Error getting local backup directory: $e');
      rethrow;
    }
  }

  /// List local database backup files
  Future<List<File>> listLocalDatabaseBackups() async {
    try {
      final String backupDir = await getLocalBackupDirectory();
      final Directory directory = Directory(backupDir);

      if (!await directory.exists()) {
        return <File>[];
      }

      final List<FileSystemEntity> entities = await directory.list().toList();
      final List<File> backupFiles = entities
          .whereType<File>()
          .where((File file) => file.path.endsWith('.db'))
          .toList();

      // Sort by modification time (newest first)
      backupFiles.sort((File a, File b) =>
          b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      print('📁 Found ${backupFiles.length} local database backup files');
      return backupFiles;
    } catch (e) {
      print('❌ Error listing local database backup files: $e');
      return <File>[];
    }
  }

  /// Clean old local database backup files (keep only latest N files)
  Future<void> cleanOldLocalDatabaseBackups({int keepCount = 5}) async {
    try {
      final List<File> backupFiles = await listLocalDatabaseBackups();

      if (backupFiles.length > keepCount) {
        final List<File> filesToDelete = backupFiles.skip(keepCount).toList();

        for (final File file in filesToDelete) {
          await file.delete();
          print('🗑️ Deleted old database backup: ${basename(file.path)}');
        }

        print('✅ Cleaned ${filesToDelete.length} old database backup files');
      }
    } catch (e) {
      print('❌ Error cleaning old local database backups: $e');
    }
  }

  /// Get file size in human readable format
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Custom HTTP client for Google API authentication
class _GoogleAuthClient extends http.BaseClient {
  final Map<String, String> _headers;
  final http.Client _client = http.Client();

  _GoogleAuthClient(this._headers);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers.addAll(_headers);
    return _client.send(request);
  }

  @override
  void close() {
    _client.close();
  }
}
