import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/backup_provider.dart';
import 'lib/screens/settings/backup_settings_screen.dart';

void main() {
  runApp(const TestBackupApp());
}

class TestBackupApp extends StatelessWidget {
  const TestBackupApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => BackupProvider(),
        ),
      ],
      child: MaterialApp(
        title: 'Test Backup System',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: const TestBackupScreen(),
      ),
    );
  }
}

class TestBackupScreen extends StatefulWidget {
  const TestBackupScreen({super.key});

  @override
  State<TestBackupScreen> createState() => _TestBackupScreenState();
}

class _TestBackupScreenState extends State<TestBackupScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Backup System'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Backup System Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BackupSettingsScreen(),
                  ),
                );
              },
              child: const Text('Open Backup Settings'),
            ),
            const SizedBox(height: 16),
            Consumer<BackupProvider>(
              builder: (context, backupProvider, child) {
                return Column(
                  children: [
                    Text('Loading: ${backupProvider.isLoading}'),
                    Text('Signed In: ${backupProvider.isSignedIn}'),
                    Text('Local Backups: ${backupProvider.localBackups.length}'),
                    Text('Drive Backups: ${backupProvider.driveBackups.length}'),
                    if (backupProvider.errorMessage != null)
                      Text(
                        'Error: ${backupProvider.errorMessage}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    if (backupProvider.successMessage != null)
                      Text(
                        'Success: ${backupProvider.successMessage}',
                        style: const TextStyle(color: Colors.green),
                      ),
                  ],
                );
              },
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    context.read<BackupProvider>().performLocalBackup();
                  },
                  child: const Text('Local Backup'),
                ),
                ElevatedButton(
                  onPressed: () {
                    context.read<BackupProvider>().signInGoogle();
                  },
                  child: const Text('Sign In Google'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
