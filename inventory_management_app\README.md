# Inventory Management App

## Overview
The Inventory Management App is a Flutter application designed to help users manage their inventory efficiently. It provides features for adding, editing, and deleting inventory items, as well as viewing detailed information about each item.

## Features
- **SQLite Database Integration**: The app uses SQLite for persistent storage of inventory items, ensuring data is saved even when the app is closed.
- **State Management with Provider**: The app utilizes the Provider package for state management, allowing for efficient updates and management of inventory data.
- **Routing with go_router**: The app employs the go_router package for seamless navigation between different screens.

## Project Structure
The project is organized into several directories, each serving a specific purpose:

- **lib/**: Contains the main application code.
  - **main.dart**: Entry point of the application.
  - **core/**: Contains core application files like the router.
  - **models/**: Defines the data models used in the app.
  - **providers/**: Contains the state management logic.
  - **data/**: Contains data access logic (repositories).
  - **services/**: Handles database operations and other business logic.
  - **screens/**: Contains the UI screens of the app.
  - **widgets/**: Reusable UI components.
  - **utils/**: Contains utility constants and functions.

## Setup Instructions
1. **Clone the repository**:
   ```
   git clone <repository-url>
   ```
2. **Navigate to the project directory**:
   ```
   cd inventory_management_app
   ```
3. **Install dependencies**:
   ```
   flutter pub get
   ```
4. **Run the app**:
   ```
   flutter run
   ```

## Dependencies
The project relies on several packages, including:
- `provider`: For state management.
- `sqflite`: For SQLite database integration.
- `go_router`: For routing and navigation.
- `shared_preferences`: For storing user preferences.
- `file_picker`: For file selection (backup and restore).
- `sqflite_common_ffi`: For SQLite FFI support.

## Contribution
Contributions are welcome! Please feel free to submit a pull request or open an issue for any suggestions or improvements.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.