import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:inventory_management_app/services/sale_service.dart';
import 'package:inventory_management_app/services/transaction_service.dart';

/// Provider class for managing sale state and operations
class SaleProvider extends ChangeNotifier {
  List<Sale> _sales = <Sale>[];
  final SaleService _saleService = SaleService();
  final TransactionService _transactionService = TransactionService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of sales
  List<Sale> get sales => _sales;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load sales from database
  Future<void> initialize() async {
    await fetchSales();
  }

  /// Fetch all sales from the database
  Future<void> fetchSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getAllSales();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch sales: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new sale
  Future<void> addSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.insertSale(sale);
      await fetchSales();
    } catch (e) {
      _setError('Failed to add sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing sale
  Future<void> updateSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.updateSale(sale);
      await fetchSales();
    } catch (e) {
      _setError('Failed to update sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a sale
  Future<void> deleteSale(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.deleteSale(id);
      await fetchSales();
    } catch (e) {
      _setError('Failed to delete sale: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sales by customer
  Future<void> getSalesByCustomer(int customerId) async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getSalesByCustomer(customerId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get sales by customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sales by date range
  Future<void> getSalesByDateRange(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getSalesByDateRange(startDate, endDate);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get sales by date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sale items for a specific sale
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleService.getSaleItems(saleId);
    } catch (e) {
      _setError('Failed to get sale items: $e');
      return <SaleItem>[];
    }
  }

  /// Get total sales amount
  Future<double> getTotalSalesAmount() async {
    try {
      return await _saleService.getTotalSalesAmount();
    } catch (e) {
      _setError('Failed to get total sales amount: $e');
      return 0.0;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Create sale with items using transaction
  Future<void> createSaleWithItems(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _transactionService.createSaleWithItems(sale, items);
      await fetchSales();
    } catch (e) {
      _setError('Failed to create sale with items: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete sale and restore stock
  Future<void> deleteSaleAndRestoreStock(int saleId) async {
    _setLoading(true);
    _clearError();

    try {
      await _transactionService.deleteSaleAndRestoreStock(saleId);
      await fetchSales();
    } catch (e) {
      _setError('Failed to delete sale and restore stock: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sale count
  Future<int> getSaleCount() async {
    try {
      return await _saleService.getSaleCount();
    } catch (e) {
      _setError('Failed to get sale count: $e');
      return 0;
    }
  }

  /// مسح جميع المبيعات
  void clearSales() {
    _sales.clear();
    notifyListeners();
  }
}
