import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';
import '../../models/sale_item.dart';

class AddProductToInvoiceDialog extends StatefulWidget {
  const AddProductToInvoiceDialog({super.key});

  @override
  State<AddProductToInvoiceDialog> createState() => _AddProductToInvoiceDialogState();
}

class _AddProductToInvoiceDialogState extends State<AddProductToInvoiceDialog> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController(text: '1');
  List<Product> _filteredProducts = <Product>[];
  Product? _selectedProduct;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  void _loadProducts() {
    final ProductProvider productProvider = context.read<ProductProvider>();
    setState(() {
      _filteredProducts = productProvider.products
          .where((Product product) => (product.warehouseQuantity ?? 0) > 0) // فقط المنتجات المتوفرة في المخزن
          .toList();
      _isLoading = false;
    });
  }

  void _filterProducts(String query) {
    final ProductProvider productProvider = context.read<ProductProvider>();
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = productProvider.products
            .where((Product product) => (product.warehouseQuantity ?? 0) > 0)
            .toList();
      } else {
        _filteredProducts = productProvider.products
            .where((Product product) =>
                (product.warehouseQuantity ?? 0) > 0 &&
                (product.name.toLowerCase().contains(query.toLowerCase()) ||
                 (product.barcode?.contains(query) ?? false)))
            .toList();
      }
    });
  }

  double get _totalPrice {
    if (_selectedProduct == null) return 0.0;
    final double quantity = double.tryParse(_quantityController.text) ?? 0;
    return quantity * (_selectedProduct!.price ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: <Widget>[
              // العنوان
              const Text(
                'إضافة منتج للفاتورة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // شريط البحث
              TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'البحث عن منتج',
                  hintText: 'ادخل اسم المنتج أو الباركود',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: _filterProducts,
              ),
              
              const SizedBox(height: 16),
              
              // قائمة المنتجات
              Expanded(
                flex: 2,
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredProducts.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
                                SizedBox(height: 16),
                                Text(
                                  'لا توجد منتجات متوفرة',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredProducts.length,
                            itemBuilder: (BuildContext context, int index) {
                              final Product product = _filteredProducts[index];
                              return _buildProductItem(product);
                            },
                          ),
              ),
              
              const SizedBox(height: 16),
              
              // تفاصيل المنتج المحدد
              if (_selectedProduct != null) ...<Widget>[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'المنتج المحدد: ${_selectedProduct!.name}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('سعر الجملة: ${_selectedProduct!.price.toStringAsFixed(2) ?? '0.00'} ر.س'),
                      Text('سعر التجزئة: ${_selectedProduct!.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                      Text('المتوفر في المخزن: ${_selectedProduct!.warehouseQuantity?.toString() ?? '0'}'),
                      Text('المتوفر في البقالة: ${_selectedProduct!.storeQuantity?.toString() ?? '0'}'),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // الكمية
                Row(
                  children: <Widget>[
                    Expanded(
                      child: TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'الكمية',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (String value) {
                          setState(() {}); // لتحديث المبلغ الإجمالي
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Text(
                          'الإجمالي: ${_totalPrice.toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              
              const SizedBox(height: 16),
              
              // أزرار الإجراءات
              Row(
                children: <Widget>[
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedProduct != null ? _addToInvoice : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إضافة للفاتورة'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductItem(Product product) {
    final bool isSelected = _selectedProduct?.id == product.id;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: isSelected ? Colors.blue.shade50 : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isSelected ? Colors.blue : Colors.grey.shade300,
          child: Icon(
            Icons.inventory_2,
            color: isSelected ? Colors.white : Colors.grey,
          ),
        ),
        title: Text(
          product.name,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('سعر الجملة: ${product.price.toStringAsFixed(2) ?? '0.00'} ر.س'),
            Text('سعر التجزئة: ${product.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س'),
            Text(
              'المخزن: ${product.warehouseQuantity?.toString() ?? '0'}',
              style: TextStyle(
                color: (product.warehouseQuantity ?? 0) <= 10 ? Colors.orange : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'البقالة: ${product.storeQuantity?.toString() ?? '0'}',
              style: TextStyle(
                color: (product.storeQuantity ?? 0) <= 10 ? Colors.orange : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (product.barcode != null && product.barcode!.isNotEmpty)
              Text('الباركود: ${product.barcode}'),
          ],
        ),
        trailing: isSelected 
            ? const Icon(Icons.check_circle, color: Colors.blue)
            : const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          setState(() {
            _selectedProduct = product;
          });
        },
      ),
    );
  }

  void _addToInvoice() {
    if (_selectedProduct == null) return;
    
    final double? quantity = double.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال كمية صحيحة')),
      );
      return;
    }
    
    if (quantity > (_selectedProduct!.warehouseQuantity ?? 0)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الكمية المطلوبة أكبر من المتوفر في المخزن')),
      );
      return;
    }
    
    final SaleItem saleItem = SaleItem(
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      quantity: quantity,
      unitPrice: _selectedProduct!.price ?? 0,
      totalPrice: _totalPrice,
      itemType: 'wholesale', // هذا للبيع بالجملة
    );
    
    Navigator.pop(context, saleItem);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    super.dispose();
  }
}
