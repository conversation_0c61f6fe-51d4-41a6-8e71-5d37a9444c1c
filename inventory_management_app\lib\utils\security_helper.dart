import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/// فئة مساعدة للأمان والتشفير
class SecurityHelper {
  static const String _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  static final Random _rnd = Random();

  /// إنشاء كلمة مرور عشوائية
  static String generateRandomPassword({int length = 12}) {
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length)),
      ),
    );
  }

  /// إنشاء رمز عشوائي
  static String generateRandomCode({int length = 6}) {
    const numbers = '0123456789';
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => numbers.codeUnitAt(_rnd.nextInt(numbers.length)),
      ),
    );
  }

  /// إنشاء معرف فريد
  static String generateUniqueId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = _rnd.nextInt(999999);
    return '$timestamp$random';
  }

  /// تشفير النص باستخدام SHA-256
  static String hashSHA256(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// تشفير النص باستخدام MD5
  static String hashMD5(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// تشفير كلمة المرور مع Salt
  static String hashPasswordWithSalt(String password, String salt) {
    final combined = password + salt;
    return hashSHA256(combined);
  }

  /// إنشاء Salt عشوائي
  static String generateSalt({int length = 32}) {
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = _rnd.nextInt(256);
    }
    return base64.encode(bytes);
  }

  /// التحقق من قوة كلمة المرور
  static PasswordStrength checkPasswordStrength(String password) {
    if (password.length < 6) {
      return PasswordStrength.weak;
    }

    int score = 0;

    // طول كلمة المرور
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // وجود أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) score++;

    // وجود أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) score++;

    // وجود أرقام
    if (password.contains(RegExp(r'[0-9]'))) score++;

    // وجود رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;

    if (score < 3) {
      return PasswordStrength.weak;
    } else if (score < 5) {
      return PasswordStrength.medium;
    } else {
      return PasswordStrength.strong;
    }
  }

  /// تشفير بسيط للنصوص (Base64)
  static String encodeBase64(String input) {
    final bytes = utf8.encode(input);
    return base64.encode(bytes);
  }

  /// فك تشفير Base64
  static String decodeBase64(String encoded) {
    final bytes = base64.decode(encoded);
    return utf8.decode(bytes);
  }

  /// تشفير بسيط باستخدام XOR
  static String encryptXOR(String input, String key) {
    final inputBytes = utf8.encode(input);
    final keyBytes = utf8.encode(key);
    final encrypted = <int>[];

    for (int i = 0; i < inputBytes.length; i++) {
      encrypted.add(inputBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64.encode(encrypted);
  }

  /// فك تشفير XOR
  static String decryptXOR(String encrypted, String key) {
    final encryptedBytes = base64.decode(encrypted);
    final keyBytes = utf8.encode(key);
    final decrypted = <int>[];

    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return utf8.decode(decrypted);
  }

  /// إنشاء رمز JWT بسيط (للاستخدام المحلي فقط)
  static String createSimpleJWT(Map<String, dynamic> payload, String secret) {
    final header = {
      'alg': 'HS256',
      'typ': 'JWT',
    };

    final encodedHeader = base64Url.encode(utf8.encode(json.encode(header)));
    final encodedPayload = base64Url.encode(utf8.encode(json.encode(payload)));

    final signature = _createSignature('$encodedHeader.$encodedPayload', secret);

    return '$encodedHeader.$encodedPayload.$signature';
  }

  /// التحقق من صحة JWT بسيط
  static bool verifySimpleJWT(String token, String secret) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return false;

      final signature = _createSignature('${parts[0]}.${parts[1]}', secret);
      return signature == parts[2];
    } catch (e) {
      return false;
    }
  }

  /// فك تشفير JWT بسيط
  static Map<String, dynamic>? decodeSimpleJWT(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = json.decode(utf8.decode(base64Url.decode(parts[1])));
      return payload as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء توقيع للـ JWT
  static String _createSignature(String data, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return base64Url.encode(digest.bytes);
  }

  /// تنظيف البيانات الحساسة من الذاكرة
  static void clearSensitiveData(String data) {
    // في Dart، لا يمكننا مسح البيانات من الذاكرة مباشرة
    // ولكن يمكننا إعادة تعيين المتغيرات إلى null
    // هذا مفيد أكثر في اللغات التي تدير الذاكرة يدوياً
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف السعودي
  static bool isValidSaudiPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');
    return RegExp(r'^(\+966|966|0)?5[0-9]{8}$').hasMatch(cleanPhone);
  }

  /// التحقق من صحة رقم الهوية السعودي
  static bool isValidSaudiID(String id) {
    if (id.length != 10) return false;
    if (!RegExp(r'^[12][0-9]{9}$').hasMatch(id)) return false;

    // خوارزمية التحقق من رقم الهوية السعودي
    int sum = 0;
    for (int i = 0; i < 9; i++) {
      int digit = int.parse(id[i]);
      if (i % 2 == 0) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      sum += digit;
    }

    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(id[9]);
  }

  /// إنشاء رمز QR للبيانات
  static String generateQRData(Map<String, dynamic> data) {
    return json.encode(data);
  }

  /// تحليل بيانات QR
  static Map<String, dynamic>? parseQRData(String qrData) {
    try {
      return json.decode(qrData) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء رمز تحقق للعمليات الحساسة
  static String generateVerificationCode() {
    return generateRandomCode(length: 6);
  }

  /// التحقق من انتهاء صلاحية الرمز
  static bool isCodeExpired(DateTime createdAt, {Duration validity = const Duration(minutes: 5)}) {
    return DateTime.now().isAfter(createdAt.add(validity));
  }

  /// تشفير البيانات الحساسة للتخزين المحلي
  static String encryptForStorage(String data, String key) {
    return encryptXOR(data, key);
  }

  /// فك تشفير البيانات من التخزين المحلي
  static String decryptFromStorage(String encryptedData, String key) {
    return decryptXOR(encryptedData, key);
  }
}

/// تعداد قوة كلمة المرور
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// امتداد لتحويل قوة كلمة المرور إلى نص
extension PasswordStrengthExtension on PasswordStrength {
  String get displayName {
    switch (this) {
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
    }
  }

  String get description {
    switch (this) {
      case PasswordStrength.weak:
        return 'كلمة المرور ضعيفة، يُنصح بتقويتها';
      case PasswordStrength.medium:
        return 'كلمة المرور متوسطة القوة';
      case PasswordStrength.strong:
        return 'كلمة المرور قوية وآمنة';
    }
  }
}
