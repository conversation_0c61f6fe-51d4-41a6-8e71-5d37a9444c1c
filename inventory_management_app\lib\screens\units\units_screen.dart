import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/models/unit.dart';
import 'package:inventory_management_app/screens/units/unit_details_screen.dart';

class UnitsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final UnitProvider unitProvider = Provider.of<UnitProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Units'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (BuildContext context) => const UnitDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: unitProvider.units.length,
        itemBuilder: (BuildContext context, int index) {
          final Unit unit = unitProvider.units[index];
          return Card(
            child: ListTile(
              title: Text(unit.name),
              subtitle: Text(unit.symbol ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              UnitDetailsScreen(unit: unit),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      unitProvider.deleteUnit(unit.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
