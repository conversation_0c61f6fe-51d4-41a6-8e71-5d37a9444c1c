/// Model class representing an item in a sale transaction
class SaleItem {
  /// Unique identifier for the sale item
  int? id;

  /// Sale ID that this item belongs to
  int? saleId;

  /// Product ID for this sale item
  int? productId;

  /// Quantity of the product sold
  double? quantity;

  /// Price per unit for this sale item
  double? price;

  /// Constructor for creating a SaleItem instance
  SaleItem({
    this.id,
    this.saleId,
    this.productId,
    this.quantity,
    this.price,
  });

  /// Converts the SaleItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'saleId': saleId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
    };
  }

  /// Creates a SaleItem instance from a Map (typically from database)
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map['id'] as int?,
      saleId: map['saleId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'SaleItem{id: $id, saleId: $saleId, productId: $productId, '
        'quantity: $quantity, price: $price}';
  }
}
