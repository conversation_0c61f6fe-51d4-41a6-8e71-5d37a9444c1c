import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// فئة مساعدة للتحقق من الاتصال بالإنترنت
class ConnectivityHelper {
  static ConnectivityHelper? _instance;
  static ConnectivityHelper get instance => _instance ??= ConnectivityHelper._();
  
  ConnectivityHelper._();

  bool _isConnected = true;
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();

  /// تدفق حالة الاتصال
  Stream<bool> get connectionStream => _connectionController.stream;

  /// حالة الاتصال الحالية
  bool get isConnected => _isConnected;

  /// بدء مراقبة الاتصال
  void startMonitoring() {
    if (!kIsWeb) {
      Timer.periodic(const Duration(seconds: 5), (timer) {
        checkConnection();
      });
    }
  }

  /// التحقق من الاتصال
  Future<bool> checkConnection() async {
    if (kIsWeb) {
      // في الويب، نفترض وجود اتصال
      return true;
    }

    try {
      final result = await InternetAddress.lookup('google.com');
      final isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      if (_isConnected != isConnected) {
        _isConnected = isConnected;
        _connectionController.add(_isConnected);
      }
      
      return isConnected;
    } on SocketException catch (_) {
      if (_isConnected) {
        _isConnected = false;
        _connectionController.add(_isConnected);
      }
      return false;
    }
  }

  /// التحقق من الاتصال مع موقع محدد
  Future<bool> checkConnectionToHost(String host) async {
    if (kIsWeb) {
      return true;
    }

    try {
      final result = await InternetAddress.lookup(host);
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// التحقق من سرعة الاتصال (تقريبي)
  Future<ConnectionSpeed> checkConnectionSpeed() async {
    if (kIsWeb) {
      return ConnectionSpeed.fast;
    }

    final stopwatch = Stopwatch()..start();
    
    try {
      await InternetAddress.lookup('google.com');
      stopwatch.stop();
      
      final milliseconds = stopwatch.elapsedMilliseconds;
      
      if (milliseconds < 100) {
        return ConnectionSpeed.fast;
      } else if (milliseconds < 500) {
        return ConnectionSpeed.medium;
      } else {
        return ConnectionSpeed.slow;
      }
    } on SocketException catch (_) {
      return ConnectionSpeed.none;
    }
  }

  /// إنهاء مراقبة الاتصال
  void dispose() {
    _connectionController.close();
  }
}

/// تعداد سرعة الاتصال
enum ConnectionSpeed {
  none,
  slow,
  medium,
  fast,
}

/// امتداد لتحويل سرعة الاتصال إلى نص
extension ConnectionSpeedExtension on ConnectionSpeed {
  String get displayName {
    switch (this) {
      case ConnectionSpeed.none:
        return 'لا يوجد اتصال';
      case ConnectionSpeed.slow:
        return 'اتصال بطيء';
      case ConnectionSpeed.medium:
        return 'اتصال متوسط';
      case ConnectionSpeed.fast:
        return 'اتصال سريع';
    }
  }

  String get description {
    switch (this) {
      case ConnectionSpeed.none:
        return 'لا يوجد اتصال بالإنترنت';
      case ConnectionSpeed.slow:
        return 'الاتصال بطيء، قد تستغرق العمليات وقتاً أطول';
      case ConnectionSpeed.medium:
        return 'الاتصال متوسط السرعة';
      case ConnectionSpeed.fast:
        return 'الاتصال سريع وجيد';
    }
  }
}

/// فئة مساعدة لإدارة العمليات المتعلقة بالشبكة
class NetworkHelper {
  /// تنفيذ عملية مع إعادة المحاولة
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        await Future.delayed(delay * attempts);
      }
    }
    
    throw Exception('فشل في تنفيذ العملية بعد $maxRetries محاولات');
  }

  /// تنفيذ عملية مع مهلة زمنية
  static Future<T> executeWithTimeout<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return await operation().timeout(
      timeout,
      onTimeout: () => throw TimeoutException(
        'انتهت المهلة الزمنية للعملية',
        timeout,
      ),
    );
  }

  /// تنفيذ عملية مع التحقق من الاتصال
  static Future<T> executeWithConnectivityCheck<T>(
    Future<T> Function() operation,
  ) async {
    final isConnected = await ConnectivityHelper.instance.checkConnection();
    
    if (!isConnected) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }
    
    return await operation();
  }

  /// تنفيذ عملية مع جميع الحمايات
  static Future<T> executeRobust<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration timeout = const Duration(seconds: 30),
    Duration retryDelay = const Duration(seconds: 1),
    bool checkConnectivity = true,
  }) async {
    return await executeWithRetry(
      () async {
        if (checkConnectivity) {
          return await executeWithConnectivityCheck(
            () => executeWithTimeout(operation, timeout: timeout),
          );
        } else {
          return await executeWithTimeout(operation, timeout: timeout);
        }
      },
      maxRetries: maxRetries,
      delay: retryDelay,
    );
  }
}

/// استثناء مخصص للشبكة
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  NetworkException(
    this.message, {
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    if (statusCode != null) {
      return 'NetworkException: $message (Status: $statusCode)';
    }
    return 'NetworkException: $message';
  }
}

/// استثناء انتهاء المهلة الزمنية
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  TimeoutException(this.message, this.timeout);

  @override
  String toString() {
    return 'TimeoutException: $message (Timeout: ${timeout.inSeconds}s)';
  }
}

/// استثناء عدم وجود اتصال
class NoConnectivityException implements Exception {
  final String message;

  NoConnectivityException(this.message);

  @override
  String toString() {
    return 'NoConnectivityException: $message';
  }
}
