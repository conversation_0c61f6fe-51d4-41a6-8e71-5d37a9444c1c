/// ثوابت التطبيق العامة
class AppConstants {
  // معلومات التطبيق
  static const String appName = 'إدارة المخزون والمبيعات';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'تطبيق شامل لإدارة المخزون والمبيعات والمشتريات';

  // إعدادات قاعدة البيانات
  static const String databaseName = 'inventory_management.db';
  static const int databaseVersion = 1;

  // أسماء الجداول
  static const String productsTable = 'products';
  static const String customersTable = 'customers';
  static const String suppliersTable = 'suppliers';
  static const String salesTable = 'sales';
  static const String saleItemsTable = 'sale_items';
  static const String purchasesTable = 'purchases';
  static const String purchaseItemsTable = 'purchase_items';
  static const String expensesTable = 'expenses';
  static const String categoriesTable = 'categories';
  static const String unitsTable = 'units';

  // إعدادات المخزون
  static const int defaultMinStockLevel = 10;
  static const int defaultMaxStockLevel = 1000;

  // إعدادات العملة
  static const String defaultCurrency = 'ر.س';
  static const String currencySymbol = 'SAR';
  static const int decimalPlaces = 2;

  // إعدادات التاريخ والوقت
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';

  // إعدادات الفواتير
  static const String invoicePrefix = 'INV';
  static const String saleInvoicePrefix = 'SALE';
  static const String purchaseInvoicePrefix = 'PUR';
  static const String expensePrefix = 'EXP';

  // إعدادات التقارير
  static const List<String> reportTypes = [
    'تقرير المبيعات',
    'تقرير المشتريات',
    'تقرير المخزون',
    'تقرير المصروفات',
    'كشف حساب عميل',
    'كشف حساب مورد',
  ];

  // إعدادات التصدير
  static const List<String> exportFormats = ['PDF', 'Excel', 'CSV'];

  // رسائل النظام
  static const String noDataMessage = 'لا توجد بيانات للعرض';
  static const String loadingMessage = 'جاري التحميل...';
  static const String errorMessage = 'حدث خطأ غير متوقع';
  static const String successMessage = 'تم بنجاح';
  static const String deleteConfirmMessage = 'هل أنت متأكد من الحذف؟';
  static const String saveConfirmMessage = 'هل تريد حفظ التغييرات؟';

  // إعدادات التحقق من الصحة
  static const int minNameLength = 2;
  static const int maxNameLength = 100;
  static const int minPhoneLength = 10;
  static const int maxPhoneLength = 15;
  static const double minPrice = 0.01;
  static const double maxPrice = 999999.99;
  static const int minQuantity = 0;
  static const int maxQuantity = 999999;

  // أنواع المعاملات
  static const String transactionTypeSale = 'sale';
  static const String transactionTypePurchase = 'purchase';
  static const String transactionTypeExpense = 'expense';
  static const String transactionTypePayment = 'payment';
  static const String transactionTypeReceipt = 'receipt';

  // حالات الفواتير
  static const String invoiceStatusDraft = 'draft';
  static const String invoiceStatusPending = 'pending';
  static const String invoiceStatusCompleted = 'completed';
  static const String invoiceStatusCancelled = 'cancelled';

  // أنواع الدفع
  static const List<String> paymentMethods = [
    'نقداً',
    'بطاقة ائتمان',
    'تحويل بنكي',
    'شيك',
    'آجل',
  ];

  // فئات المصروفات الافتراضية
  static const List<String> defaultExpenseCategories = [
    'إيجار',
    'كهرباء',
    'مياه',
    'هاتف وإنترنت',
    'رواتب',
    'صيانة',
    'وقود',
    'مواد تنظيف',
    'أخرى',
  ];

  // وحدات القياس الافتراضية
  static const List<String> defaultUnits = [
    'قطعة',
    'كيلو',
    'جرام',
    'لتر',
    'متر',
    'علبة',
    'كرتون',
    'حبة',
  ];

  // إعدادات الأمان
  static const int maxLoginAttempts = 5;
  static const int sessionTimeoutMinutes = 30;

  // إعدادات النسخ الاحتياطي
  static const String backupFileExtension = '.backup';
  static const String backupDateFormat = 'yyyyMMdd_HHmmss';

  // مسارات الملفات
  static const String documentsFolder = 'documents';
  static const String reportsFolder = 'reports';
  static const String backupsFolder = 'backups';
  static const String imagesFolder = 'images';

  // إعدادات الشبكة (للمستقبل)
  static const int connectionTimeoutSeconds = 30;
  static const int receiveTimeoutSeconds = 30;

  // إعدادات الإشعارات
  static const String lowStockNotificationTitle = 'تنبيه مخزون منخفض';
  static const String lowStockNotificationBody =
      'يوجد منتجات تحتاج إعادة تخزين';

  // إعدادات الطباعة
  static const String defaultPrinterName = 'Default';
  static const String paperSizeA4 = 'A4';
  static const String paperSizeThermal = 'Thermal';

  // معرفات الإشعارات
  static const int dailyReportNotificationId = 1001;
  static const int weeklyReportNotificationId = 1002;
  static const int monthlyReportNotificationId = 1003;

  // مجلد التخزين المؤقت
  static const String cacheFolder = 'cache';
}
