import 'package:sqflite/sqflite.dart';
import 'database_service.dart';

/// خدمة العمليات المتقدمة لقاعدة البيانات
class DatabaseOperationsService {
  static final DatabaseOperationsService _instance = DatabaseOperationsService._internal();
  factory DatabaseOperationsService() => _instance;
  DatabaseOperationsService._internal();

  static DatabaseOperationsService get instance => _instance;

  // ==================== Internal Transfers Operations ====================

  /// Insert a new internal transfer record
  Future<int> insertInternalTransfer(Map<String, dynamic> transferMap) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int id = await db.insert('internal_transfers', transferMap);
      print('✅ Internal transfer inserted with ID: $id');
      return id;
    } catch (e) {
      print('❌ Error inserting internal transfer: $e');
      rethrow;
    }
  }

  /// Get all internal transfers with product information
  Future<List<Map<String, dynamic>>> getInternalTransfers() async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        ORDER BY it.transferDate DESC
      ''');
      print('📦 Retrieved ${transfers.length} internal transfers');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get internal transfers by date range
  Future<List<Map<String, dynamic>>> getInternalTransfersByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        WHERE it.transferDate BETWEEN ? AND ?
        ORDER BY it.transferDate DESC
      ''', <Object?>[startTimestamp, endTimestamp]);

      print('📦 Retrieved ${transfers.length} internal transfers for date range');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers by date range: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get internal transfers by product ID
  Future<List<Map<String, dynamic>>> getInternalTransfersByProduct(int productId) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        WHERE it.productId = ?
        ORDER BY it.transferDate DESC
      ''', <Object?>[productId]);

      print('📦 Retrieved ${transfers.length} internal transfers for product $productId');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers by product: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Update an existing internal transfer
  Future<int> updateInternalTransfer(Map<String, dynamic> transferMap) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int id = transferMap['id'] as int;
      final int rowsAffected = await db.update(
        'internal_transfers',
        transferMap,
        where: 'id = ?',
        whereArgs: <Object?>[id],
      );
      print('✅ Internal transfer updated: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error updating internal transfer: $e');
      rethrow;
    }
  }

  /// Delete an internal transfer
  Future<int> deleteInternalTransfer(int id) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int rowsAffected = await db.delete(
        'internal_transfers',
        where: 'id = ?',
        whereArgs: <Object?>[id],
      );
      print('✅ Internal transfer deleted: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error deleting internal transfer: $e');
      rethrow;
    }
  }

  /// Get total transferred quantity for a product
  Future<int> getTotalTransferredQuantity(int productId) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COALESCE(SUM(transferredQuantity), 0) as totalTransferred
        FROM internal_transfers
        WHERE productId = ?
      ''', <Object?>[productId]);

      final int totalTransferred = result.first['totalTransferred'] as int;
      print('📊 Total transferred quantity for product $productId: $totalTransferred');
      return totalTransferred;
    } catch (e) {
      print('❌ Error getting total transferred quantity: $e');
      return 0;
    }
  }

  // ==================== Store Inventory Adjustments Operations ====================

  /// Insert a new store inventory adjustment
  Future<int> insertStoreInventoryAdjustment(Map<String, dynamic> adjustmentMap) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int id = await db.insert('store_inventory_adjustments', adjustmentMap);
      print('✅ Store inventory adjustment inserted with ID: $id');
      return id;
    } catch (e) {
      print('❌ Error inserting store inventory adjustment: $e');
      rethrow;
    }
  }

  /// Get all store inventory adjustments
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustments() async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> adjustments = await db.rawQuery('''
        SELECT
          sia.*,
          p.name as productName,
          p.retailPrice
        FROM store_inventory_adjustments sia
        LEFT JOIN products p ON sia.productId = p.id
        ORDER BY sia.adjustmentDate DESC
      ''');
      print('📦 Retrieved ${adjustments.length} store inventory adjustments');
      return adjustments;
    } catch (e) {
      print('❌ Error getting store inventory adjustments: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get store inventory adjustments by date range
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustmentsByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> adjustments = await db.rawQuery('''
        SELECT
          sia.*,
          p.name as productName,
          p.retailPrice
        FROM store_inventory_adjustments sia
        LEFT JOIN products p ON sia.productId = p.id
        WHERE sia.adjustmentDate BETWEEN ? AND ?
        ORDER BY sia.adjustmentDate DESC
      ''', <Object?>[startTimestamp, endTimestamp]);

      print('📦 Retrieved ${adjustments.length} store inventory adjustments for date range');
      return adjustments;
    } catch (e) {
      print('❌ Error getting store inventory adjustments by date range: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Update an existing store inventory adjustment
  Future<int> updateStoreInventoryAdjustment(Map<String, dynamic> adjustmentMap) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int id = adjustmentMap['id'] as int;
      final int rowsAffected = await db.update(
        'store_inventory_adjustments',
        adjustmentMap,
        where: 'id = ?',
        whereArgs: <Object?>[id],
      );
      print('✅ Store inventory adjustment updated: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error updating store inventory adjustment: $e');
      rethrow;
    }
  }

  /// Delete a store inventory adjustment
  Future<int> deleteStoreInventoryAdjustment(int id) async {
    try {
      final Database db = await DatabaseService.instance.database;
      final int rowsAffected = await db.delete(
        'store_inventory_adjustments',
        where: 'id = ?',
        whereArgs: <Object?>[id],
      );
      print('✅ Store inventory adjustment deleted: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error deleting store inventory adjustment: $e');
      rethrow;
    }
  }

  // ==================== Analytics and Statistics Operations ====================

  /// Get internal transfers count
  Future<int> getInternalTransfersCount() async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COUNT(*) as count FROM internal_transfers
      ''');

      final int count = result.first['count'] as int;
      print('📊 Total internal transfers count: $count');
      return count;
    } catch (e) {
      print('❌ Error getting internal transfers count: $e');
      return 0;
    }
  }

  /// Get store inventory adjustments count
  Future<int> getStoreInventoryAdjustmentsCount() async {
    try {
      final Database db = await DatabaseService.instance.database;
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COUNT(*) as count FROM store_inventory_adjustments
      ''');

      final int count = result.first['count'] as int;
      print('📊 Total store inventory adjustments count: $count');
      return count;
    } catch (e) {
      print('❌ Error getting store inventory adjustments count: $e');
      return 0;
    }
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStatistics() async {
    try {
      final Database db = await DatabaseService.instance.database;
      
      final Map<String, int> stats = <String, int>{};
      
      // Count records in each table
      final List<String> tables = <String>[
        'products', 'customers', 'suppliers', 'sales', 'purchases', 
        'orders', 'expenses', 'internal_transfers', 'store_inventory_adjustments'
      ];
      
      for (final String table in tables) {
        try {
          final List<Map<String, dynamic>> result = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $table'
          );
          stats[table] = result.first['count'] as int;
        } catch (e) {
          print('⚠️ Error counting $table: $e');
          stats[table] = 0;
        }
      }
      
      print('📊 Database statistics retrieved: $stats');
      return stats;
    } catch (e) {
      print('❌ Error getting database statistics: $e');
      return <String, int>{};
    }
  }
}
