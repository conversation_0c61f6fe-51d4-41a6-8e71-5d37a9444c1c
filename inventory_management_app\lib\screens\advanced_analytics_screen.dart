import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';

class AdvancedAnalyticsScreen extends StatefulWidget {
  const AdvancedAnalyticsScreen({super.key});

  @override
  State<AdvancedAnalyticsScreen> createState() => _AdvancedAnalyticsScreenState();
}

class _AdvancedAnalyticsScreenState extends State<AdvancedAnalyticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'شهر';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('التحليلات المتقدمة'),
          backgroundColor: Colors.deepPurple,
          foregroundColor: Colors.white,
          actions: [
            PopupMenuButton<String>(
              icon: const Icon(Icons.date_range),
              onSelected: (value) {
                setState(() {
                  _selectedPeriod = value;
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'يوم', child: Text('اليوم')),
                const PopupMenuItem(value: 'أسبوع', child: Text('الأسبوع')),
                const PopupMenuItem(value: 'شهر', child: Text('الشهر')),
                const PopupMenuItem(value: 'سنة', child: Text('السنة')),
              ],
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            isScrollable: true,
            tabs: const [
              Tab(icon: Icon(Icons.trending_up), text: 'الأداء'),
              Tab(icon: Icon(Icons.pie_chart), text: 'التوزيع'),
              Tab(icon: Icon(Icons.timeline), text: 'الاتجاهات'),
              Tab(icon: Icon(Icons.insights), text: 'التوقعات'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildPerformanceTab(),
            _buildDistributionTab(),
            _buildTrendsTab(),
            _buildPredictionsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return Consumer3<ProductProvider, CustomerProvider, SaleProvider>(
      builder: (context, productProvider, customerProvider, saleProvider, child) {
        final sales = saleProvider.sales;
        final totalRevenue = sales.fold<double>(0.0, (sum, sale) => sum + (sale.total ?? 0));
        final averageOrderValue = sales.isNotEmpty ? totalRevenue / sales.length : 0.0;
        final topProducts = productProvider.products.take(5).toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // KPI Cards
              Row(
                children: [
                  Expanded(
                    child: _buildKPICard(
                      'إجمالي الإيرادات',
                      '${totalRevenue.toStringAsFixed(2)} ر.س',
                      Icons.attach_money,
                      Colors.green,
                      '+12.5%',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildKPICard(
                      'متوسط قيمة الطلب',
                      '${averageOrderValue.toStringAsFixed(2)} ر.س',
                      Icons.shopping_cart,
                      Colors.blue,
                      '+8.3%',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildKPICard(
                      'عدد المبيعات',
                      sales.length.toString(),
                      Icons.receipt,
                      Colors.orange,
                      '+15.2%',
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildKPICard(
                      'العملاء النشطون',
                      customerProvider.customers.length.toString(),
                      Icons.people,
                      Colors.purple,
                      '+5.7%',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Revenue Chart
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الإيرادات خلال $_selectedPeriod',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 200,
                        child: LineChart(
                          LineChartData(
                            gridData: const FlGridData(show: true),
                            titlesData: FlTitlesData(
                              leftTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 40,
                                  getTitlesWidget: (value, meta) {
                                    return Text('${value.toInt()}');
                                  },
                                ),
                              ),
                              bottomTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  getTitlesWidget: (value, meta) {
                                    return Text('${value.toInt()}');
                                  },
                                ),
                              ),
                              rightTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                              topTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                            ),
                            borderData: FlBorderData(show: true),
                            lineBarsData: [
                              LineChartBarData(
                                spots: _generateRevenueSpots(sales),
                                isCurved: true,
                                color: Colors.blue,
                                barWidth: 3,
                                dotData: const FlDotData(show: false),
                                belowBarData: BarAreaData(
                                  show: true,
                                  color: Colors.blue.withOpacity(0.1),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Top Products
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'أفضل المنتجات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...topProducts.asMap().entries.map((entry) {
                        final index = entry.key;
                        final product = entry.value;
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getProductColor(index),
                            child: Text('${index + 1}'),
                          ),
                          title: Text(product.name),
                          subtitle: Text('السعر: ${product.price?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                          trailing: Text(
                            'الكمية: ${product.quantity?.toStringAsFixed(0) ?? '0'}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDistributionTab() {
    return Consumer<SaleProvider>(
      builder: (context, saleProvider, child) {
        final sales = saleProvider.sales;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Sales Distribution Pie Chart
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'توزيع المبيعات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 300,
                        child: sales.isNotEmpty
                            ? PieChart(
                                PieChartData(
                                  sections: _buildSalesDistributionSections(sales),
                                  centerSpaceRadius: 60,
                                  sectionsSpace: 2,
                                ),
                              )
                            : const Center(
                                child: Text(
                                  'لا توجد بيانات مبيعات',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Customer Segments
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'شرائح العملاء',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 200,
                        child: BarChart(
                          BarChartData(
                            alignment: BarChartAlignment.spaceAround,
                            maxY: 100,
                            barTouchData: BarTouchData(enabled: false),
                            titlesData: FlTitlesData(
                              show: true,
                              bottomTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  getTitlesWidget: (value, meta) {
                                    switch (value.toInt()) {
                                      case 0: return const Text('جدد');
                                      case 1: return const Text('عاديون');
                                      case 2: return const Text('مميزون');
                                      case 3: return const Text('VIP');
                                      default: return const Text('');
                                    }
                                  },
                                ),
                              ),
                              leftTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 40,
                                  getTitlesWidget: (value, meta) {
                                    return Text('${value.toInt()}%');
                                  },
                                ),
                              ),
                              topTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                              rightTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                            ),
                            borderData: FlBorderData(show: false),
                            barGroups: [
                              BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: 30, color: Colors.blue)]),
                              BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: 45, color: Colors.green)]),
                              BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: 20, color: Colors.orange)]),
                              BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: 5, color: Colors.purple)]),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTrendsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.timeline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'تحليل الاتجاهات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قريباً - تحليل اتجاهات المبيعات والمخزون',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.insights, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'التوقعات الذكية',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'قريباً - توقعات المبيعات والطلب باستخدام الذكاء الاصطناعي',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color, String change) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    change,
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<FlSpot> _generateRevenueSpots(List sales) {
    if (sales.isEmpty) return [];
    
    final spots = <FlSpot>[];
    for (int i = 0; i < sales.length && i < 10; i++) {
      final sale = sales[i];
      spots.add(FlSpot(i.toDouble(), sale.total?.toDouble() ?? 0.0));
    }
    return spots;
  }

  List<PieChartSectionData> _buildSalesDistributionSections(List sales) {
    if (sales.isEmpty) return [];

    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.red];
    final sections = <PieChartSectionData>[];

    for (int i = 0; i < sales.length && i < 5; i++) {
      final sale = sales[i];
      final value = sale.total ?? 0.0;
      sections.add(
        PieChartSectionData(
          color: colors[i % colors.length],
          value: value,
          title: '${value.toStringAsFixed(0)} ر.س',
          radius: 80,
          titleStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return sections;
  }

  Color _getProductColor(int index) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.red];
    return colors[index % colors.length];
  }
}
