import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/app_constants.dart';
import '../utils/settings_helper.dart';

/// فئة مساعدة للتحقق من التحديثات
class UpdateHelper {
  static const String _updateCheckUrl = 'https://api.github.com/repos/your-repo/releases/latest';
  static const String _changelogUrl = 'https://raw.githubusercontent.com/your-repo/main/CHANGELOG.md';

  /// التحقق من وجود تحديث جديد
  static Future<UpdateInfo?> checkForUpdates() async {
    if (kIsWeb) {
      // في الويب، لا نحتاج للتحقق من التحديثات
      return null;
    }

    try {
      final response = await http.get(
        Uri.parse(_updateCheckUrl),
        headers: {
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': '${AppConstants.appName}/${AppConstants.appVersion}',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final latestVersion = data['tag_name'] as String?;
        final downloadUrl = _getDownloadUrl(data);
        final releaseNotes = data['body'] as String?;
        final publishedAt = DateTime.tryParse(data['published_at'] ?? '');

        if (latestVersion != null && _isNewerVersion(latestVersion)) {
          return UpdateInfo(
            version: latestVersion,
            downloadUrl: downloadUrl,
            releaseNotes: releaseNotes ?? '',
            publishedAt: publishedAt ?? DateTime.now(),
            isRequired: _isRequiredUpdate(latestVersion),
          );
        }
      }
    } catch (e) {
      // تجاهل أخطاء التحقق من التحديثات
      debugPrint('خطأ في التحقق من التحديثات: $e');
    }

    return null;
  }

  /// الحصول على رابط التحميل المناسب للمنصة
  static String? _getDownloadUrl(Map<String, dynamic> releaseData) {
    final assets = releaseData['assets'] as List?;
    if (assets == null || assets.isEmpty) return null;

    String? targetExtension;
    if (Platform.isWindows) {
      targetExtension = '.exe';
    } else if (Platform.isMacOS) {
      targetExtension = '.dmg';
    } else if (Platform.isLinux) {
      targetExtension = '.AppImage';
    } else if (Platform.isAndroid) {
      targetExtension = '.apk';
    }

    if (targetExtension == null) return null;

    for (final asset in assets) {
      final name = asset['name'] as String?;
      if (name != null && name.endsWith(targetExtension)) {
        return asset['browser_download_url'] as String?;
      }
    }

    return null;
  }

  /// التحقق من كون الإصدار أحدث
  static bool _isNewerVersion(String latestVersion) {
    final currentVersion = AppConstants.appVersion;
    return _compareVersions(latestVersion, currentVersion) > 0;
  }

  /// التحقق من كون التحديث مطلوب
  static bool _isRequiredUpdate(String latestVersion) {
    // يمكن تحديد التحديثات المطلوبة بناءً على قواعد معينة
    // مثلاً: إذا كان الإصدار الجديد يحتوي على إصلاحات أمنية مهمة
    final currentMajor = _getMajorVersion(AppConstants.appVersion);
    final latestMajor = _getMajorVersion(latestVersion);
    
    // إذا تغير الإصدار الرئيسي، فالتحديث مطلوب
    return latestMajor > currentMajor;
  }

  /// مقارنة الإصدارات
  static int _compareVersions(String version1, String version2) {
    final v1Parts = version1.replaceAll('v', '').split('.').map(int.parse).toList();
    final v2Parts = version2.replaceAll('v', '').split('.').map(int.parse).toList();

    final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;

    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }

  /// الحصول على رقم الإصدار الرئيسي
  static int _getMajorVersion(String version) {
    final parts = version.replaceAll('v', '').split('.');
    return parts.isNotEmpty ? int.tryParse(parts[0]) ?? 0 : 0;
  }

  /// الحصول على سجل التغييرات
  static Future<String?> getChangelog() async {
    try {
      final response = await http.get(
        Uri.parse(_changelogUrl),
        headers: {
          'User-Agent': '${AppConstants.appName}/${AppConstants.appVersion}',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return response.body;
      }
    } catch (e) {
      debugPrint('خطأ في جلب سجل التغييرات: $e');
    }

    return null;
  }

  /// حفظ معلومات آخر تحقق من التحديثات
  static Future<void> saveLastUpdateCheck() async {
    await SettingsHelper.prefs.setInt(
      'last_update_check',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// الحصول على تاريخ آخر تحقق من التحديثات
  static DateTime? getLastUpdateCheck() {
    final timestamp = SettingsHelper.prefs.getInt('last_update_check');
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  /// التحقق من ضرورة فحص التحديثات
  static bool shouldCheckForUpdates() {
    final lastCheck = getLastUpdateCheck();
    if (lastCheck == null) return true;

    // فحص التحديثات كل 24 ساعة
    return DateTime.now().difference(lastCheck).inHours >= 24;
  }

  /// تجاهل تحديث معين
  static Future<void> ignoreUpdate(String version) async {
    await SettingsHelper.prefs.setString('ignored_update', version);
  }

  /// التحقق من كون التحديث متجاهل
  static bool isUpdateIgnored(String version) {
    final ignoredVersion = SettingsHelper.prefs.getString('ignored_update');
    return ignoredVersion == version;
  }

  /// مسح التحديث المتجاهل
  static Future<void> clearIgnoredUpdate() async {
    await SettingsHelper.prefs.remove('ignored_update');
  }

  /// تفعيل/إلغاء التحقق التلقائي من التحديثات
  static Future<void> setAutoUpdateCheck(bool enabled) async {
    await SettingsHelper.prefs.setBool('auto_update_check', enabled);
  }

  /// التحقق من تفعيل التحقق التلقائي
  static bool isAutoUpdateCheckEnabled() {
    return SettingsHelper.prefs.getBool('auto_update_check') ?? true;
  }

  /// تحميل التحديث (للمنصات المدعومة)
  static Future<bool> downloadUpdate(String downloadUrl, String fileName) async {
    if (kIsWeb) return false;

    try {
      final response = await http.get(Uri.parse(downloadUrl));
      if (response.statusCode == 200) {
        final file = File(fileName);
        await file.writeAsBytes(response.bodyBytes);
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل التحديث: $e');
    }

    return false;
  }

  /// تثبيت التحديث (يتطلب صلاحيات خاصة)
  static Future<bool> installUpdate(String filePath) async {
    if (kIsWeb) return false;

    try {
      if (Platform.isWindows) {
        await Process.start(filePath, [], mode: ProcessStartMode.detached);
        return true;
      } else if (Platform.isMacOS) {
        await Process.start('open', [filePath], mode: ProcessStartMode.detached);
        return true;
      } else if (Platform.isLinux) {
        await Process.start('chmod', ['+x', filePath]);
        await Process.start(filePath, [], mode: ProcessStartMode.detached);
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في تثبيت التحديث: $e');
    }

    return false;
  }

  /// إعادة تشغيل التطبيق
  static Future<void> restartApp() async {
    if (kIsWeb) {
      // في الويب، يمكن إعادة تحميل الصفحة
      // window.location.reload();
    } else {
      // في التطبيقات المحلية، يتطلب إعادة تشغيل العملية
      exit(0);
    }
  }
}

/// معلومات التحديث
class UpdateInfo {
  final String version;
  final String? downloadUrl;
  final String releaseNotes;
  final DateTime publishedAt;
  final bool isRequired;

  UpdateInfo({
    required this.version,
    this.downloadUrl,
    required this.releaseNotes,
    required this.publishedAt,
    required this.isRequired,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'downloadUrl': downloadUrl,
      'releaseNotes': releaseNotes,
      'publishedAt': publishedAt.toIso8601String(),
      'isRequired': isRequired,
    };
  }

  /// إنشاء من JSON
  factory UpdateInfo.fromJson(Map<String, dynamic> json) {
    return UpdateInfo(
      version: json['version'],
      downloadUrl: json['downloadUrl'],
      releaseNotes: json['releaseNotes'] ?? '',
      publishedAt: DateTime.parse(json['publishedAt']),
      isRequired: json['isRequired'] ?? false,
    );
  }

  @override
  String toString() {
    return 'UpdateInfo(version: $version, isRequired: $isRequired)';
  }
}

/// حالة التحديث
enum UpdateStatus {
  checking,
  available,
  downloading,
  downloaded,
  installing,
  installed,
  failed,
  upToDate,
}

/// امتداد لحالة التحديث
extension UpdateStatusExtension on UpdateStatus {
  String get displayName {
    switch (this) {
      case UpdateStatus.checking:
        return 'جاري التحقق...';
      case UpdateStatus.available:
        return 'تحديث متاح';
      case UpdateStatus.downloading:
        return 'جاري التحميل...';
      case UpdateStatus.downloaded:
        return 'تم التحميل';
      case UpdateStatus.installing:
        return 'جاري التثبيت...';
      case UpdateStatus.installed:
        return 'تم التثبيت';
      case UpdateStatus.failed:
        return 'فشل التحديث';
      case UpdateStatus.upToDate:
        return 'التطبيق محدث';
    }
  }

  String get description {
    switch (this) {
      case UpdateStatus.checking:
        return 'جاري التحقق من وجود تحديثات جديدة';
      case UpdateStatus.available:
        return 'يوجد تحديث جديد متاح للتحميل';
      case UpdateStatus.downloading:
        return 'جاري تحميل التحديث';
      case UpdateStatus.downloaded:
        return 'تم تحميل التحديث بنجاح';
      case UpdateStatus.installing:
        return 'جاري تثبيت التحديث';
      case UpdateStatus.installed:
        return 'تم تثبيت التحديث بنجاح';
      case UpdateStatus.failed:
        return 'فشل في تحديث التطبيق';
      case UpdateStatus.upToDate:
        return 'التطبيق محدث إلى أحدث إصدار';
    }
  }
}
