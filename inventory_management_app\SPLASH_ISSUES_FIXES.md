# 🔧 إصلاح مشاكل شاشة Splash Screen والتشغيل

## **🚨 المشاكل المحتملة والحلول**

### **1. مشكلة عدم تشغيل Flutter**

#### **الأسباب المحتملة:**
- عدم تثبيت Flutter SDK بشكل صحيح
- مشاكل في متغيرات البيئة (PATH)
- عدم توفر جهاز متصل أو محاكي
- مشاكل في Android SDK

#### **الحلول:**
```bash
# 1. التحقق من تثبيت Flutter
flutter doctor

# 2. تنظيف المشروع
flutter clean
flutter pub get

# 3. التحقق من الأجهزة المتاحة
flutter devices

# 4. تشغيل التطبيق مع تحديد الجهاز
flutter run -d <device_id>

# 5. تشغيل على الويب (إذا كان متاحاً)
flutter run -d chrome
```

---

### **2. مشاكل التبعيات**

#### **التبعيات المطلوبة:**
```yaml
dependencies:
  flutter:
    sdk: flutter
  animated_text_kit: ^4.2.2
  flutter_spinkit: ^5.2.0
  shared_preferences: ^2.2.2
```

#### **إصلاح مشاكل التبعيات:**
```bash
# حذف pubspec.lock
rm pubspec.lock

# تنظيف وإعادة تحميل التبعيات
flutter clean
flutter pub get

# في حالة استمرار المشاكل
flutter pub deps
flutter pub upgrade
```

---

### **3. مشاكل الاستيراد**

#### **التحقق من الاستيرادات الصحيحة:**

**في main.dart:**
```dart
import 'package:inventory_management_app/screens/splash/modern_splash_screen.dart';
import 'package:inventory_management_app/config/app_design_constants.dart';
```

**في modern_splash_screen.dart:**
```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_design_constants.dart';
import '../onboarding/onboarding_screen.dart';
import '../enhanced_dashboard_screen.dart';
```

---

### **4. إصلاح مشاكل الملفات المفقودة**

#### **إنشاء ملف splash بديل مبسط:**

```dart
// lib/screens/splash/simple_splash_screen.dart
import 'package:flutter/material.dart';

class SimpleSplashScreen extends StatefulWidget {
  const SimpleSplashScreen({super.key});

  @override
  State<SimpleSplashScreen> createState() => _SimpleSplashScreenState();
}

class _SimpleSplashScreenState extends State<SimpleSplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToHome();
  }

  _navigateToHome() async {
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/home');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.store,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              'أسامة ماركت',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'إدارة ذكية لمتجرك',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 50),
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}
```

---

### **5. إصلاح مشاكل الأذونات**

#### **Android Manifest (android/app/src/main/AndroidManifest.xml):**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

---

### **6. إصلاح مشاكل الخطوط**

#### **التحقق من ملف pubspec.yaml:**
```yaml
flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
```

#### **إنشاء مجلد الخطوط إذا لم يكن موجود:**
```bash
mkdir -p assets/fonts
# ضع ملف Cairo-Regular.ttf في هذا المجلد
```

---

### **7. حلول بديلة للتشغيل**

#### **أ. تشغيل على الويب:**
```bash
flutter config --enable-web
flutter run -d chrome
```

#### **ب. تشغيل على محاكي Android:**
```bash
# بدء محاكي Android
emulator -avd <avd_name>

# تشغيل التطبيق
flutter run
```

#### **ج. تشغيل على جهاز فيزيائي:**
```bash
# تفعيل وضع المطور على الجهاز
# تفعيل USB Debugging
# توصيل الجهاز

flutter devices
flutter run -d <device_id>
```

---

### **8. اختبار التطبيق بدون تشغيل**

#### **فحص الأخطاء:**
```bash
# فحص الكود
flutter analyze

# فحص التبعيات
flutter pub deps

# بناء التطبيق بدون تشغيل
flutter build apk --debug
```

---

### **9. ملف main.dart مبسط للاختبار**

```dart
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'أسامة ماركت',
      debugShowCheckedModeBanner: false,
      home: const SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.store,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              'أسامة ماركت',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'إدارة ذكية لمتجرك',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 50),
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أسامة ماركت'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Text(
          'مرحباً بك في أسامة ماركت!',
          style: TextStyle(fontSize: 24),
        ),
      ),
    );
  }
}
```

---

## **🎯 خطوات الإصلاح المقترحة**

### **الخطوة 1: التحقق الأساسي**
```bash
flutter doctor
flutter devices
```

### **الخطوة 2: تنظيف المشروع**
```bash
flutter clean
flutter pub get
```

### **الخطوة 3: اختبار بسيط**
```bash
flutter run test_splash_simple.dart
```

### **الخطوة 4: إصلاح التبعيات**
- التأكد من وجود جميع التبعيات في pubspec.yaml
- حذف pubspec.lock وإعادة تشغيل pub get

### **الخطوة 5: اختبار على منصات مختلفة**
- Android
- iOS (إذا كان متاحاً)
- Web

---

## **📞 الدعم الإضافي**

إذا استمرت المشاكل، يمكن:
1. استخدام الملف المبسط أعلاه
2. إزالة التبعيات المعقدة مؤقتاً
3. اختبار على منصة مختلفة
4. التحقق من إعدادات IDE

**الهدف**: الحصول على شاشة سبلاش تعمل بشكل أساسي، ثم إضافة التحسينات تدريجياً.
