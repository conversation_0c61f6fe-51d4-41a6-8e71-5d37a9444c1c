class Product {
  int? id;
  String name;
  String description;
  double price;
  double? quantity;
  int? categoryId;
  int? unitId;
  int? supplierId;

  Product({
    this.id,
    required this.name,
    required this.description,
    required this.price,
    this.quantity,
    this.categoryId,
    this.unitId,
    this.supplierId,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'quantity': quantity,
      'categoryId': categoryId,
      'unitId': unitId,
      'supplierId': supplierId,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: map['price'],
      quantity: map['quantity'],
      categoryId: map['categoryId'],
      unitId: map['unitId'],
      supplierId: map['supplierId'],
    );
  }
}
