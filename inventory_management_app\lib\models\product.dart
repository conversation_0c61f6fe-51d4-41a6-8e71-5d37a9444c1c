class Product {
  int? id;
  String name;
  String description;
  double price;
  double? quantity;
  int? categoryId;
  int? unitId;
  int? supplierId;

  // خصائص إضافية مطلوبة
  String? category;
  String? unit;
  double? purchasePrice;
  double? salePrice;
  int? minLevel;
  String? barcode;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? date;

  Product({
    this.id,
    required this.name,
    required this.description,
    required this.price,
    this.quantity,
    this.categoryId,
    this.unitId,
    this.supplierId,
    this.category,
    this.unit,
    this.purchasePrice,
    this.salePrice,
    this.minLevel,
    this.barcode,
    this.createdAt,
    this.updatedAt,
    this.date,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'quantity': quantity,
      'categoryId': categoryId,
      'unitId': unitId,
      'supplierId': supplierId,
      'category': category,
      'unit': unit,
      'purchasePrice': purchasePrice,
      'salePrice': salePrice,
      'minLevel': minLevel,
      'barcode': barcode,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'date': date,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: map['price'],
      quantity: map['quantity'],
      categoryId: map['categoryId'],
      unitId: map['unitId'],
      supplierId: map['supplierId'],
      category: map['category'],
      unit: map['unit'],
      purchasePrice: map['purchasePrice'],
      salePrice: map['salePrice'],
      minLevel: map['minLevel'],
      barcode: map['barcode'],
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      date: map['date'],
    );
  }
}
