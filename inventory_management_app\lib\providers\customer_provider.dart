import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/services/customer_service.dart';
import 'package:contacts_service/contacts_service.dart';

/// Provider class for managing customer state and operations
class CustomerProvider extends ChangeNotifier {
  List<Customer> _customers = <Customer>[];
  final CustomerService _customerService = CustomerService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of customers
  List<Customer> get customers => _customers;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load customers from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة CustomerProvider...');
      await fetchCustomers();
      debugPrint('✅ تم تهيئة CustomerProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة CustomerProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل العملاء: $e';
      notifyListeners();
    }
  }

  /// Fetch all customers from the database
  Future<void> fetchCustomers() async {
    _setLoading(true);
    _clearError();

    try {
      _customers = await _customerService.getAllCustomers();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch customers: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new customer
  Future<Customer> addCustomer(Customer customer) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.insertCustomer(customer);
      await fetchCustomers();
      // Return the added customer (find it in the list)
      return _customers.where((c) => c.name == customer.name).first;
    } catch (e) {
      _setError('Failed to add customer: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing customer
  Future<void> updateCustomer(Customer customer) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.updateCustomer(customer);
      await fetchCustomers();
    } catch (e) {
      _setError('Failed to update customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a customer
  Future<void> deleteCustomer(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.deleteCustomer(id);
      await fetchCustomers();
    } catch (e) {
      _setError('Failed to delete customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search customers by name
  Future<void> searchCustomers(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _customers = await _customerService.searchCustomersByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search customers: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// مسح جميع العملاء
  void clearCustomers() {
    _customers.clear();
    notifyListeners();
  }

  /// استيراد العملاء من جهات الاتصال
  Future<Map<String, dynamic>> importCustomersFromContacts(List<Contact> contacts) async {
    _setLoading(true);
    _clearError();

    int importedCount = 0;
    int skippedCount = 0;
    List<String> errors = [];

    try {
      for (final Contact contact in contacts) {
        try {
          // التحقق من وجود اسم ورقم هاتف
          if (contact.displayName == null || contact.displayName!.isEmpty) {
            skippedCount++;
            continue;
          }

          final String name = contact.displayName!.trim();
          final String phone = contact.phones?.isNotEmpty == true
              ? _cleanPhoneNumber(contact.phones!.first.value ?? '')
              : '';

          // التحقق من عدم وجود عميل بنفس الاسم أو رقم الهاتف
          final bool nameExists = await _customerService.customerNameExists(name);
          final bool phoneExists = phone.isNotEmpty
              ? await _customerService.customerPhoneExists(phone)
              : false;

          if (nameExists || phoneExists) {
            skippedCount++;
            continue;
          }

          // إنشاء عميل جديد
          final Customer newCustomer = Customer(
            name: name,
            phone: phone.isNotEmpty ? phone : null,
            email: contact.emails?.isNotEmpty == true
                ? contact.emails!.first.value
                : null,
            address: contact.postalAddresses?.isNotEmpty == true
                ? _formatAddress(contact.postalAddresses!.first)
                : null,
          );

          // حفظ العميل في قاعدة البيانات
          await _customerService.insertCustomer(newCustomer);
          importedCount++;

        } catch (e) {
          errors.add('خطأ في استيراد ${contact.displayName ?? 'جهة اتصال'}: $e');
          skippedCount++;
        }
      }

      // إعادة تحميل قائمة العملاء
      await fetchCustomers();

      return {
        'success': true,
        'imported': importedCount,
        'skipped': skippedCount,
        'errors': errors,
      };

    } catch (e) {
      _setError('فشل في استيراد جهات الاتصال: $e');
      return {
        'success': false,
        'imported': importedCount,
        'skipped': skippedCount,
        'errors': [...errors, e.toString()],
      };
    } finally {
      _setLoading(false);
    }
  }

  /// تنظيف رقم الهاتف من الرموز غير المرغوبة
  String _cleanPhoneNumber(String phone) {
    // إزالة المسافات والرموز الخاصة
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // إزالة رمز البلد السعودي إذا كان موجوداً
    if (cleaned.startsWith('+966')) {
      cleaned = '0${cleaned.substring(4)}';
    } else if (cleaned.startsWith('966')) {
      cleaned = '0${cleaned.substring(3)}';
    }

    return cleaned;
  }

  /// تنسيق العنوان من جهة الاتصال
  String _formatAddress(PostalAddress address) {
    final List<String> addressParts = [];

    if (address.street?.isNotEmpty == true) {
      addressParts.add(address.street!);
    }
    if (address.city?.isNotEmpty == true) {
      addressParts.add(address.city!);
    }
    if (address.region?.isNotEmpty == true) {
      addressParts.add(address.region!);
    }
    if (address.country?.isNotEmpty == true) {
      addressParts.add(address.country!);
    }

    return addressParts.join(', ');
  }
}
