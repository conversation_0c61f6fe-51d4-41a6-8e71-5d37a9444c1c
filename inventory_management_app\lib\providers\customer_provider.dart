import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/services/customer_service.dart';

/// Provider class for managing customer state and operations
class CustomerProvider extends ChangeNotifier {
  List<Customer> _customers = <Customer>[];
  final CustomerService _customerService = CustomerService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of customers
  List<Customer> get customers => _customers;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load customers from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة CustomerProvider...');
      await fetchCustomers();
      debugPrint('✅ تم تهيئة CustomerProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة CustomerProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل العملاء: $e';
      notifyListeners();
    }
  }

  /// Fetch all customers from the database
  Future<void> fetchCustomers() async {
    _setLoading(true);
    _clearError();

    try {
      _customers = await _customerService.getAllCustomers();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch customers: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new customer
  Future<void> addCustomer(Customer customer) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.insertCustomer(customer);
      await fetchCustomers();
    } catch (e) {
      _setError('Failed to add customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing customer
  Future<void> updateCustomer(Customer customer) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.updateCustomer(customer);
      await fetchCustomers();
    } catch (e) {
      _setError('Failed to update customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a customer
  Future<void> deleteCustomer(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.deleteCustomer(id);
      await fetchCustomers();
    } catch (e) {
      _setError('Failed to delete customer: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search customers by name
  Future<void> searchCustomers(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _customers = await _customerService.searchCustomersByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search customers: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// مسح جميع العملاء
  void clearCustomers() {
    _customers.clear();
    notifyListeners();
  }
}
