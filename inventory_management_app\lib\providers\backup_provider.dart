import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import '../services/database_service.dart';

class BackupProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  String? _successMessage;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get successMessage => _successMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setSuccess(String? message) {
    _successMessage = message;
    notifyListeners();
  }

  void clearMessages() {
    _error = null;
    _successMessage = null;
    notifyListeners();
  }

  /// Create local backup of the database
  Future<bool> createLocalBackup() async {
    try {
      _setLoading(true);
      _setError(null);
      _setSuccess(null);

      // Get the database path
      final dbPath = await DatabaseService.instance.getDatabasePath();
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        _setError('ملف قاعدة البيانات غير موجود');
        return false;
      }

      // Get the downloads directory
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(directory.path, 'backups'));
      
      // Create backup directory if it doesn't exist
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Create backup filename with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
      final backupFileName = 'inventory_backup_$timestamp.db';
      final backupPath = path.join(backupDir.path, backupFileName);

      // Copy database file to backup location
      await dbFile.copy(backupPath);

      _setSuccess('تم إنشاء النسخة الاحتياطية بنجاح في:\n$backupPath');
      return true;

    } catch (e) {
      _setError('فشل في إنشاء النسخة الاحتياطية: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Restore database from backup file
  Future<bool> restoreLocalBackup() async {
    try {
      _setLoading(true);
      _setError(null);
      _setSuccess(null);

      // Pick backup file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['db'],
        dialogTitle: 'اختر ملف النسخة الاحتياطية',
      );

      if (result == null || result.files.single.path == null) {
        _setError('لم يتم اختيار ملف');
        return false;
      }

      final backupFile = File(result.files.single.path!);
      
      if (!await backupFile.exists()) {
        _setError('الملف المختار غير موجود');
        return false;
      }

      // Verify it's a valid database file (basic check)
      final fileSize = await backupFile.length();
      if (fileSize < 1024) { // Less than 1KB is probably not a valid DB
        _setError('الملف المختار لا يبدو كملف قاعدة بيانات صحيح');
        return false;
      }

      // Get current database path
      final dbPath = await DatabaseService.instance.getDatabasePath();
      
      // Close current database connection
      await DatabaseService.instance.resetDatabase();

      // Backup current database before replacing
      final currentDbFile = File(dbPath);
      if (await currentDbFile.exists()) {
        final tempBackupPath = '$dbPath.backup_${DateTime.now().millisecondsSinceEpoch}';
        await currentDbFile.copy(tempBackupPath);
      }

      // Copy backup file to database location
      await backupFile.copy(dbPath);

      // Reinitialize database
      await DatabaseService.instance.database;

      _setSuccess('تم استعادة النسخة الاحتياطية بنجاح');
      return true;

    } catch (e) {
      _setError('فشل في استعادة النسخة الاحتياطية: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get list of available local backups
  Future<List<FileSystemEntity>> getLocalBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(directory.path, 'backups'));
      
      if (!await backupDir.exists()) {
        return [];
      }

      final backups = await backupDir.list().where((entity) {
        return entity is File && entity.path.endsWith('.db');
      }).toList();

      // Sort by modification date (newest first)
      backups.sort((a, b) {
        final aStat = (a as File).lastModifiedSync();
        final bStat = (b as File).lastModifiedSync();
        return bStat.compareTo(aStat);
      });

      return backups;
    } catch (e) {
      return [];
    }
  }

  /// Delete a backup file
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get backup file info
  Map<String, dynamic> getBackupInfo(FileSystemEntity backup) {
    final file = backup as File;
    final fileName = path.basename(file.path);
    final lastModified = file.lastModifiedSync();
    final size = file.lengthSync();
    
    return {
      'name': fileName,
      'path': file.path,
      'lastModified': lastModified,
      'size': size,
      'sizeFormatted': _formatFileSize(size),
    };
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
