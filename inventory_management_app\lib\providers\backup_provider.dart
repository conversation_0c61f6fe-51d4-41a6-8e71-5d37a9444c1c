import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';
import 'package:provider/provider.dart';
import '../services/backup_service.dart';
import '../services/database_service.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/supplier_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/order_provider.dart';
import '../providers/expense_provider.dart';
import '../providers/activity_provider.dart';
import '../providers/customer_statement_provider.dart';
import '../providers/supplier_statement_provider.dart';
import '../providers/internal_transfer_provider.dart';
import '../providers/analytics_provider.dart';
import '../providers/store_inventory_provider.dart';

/// تردد النسخ الاحتياطي التلقائي
enum BackupFrequency {
  daily('يومي'),
  weekly('أسبوعي'),
  monthly('شهري');

  const BackupFrequency(this.displayName);
  final String displayName;
}

/// مزود النسخ الاحتياطي والاستعادة
class BackupProvider extends ChangeNotifier {
  final BackupService _backupService = BackupService();
  final DatabaseService _databaseService = DatabaseService();

  // حالة تسجيل الدخول
  GoogleSignInAccount? _currentUser;
  bool _isSignedIn = false;

  // قوائم النسخ الاحتياطية
  List<drive.File> _driveBackups = [];
  List<File> _localBackups = [];

  // حالة التحميل والأخطاء
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // إعدادات النسخ الاحتياطي التلقائي
  bool _autoBackupEnabled = false;
  BackupFrequency _autoBackupFrequency = BackupFrequency.weekly;
  bool _autoBackupToCloud = false;

  // إعدادات المزامنة
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  String? _lastSyncStatus;

  // Getters
  GoogleSignInAccount? get currentUser => _currentUser;
  bool get isSignedIn => _isSignedIn;
  List<drive.File> get driveBackups => _driveBackups;
  List<File> get localBackups => _localBackups;
  bool get isLoading => _isLoading;
  String? get error => _errorMessage;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  bool get autoBackupEnabled => _autoBackupEnabled;
  BackupFrequency get autoBackupFrequency => _autoBackupFrequency;
  bool get autoBackupToCloud => _autoBackupToCloud;
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get lastSyncStatus => _lastSyncStatus;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadSettings();
      _currentUser = _backupService.currentUser;
      _isSignedIn = _backupService.isSignedIn;

      if (_isSignedIn) {
        await refreshDriveBackups();
      }

      await refreshLocalBackups();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تهيئة مزود النسخ الاحتياطي: $e');
    }
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      _autoBackupEnabled = prefs.getBool('auto_backup_enabled') ?? false;
      _autoBackupToCloud = prefs.getBool('auto_backup_to_cloud') ?? false;

      final String frequencyString = prefs.getString('auto_backup_frequency') ?? 'weekly';
      _autoBackupFrequency = BackupFrequency.values.firstWhere(
        (freq) => freq.name == frequencyString,
        orElse: () => BackupFrequency.weekly,
      );

      // تحميل إعدادات المزامنة
      final String? lastSyncTimeString = prefs.getString('last_sync_time');
      if (lastSyncTimeString != null) {
        _lastSyncTime = DateTime.tryParse(lastSyncTimeString);
      }
      _lastSyncStatus = prefs.getString('last_sync_status');
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات النسخ الاحتياطي: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_backup_enabled', _autoBackupEnabled);
      await prefs.setBool('auto_backup_to_cloud', _autoBackupToCloud);
      await prefs.setString('auto_backup_frequency', _autoBackupFrequency.name);

      // حفظ إعدادات المزامنة
      if (_lastSyncTime != null) {
        await prefs.setString('last_sync_time', _lastSyncTime!.toIso8601String());
      }
      if (_lastSyncStatus != null) {
        await prefs.setString('last_sync_status', _lastSyncStatus!);
      }
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات النسخ الاحتياطي: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _setSuccess(String? message) {
    _successMessage = message;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }

  /// تسجيل الدخول إلى Google
  Future<void> signInGoogle() async {
    try {
      _setLoading(true);
      _clearMessages();

      final GoogleSignInAccount? account = await _backupService.signInWithGoogle();

      if (account != null) {
        _currentUser = account;
        _isSignedIn = true;
        _setSuccess('تم تسجيل الدخول بنجاح: ${account.email}');
        await refreshDriveBackups();
      } else {
        _setError('تم إلغاء تسجيل الدخول');
      }
    } catch (e) {
      _setError('فشل في تسجيل الدخول: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الخروج من Google
  Future<void> signOutGoogle() async {
    try {
      _setLoading(true);
      _clearMessages();

      await _backupService.signOutGoogle();
      _currentUser = null;
      _isSignedIn = false;
      _driveBackups.clear();

      _setSuccess('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('فشل في تسجيل الخروج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء نسخة احتياطية محلية
  Future<void> performLocalBackup() async {
    try {
      _setLoading(true);
      _clearMessages();

      final String backupPath = await _backupService.createLocalDatabaseBackup();
      await refreshLocalBackups();

      // تنظيف النسخ القديمة
      await _backupService.cleanOldLocalDatabaseBackups();

      _setSuccess('تم إنشاء النسخة الاحتياطية المحلية بنجاح');
      debugPrint('تم إنشاء النسخة الاحتياطية في: $backupPath');
    } catch (e) {
      _setError('فشل في إنشاء النسخة الاحتياطية المحلية: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create local backup of the database (legacy method for compatibility)
  Future<bool> createLocalBackup() async {
    await performLocalBackup();
    return _errorMessage == null;
  }

  /// استعادة من نسخة احتياطية محلية
  Future<void> restoreLocalBackup(File backupFile) async {
    try {
      _setLoading(true);
      _clearMessages();

      await _databaseService.restoreDatabaseFromFile(backupFile.path);
      _setSuccess('تم استعادة البيانات من النسخة الاحتياطية المحلية بنجاح');
    } catch (e) {
      _setError('فشل في استعادة النسخة الاحتياطية المحلية: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Restore database from backup file (legacy method for compatibility)
  Future<bool> restoreLocalBackup() async {
    try {
      _setLoading(true);
      _clearMessages();

      // This method is kept for compatibility but should use file picker
      _setError('يرجى استخدام قائمة النسخ الاحتياطية لاختيار ملف للاستعادة');
      return false;

    } catch (e) {
      _setError('فشل في استعادة النسخة الاحتياطية: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء نسخة احتياطية على Google Drive
  Future<void> performGoogleDriveBackup() async {
    try {
      if (!_isSignedIn) {
        throw Exception('يجب تسجيل الدخول إلى Google أولاً');
      }

      _setLoading(true);
      _clearMessages();

      final String? fileId = await _backupService.uploadDatabaseToGoogleDrive();

      if (fileId != null) {
        await refreshDriveBackups();
        _setSuccess('تم رفع النسخة الاحتياطية إلى Google Drive بنجاح');
      } else {
        throw Exception('فشل في رفع الملف');
      }
    } catch (e) {
      _setError('فشل في إنشاء النسخة الاحتياطية على Google Drive: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// استعادة من Google Drive
  Future<void> restoreFromGoogleDrive(drive.File driveFile) async {
    try {
      if (!_isSignedIn) {
        throw Exception('يجب تسجيل الدخول إلى Google أولاً');
      }

      _setLoading(true);
      _clearMessages();

      await _backupService.downloadDatabaseFromGoogleDrive(driveFile.id!);
      _setSuccess('تم استعادة البيانات من Google Drive بنجاح');
    } catch (e) {
      _setError('فشل في استعادة البيانات من Google Drive: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث قائمة النسخ الاحتياطية على Google Drive
  Future<void> refreshDriveBackups() async {
    try {
      if (!_isSignedIn) return;

      _driveBackups = await _backupService.listBackupFilesOnDrive();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قائمة النسخ الاحتياطية على Drive: $e');
    }
  }

  /// تحديث قائمة النسخ الاحتياطية المحلية
  Future<void> refreshLocalBackups() async {
    try {
      _localBackups = await _backupService.listLocalDatabaseBackups();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قائمة النسخ الاحتياطية المحلية: $e');
    }
  }

  /// حذف نسخة احتياطية من Google Drive
  Future<void> deleteFromGoogleDrive(String fileId) async {
    try {
      _setLoading(true);
      _clearMessages();

      await _backupService.deleteFileFromGoogleDrive(fileId);
      await refreshDriveBackups();

      _setSuccess('تم حذف النسخة الاحتياطية من Google Drive');
    } catch (e) {
      _setError('فشل في حذف النسخة الاحتياطية: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// حذف نسخة احتياطية محلية
  Future<void> deleteLocalBackup(File backupFile) async {
    try {
      await backupFile.delete();
      await refreshLocalBackups();
      _setSuccess('تم حذف النسخة الاحتياطية المحلية');
    } catch (e) {
      _setError('فشل في حذف النسخة الاحتياطية المحلية: $e');
    }
  }

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupEnabled(bool enabled) async {
    _autoBackupEnabled = enabled;
    await _saveSettings();

    if (enabled) {
      await _scheduleAutoBackup();
    } else {
      await _cancelAutoBackup();
    }

    notifyListeners();
  }

  /// تغيير تردد النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupFrequency(BackupFrequency frequency) async {
    _autoBackupFrequency = frequency;
    await _saveSettings();

    if (_autoBackupEnabled) {
      await _scheduleAutoBackup();
    }

    notifyListeners();
  }

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي للسحابة
  Future<void> setAutoBackupToCloud(bool enabled) async {
    _autoBackupToCloud = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// جدولة النسخ الاحتياطي التلقائي
  Future<void> _scheduleAutoBackup() async {
    try {
      // إلغاء الجدولة السابقة
      await Workmanager().cancelByUniqueName('auto_backup');

      // تحديد التردد بالدقائق
      Duration frequency;
      switch (_autoBackupFrequency) {
        case BackupFrequency.daily:
          frequency = const Duration(days: 1);
          break;
        case BackupFrequency.weekly:
          frequency = const Duration(days: 7);
          break;
        case BackupFrequency.monthly:
          frequency = const Duration(days: 30);
          break;
      }

      // جدولة المهمة
      await Workmanager().registerPeriodicTask(
        'auto_backup',
        'autoBackupTask',
        frequency: frequency,
        initialDelay: const Duration(minutes: 5),
        inputData: {
          'backup_to_cloud': _autoBackupToCloud,
        },
      );

      debugPrint('تم جدولة النسخ الاحتياطي التلقائي: ${_autoBackupFrequency.displayName}');
    } catch (e) {
      debugPrint('خطأ في جدولة النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// إلغاء النسخ الاحتياطي التلقائي
  Future<void> _cancelAutoBackup() async {
    try {
      await Workmanager().cancelByUniqueName('auto_backup');
      debugPrint('تم إلغاء النسخ الاحتياطي التلقائي');
    } catch (e) {
      debugPrint('خطأ في إلغاء النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// Get list of available local backups (legacy method for compatibility)
  Future<List<FileSystemEntity>> getLocalBackups() async {
    await refreshLocalBackups();
    return _localBackups.cast<FileSystemEntity>();
  }

  /// Delete a backup file (legacy method for compatibility)
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      await deleteLocalBackup(file);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get backup file info
  Map<String, dynamic> getBackupInfo(FileSystemEntity backup) {
    final file = backup as File;
    final fileName = file.path.split('/').last;
    final lastModified = file.lastModifiedSync();
    final size = file.lengthSync();

    return {
      'name': fileName,
      'path': file.path,
      'lastModified': lastModified,
      'size': size,
      'sizeFormatted': _backupService.formatFileSize(size),
    };
  }

  /// Get drive file info
  Map<String, dynamic> getDriveFileInfo(drive.File driveFile) {
    return {
      'name': driveFile.name ?? 'Unknown',
      'id': driveFile.id ?? '',
      'modifiedTime': driveFile.modifiedTime,
      'size': driveFile.size != null ? int.parse(driveFile.size!) : 0,
      'sizeFormatted': driveFile.size != null
          ? _backupService.formatFileSize(int.parse(driveFile.size!))
          : 'Unknown',
    };
  }

  // ==================== المزامنة عبر Google Drive ====================

  /// تنفيذ المزامنة مع Google Drive
  Future<void> performSync(BuildContext context) async {
    try {
      // التحقق من تسجيل الدخول
      if (!_isSignedIn) {
        _setError('يجب تسجيل الدخول إلى Google Drive أولاً');
        return;
      }

      _isSyncing = true;
      _setLoading(true);
      _clearMessages();
      notifyListeners();

      debugPrint('🔄 بدء عملية المزامنة...');

      // جلب قائمة النسخ الاحتياطية من Google Drive
      await refreshDriveBackups();

      if (_driveBackups.isEmpty) {
        _setError('لا توجد نسخ احتياطية على Google Drive للمزامنة معها');
        _lastSyncStatus = 'فشل - لا توجد نسخ احتياطية';
        await _saveSettings();
        return;
      }

      // تحديد أحدث نسخة احتياطية
      final drive.File latestBackup = _findLatestBackup(_driveBackups);
      final Map<String, dynamic> backupInfo = getDriveFileInfo(latestBackup);

      debugPrint('📁 أحدث نسخة احتياطية: ${backupInfo['name']}');

      // عرض حوار التأكيد
      final bool? confirmed = await _showSyncConfirmationDialog(context, backupInfo);

      if (confirmed != true) {
        _setError('تم إلغاء المزامنة من قبل المستخدم');
        _lastSyncStatus = 'ملغاة';
        await _saveSettings();
        return;
      }

      // تنزيل واستعادة النسخة الاحتياطية
      await _backupService.downloadDatabaseFromGoogleDrive(latestBackup.id!);

      // إعادة تحميل جميع البيانات في التطبيق
      await _reloadAllProviders(context);

      // تحديث حالة المزامنة
      _lastSyncTime = DateTime.now();
      _lastSyncStatus = 'نجحت';
      await _saveSettings();

      _setSuccess('تم إجراء المزامنة بنجاح مع النسخة الاحتياطية: ${backupInfo['name']}');
      debugPrint('✅ تمت المزامنة بنجاح');

    } catch (e) {
      _lastSyncStatus = 'فشلت: $e';
      await _saveSettings();
      _setError('فشل في المزامنة: $e');
      debugPrint('❌ خطأ في المزامنة: $e');
    } finally {
      _isSyncing = false;
      _setLoading(false);
      notifyListeners();
    }
  }

  /// تحديد أحدث نسخة احتياطية من القائمة
  drive.File _findLatestBackup(List<drive.File> backups) {
    drive.File latest = backups.first;

    for (final backup in backups) {
      final DateTime? backupTime = backup.modifiedTime;
      final DateTime? latestTime = latest.modifiedTime;

      if (backupTime != null && latestTime != null) {
        if (backupTime.isAfter(latestTime)) {
          latest = backup;
        }
      }
    }

    return latest;
  }

  /// عرض حوار تأكيد المزامنة
  Future<bool?> _showSyncConfirmationDialog(BuildContext context, Map<String, dynamic> backupInfo) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange, size: 28),
                SizedBox(width: 12),
                Text('تأكيد المزامنة'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تحذير: المزامنة ستحذف جميع البيانات الحالية على هذا الجهاز وتستبدلها ببيانات النسخة الاحتياطية الأحدث من Google Drive.',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 16),
                const Text('معلومات النسخة الاحتياطية الأحدث:'),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('الاسم: ${backupInfo['name']}'),
                      Text('الحجم: ${backupInfo['sizeFormatted']}'),
                      if (backupInfo['modifiedTime'] != null)
                        Text('التاريخ: ${_formatDateTime(backupInfo['modifiedTime'])}'),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'هل أنت متأكد من رغبتك في المتابعة؟',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تأكيد المزامنة'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// إعادة تحميل جميع البيانات في التطبيق بعد المزامنة
  Future<void> _reloadAllProviders(BuildContext context) async {
    try {
      debugPrint('🔄 إعادة تحميل جميع البيانات...');

      // الحصول على جميع المزودات من السياق
      final productProvider = Provider.of<ProductProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(context, listen: false);
      final supplierProvider = Provider.of<SupplierProvider>(context, listen: false);
      final saleProvider = Provider.of<SaleProvider>(context, listen: false);
      final purchaseProvider = Provider.of<PurchaseProvider>(context, listen: false);
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
      final activityProvider = Provider.of<ActivityProvider>(context, listen: false);
      final customerStatementProvider = Provider.of<CustomerStatementProvider>(context, listen: false);
      final supplierStatementProvider = Provider.of<SupplierStatementProvider>(context, listen: false);
      final internalTransferProvider = Provider.of<InternalTransferProvider>(context, listen: false);
      final analyticsProvider = Provider.of<AnalyticsProvider>(context, listen: false);
      final storeInventoryProvider = Provider.of<StoreInventoryProvider>(context, listen: false);

      // إعادة تحميل البيانات لكل مزود
      await Future.wait([
        productProvider.fetchProducts(),
        customerProvider.fetchCustomers(),
        supplierProvider.fetchSuppliers(),
        saleProvider.fetchSales(),
        purchaseProvider.fetchPurchases(),
        orderProvider.fetchOrders(),
        expenseProvider.fetchExpenses(),
        activityProvider.fetchActivities(),
        customerStatementProvider.refreshStatements(),
        supplierStatementProvider.refreshStatements(),
        internalTransferProvider.fetchTransfers(),
        analyticsProvider.refreshAnalytics(),
        storeInventoryProvider.fetchAdjustments(),
      ]);

      debugPrint('✅ تم إعادة تحميل جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
      // لا نرمي الخطأ هنا لأن المزامنة نجحت، فقط إعادة التحميل فشلت
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على نص حالة المزامنة الأخيرة
  String get lastSyncStatusText {
    if (_lastSyncTime == null) {
      return 'لم يتم إجراء مزامنة من قبل';
    }

    final String timeText = _formatDateTime(_lastSyncTime!);
    final String statusText = _lastSyncStatus ?? 'غير معروف';

    return 'آخر مزامنة: $timeText ($statusText)';
  }

  /// التحقق من إمكانية إجراء المزامنة
  bool get canSync {
    return _isSignedIn && !_isLoading && !_isSyncing;
  }

  /// إعادة تعيين حالة المزامنة
  void resetSyncStatus() {
    _lastSyncTime = null;
    _lastSyncStatus = null;
    _saveSettings();
    notifyListeners();
  }
}
