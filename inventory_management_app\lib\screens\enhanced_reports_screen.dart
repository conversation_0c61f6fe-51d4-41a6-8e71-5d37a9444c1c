import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';

class EnhancedReportsScreen extends StatefulWidget {
  const EnhancedReportsScreen({super.key});

  @override
  State<EnhancedReportsScreen> createState() => _EnhancedReportsScreenState();
}

class _EnhancedReportsScreenState extends State<EnhancedReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('التقارير والتحليلات'),
          backgroundColor: Colors.indigo,
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            tabs: const [
              Tab(icon: Icon(Icons.bar_chart), text: 'المبيعات'),
              Tab(icon: Icon(Icons.inventory), text: 'المخزون'),
              Tab(icon: Icon(Icons.people), text: 'العملاء'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildSalesReports(),
            _buildInventoryReports(),
            _buildCustomerReports(),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesReports() {
    return Consumer<SaleProvider>(
      builder: (context, saleProvider, child) {
        final sales = saleProvider.sales;
        final totalSales = sales.fold<double>(0.0, (sum, sale) => sum + (sale.total ?? 0));
        final averageSale = sales.isNotEmpty ? totalSales / sales.length : 0.0;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات سريعة
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي المبيعات',
                      '${totalSales.toStringAsFixed(2)} ر.س',
                      Icons.attach_money,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'عدد المبيعات',
                      sales.length.toString(),
                      Icons.receipt,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'متوسط البيع',
                      '${averageSale.toStringAsFixed(2)} ر.س',
                      Icons.trending_up,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'أعلى بيع',
                      sales.isNotEmpty 
                          ? '${sales.map((s) => s.total ?? 0).reduce((a, b) => a > b ? a : b).toStringAsFixed(2)} ر.س'
                          : '0.00 ر.س',
                      Icons.star,
                      Colors.purple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // رسم بياني للمبيعات
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'توزيع المبيعات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 200,
                        child: sales.isNotEmpty
                            ? PieChart(
                                PieChartData(
                                  sections: _buildSalesPieChartSections(sales),
                                  centerSpaceRadius: 40,
                                  sectionsSpace: 2,
                                ),
                              )
                            : const Center(
                                child: Text(
                                  'لا توجد بيانات مبيعات',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInventoryReports() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final products = productProvider.products;
        final totalProducts = products.length;
        final totalValue = products.fold<double>(
          0.0,
          (sum, product) => sum + ((product.price ?? 0) * (product.quantity ?? 0)),
        );
        final lowStockProducts = products.where((p) => (p.quantity ?? 0) < 10).length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات المخزون
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي المنتجات',
                      totalProducts.toString(),
                      Icons.inventory_2,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'قيمة المخزون',
                      '${totalValue.toStringAsFixed(2)} ر.س',
                      Icons.attach_money,
                      Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'مخزون منخفض',
                      lowStockProducts.toString(),
                      Icons.warning,
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'متوسط السعر',
                      products.isNotEmpty
                          ? '${(products.fold<double>(0.0, (sum, p) => sum + (p.price ?? 0)) / products.length).toStringAsFixed(2)} ر.س'
                          : '0.00 ر.س',
                      Icons.calculate,
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // قائمة المنتجات منخفضة المخزون
              if (lowStockProducts > 0)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.warning, color: Colors.red),
                            const SizedBox(width: 8),
                            const Text(
                              'منتجات تحتاج إعادة تخزين',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ...products
                            .where((p) => (p.quantity ?? 0) < 10)
                            .take(5)
                            .map((product) => ListTile(
                                  leading: const Icon(Icons.inventory_2, color: Colors.red),
                                  title: Text(product.name),
                                  subtitle: Text('الكمية المتبقية: ${product.quantity ?? 0}'),
                                  trailing: Text(
                                    '${product.price?.toStringAsFixed(2) ?? '0.00'} ر.س',
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                )),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCustomerReports() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        final customers = customerProvider.customers;
        final totalCustomers = customers.length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات العملاء
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي العملاء',
                      totalCustomers.toString(),
                      Icons.people,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'عملاء جدد',
                      '${(totalCustomers * 0.2).round()}', // تقدير
                      Icons.person_add,
                      Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // قائمة العملاء
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'قائمة العملاء',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (customers.isEmpty)
                        const Center(
                          child: Text(
                            'لا يوجد عملاء',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        )
                      else
                        ...customers.take(10).map((customer) => ListTile(
                              leading: const CircleAvatar(
                                backgroundColor: Colors.blue,
                                child: Icon(Icons.person, color: Colors.white),
                              ),
                              title: Text(customer.name),
                              subtitle: Text(customer.email ?? customer.phone ?? 'لا يوجد معلومات اتصال'),
                              trailing: const Icon(Icons.arrow_forward_ios),
                            )),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildSalesPieChartSections(List sales) {
    if (sales.isEmpty) return [];

    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.red];
    final sections = <PieChartSectionData>[];

    for (int i = 0; i < sales.length && i < 5; i++) {
      final sale = sales[i];
      final value = sale.total ?? 0.0;
      sections.add(
        PieChartSectionData(
          color: colors[i % colors.length],
          value: value,
          title: '${value.toStringAsFixed(0)} ر.س',
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return sections;
  }
}
