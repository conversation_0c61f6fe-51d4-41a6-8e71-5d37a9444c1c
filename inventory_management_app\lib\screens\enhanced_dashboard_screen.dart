import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';
import 'enhanced_reports_screen.dart';
import 'advanced_settings_screen.dart';
import 'advanced_analytics_screen.dart';
import 'mobile_features_screen.dart';
import 'advanced_search_screen.dart';
import 'gamification_screen.dart';
import 'user_management_screen.dart';
import 'support_screen.dart';
// استيراد الشاشات الجديدة
import 'invoices/create_sale_invoice_screen.dart';
import 'invoices/create_purchase_invoice_screen.dart';
import 'orders/create_shop_order_screen.dart';
import 'activity/activity_list_screen.dart';
import 'invoices/sale_invoice_list_screen.dart';
import 'invoices/purchase_invoice_list_screen.dart';
import 'orders/order_list_screen.dart';
import 'expenses/expense_list_screen.dart';
import 'statements/customer_statement_screen.dart';
import 'inventory/internal_transfer_screen.dart';
import 'statements/supplier_statement_screen.dart';
import 'analytics/analytics_screen.dart';
import 'simple_settings_screen.dart';
import 'backup/backup_dialog.dart';
import 'inventory/store_inventory_adjustment_screen.dart';

class EnhancedDashboardScreen extends StatelessWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white, // خلفية بيضاء نقية
        appBar: AppBar(
          title: const Text('لوحة التحكم'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        drawer: _buildDrawer(context),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ترحيب
              _buildWelcomeCard(),

              const SizedBox(height: 24),

              // أزرار الوصول السريع الجديدة
              _buildQuickAccessButtons(context),


            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[400]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مرحباً بك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'نظام إدارة المخزون',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اليوم: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  /// أزرار الوصول السريع الجديدة
  Widget _buildQuickAccessButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // الصف الأول: بيع + توريد
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'فاتورة بيع جديدة',
                icon: Icons.shopping_cart,
                color: Colors.blue,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateSaleInvoiceScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'فاتورة توريد جديدة',
                icon: Icons.local_shipping,
                color: Colors.green,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreatePurchaseInvoiceScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثاني: طلبية + قائمة الأنشطة
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'تسجيل طلبية للمحل',
                icon: Icons.inventory,
                color: Colors.orange,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateShopOrderScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'قائمة الأنشطة',
                icon: Icons.list_alt,
                color: Colors.purple,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ActivityListScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الثالث: فواتير البيع + فواتير التوريد
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'فواتير البيع',
                icon: Icons.receipt_long,
                color: Colors.teal,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SaleInvoiceListScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'فواتير التوريد',
                icon: Icons.assignment_turned_in,
                color: Colors.indigo,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PurchaseInvoiceListScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الرابع: الطلبيات السابقة + تحويل المخزون
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'الطلبيات السابقة',
                icon: Icons.history,
                color: Colors.brown,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const OrderListScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'تحويل المخزون',
                icon: Icons.swap_horiz,
                color: Colors.purple,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const InternalTransferScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // الصف الخامس: المصروفات
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessButton(
                context: context,
                title: 'المصروفات',
                icon: Icons.money_off,
                color: Colors.red,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ExpenseListScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(child: SizedBox()), // مساحة فارغة
          ],
        ),
      ],
    );
  }

  /// بناء زر الوصول السريع
  Widget _buildQuickAccessButton({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        elevation: 2,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 28),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء الـ Drawer الجانبي
  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Header
          _buildDrawerHeader(),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // التنقل الرئيسي
                _buildDrawerSection('التنقل الرئيسي', [
                  _buildDrawerItem(
                    icon: Icons.home,
                    title: 'الرئيسية',
                    onTap: () => Navigator.pop(context),
                  ),
                  _buildDrawerItem(
                    icon: Icons.inventory,
                    title: 'المنتجات',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to products
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.people,
                    title: 'العملاء',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to customers
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.local_shipping,
                    title: 'الموردون',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to suppliers
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.swap_horiz,
                    title: 'تحويل المخزون',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const InternalTransferScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.inventory_2,
                    title: 'جرد مخزون البقالة',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const StoreInventoryAdjustmentScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // التقارير والكشوفات
                _buildDrawerSection('التقارير والكشوفات', [
                  _buildDrawerItem(
                    icon: Icons.people_alt,
                    title: 'كشف العملاء',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CustomerStatementScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.business_center,
                    title: 'كشف الموردين',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SupplierStatementScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.analytics,
                    title: 'الإحصائيات والتقارير',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AnalyticsScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // الأدوات والإعدادات
                _buildDrawerSection('الأدوات والإعدادات', [
                  _buildDrawerItem(
                    icon: Icons.backup,
                    title: 'النسخ الاحتياطي',
                    onTap: () {
                      Navigator.pop(context);
                      showDialog(
                        context: context,
                        builder: (context) => const BackupDialog(),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'الإعدادات',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SimpleSettingsScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // تسجيل الخروج
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: 'تسجيل الخروج',
                  textColor: Colors.red,
                  onTap: () => _showLogoutDialog(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[400]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
      ),
      child: const SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white,
                child: Icon(
                  Icons.store,
                  size: 35,
                  color: Colors.blue,
                ),
              ),
              SizedBox(height: 12),
              Text(
                'نظام إدارة المخزون',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'الإصدار 1.0',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              letterSpacing: 1.2,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Colors.grey[700],
        size: 22,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          color: textColor ?? Colors.grey[800],
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      hoverColor: Colors.grey[100],
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // TODO: Implement logout functionality
      // For now, just show a message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
