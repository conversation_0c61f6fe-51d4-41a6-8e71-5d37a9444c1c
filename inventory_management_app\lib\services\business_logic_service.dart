import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/category_service.dart';
import 'package:inventory_management_app/services/customer_service.dart';
import 'package:inventory_management_app/services/supplier_service.dart';
import 'package:inventory_management_app/services/sale_service.dart';
import 'package:inventory_management_app/services/purchase_service.dart';

/// Service for handling business logic and validation rules
class BusinessLogicService {
  final ProductService _productService = ProductService();
  final CategoryService _categoryService = CategoryService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();
  final SaleService _saleService = SaleService();
  final PurchaseService _purchaseService = PurchaseService();

  /// Check if a category can be deleted (no products using it)
  Future<bool> canDeleteCategory(int categoryId) async {
    final List<Product> products =
        await _productService.getProductsByCategory(categoryId);
    return products.isEmpty;
  }

  /// Check if a supplier can be deleted (no products or purchases)
  Future<bool> canDeleteSupplier(int supplierId) async {
    final List<Product> products =
        await _productService.getProductsBySupplier(supplierId);
    final List<Purchase> purchases =
        await _purchaseService.getPurchasesBySupplier(supplierId);
    return products.isEmpty && purchases.isEmpty;
  }

  /// Check if a customer can be deleted (no sales or orders)
  Future<bool> canDeleteCustomer(int customerId) async {
    final List<Sale> sales = await _saleService.getSalesByCustomer(customerId);
    return sales.isEmpty;
  }

  /// Validate product name uniqueness within category
  Future<bool> isProductNameUniqueInCategory(String name, int? categoryId,
      {int? excludeProductId}) async {
    final List<Product> products = categoryId != null
        ? await _productService.getProductsByCategory(categoryId)
        : await _productService.getAllProducts();

    return !products.any((Product product) =>
        product.name.toLowerCase() == name.toLowerCase() &&
        product.id != excludeProductId);
  }

  /// Validate email uniqueness for customers
  Future<bool> isCustomerEmailUnique(String email,
      {int? excludeCustomerId}) async {
    return !await _customerService.customerEmailExists(email,
        excludeId: excludeCustomerId);
  }

  /// Validate email uniqueness for suppliers
  Future<bool> isSupplierEmailUnique(String email,
      {int? excludeSupplierId}) async {
    return !await _supplierService.supplierEmailExists(email,
        excludeId: excludeSupplierId);
  }

  /// Check if product has sufficient stock for sale
  Future<bool> hasEnoughStock(int productId, double requestedQuantity) async {
    final Product? product = await _productService.getProductById(productId);
    if (product == null) return false;
    return (product.quantity ?? 0) >= requestedQuantity;
  }

  /// Update product stock after sale
  Future<void> updateStockAfterSale(int productId, double soldQuantity) async {
    final Product? product = await _productService.getProductById(productId);
    if (product != null) {
      final double newQuantity = (product.quantity ?? 0) - soldQuantity;
      await _productService.updateProductQuantity(productId, newQuantity);
    }
  }

  /// Update product stock after purchase
  Future<void> updateStockAfterPurchase(
      int productId, double purchasedQuantity) async {
    final Product? product = await _productService.getProductById(productId);
    if (product != null) {
      final double newQuantity = (product.quantity ?? 0) + purchasedQuantity;
      await _productService.updateProductQuantity(productId, newQuantity);
    }
  }

  /// Get low stock products (below threshold)
  Future<List<Map<String, dynamic>>> getLowStockReport(double threshold) async {
    final List<Product> lowStockProducts =
        await _productService.getLowStockProducts(threshold);
    return lowStockProducts
        .map((Product product) => <String, Object?>{
              'id': product.id,
              'name': product.name,
              'currentStock': product.quantity ?? 0,
              'threshold': threshold,
              'status': 'Low Stock',
            })
        .toList();
  }

  /// Validate business rules before deleting entity
  Future<Map<String, dynamic>> validateDeletion(
      String entityType, int entityId) async {
    switch (entityType.toLowerCase()) {
      case 'category':
        final bool canDelete = await canDeleteCategory(entityId);
        return <String, dynamic>{
          'canDelete': canDelete,
          'reason':
              canDelete ? null : 'Category has products associated with it',
        };
      case 'supplier':
        final bool canDelete = await canDeleteSupplier(entityId);
        return <String, dynamic>{
          'canDelete': canDelete,
          'reason': canDelete ? null : 'Supplier has products or purchases',
        };
      case 'customer':
        final bool canDelete = await canDeleteCustomer(entityId);
        return <String, dynamic>{
          'canDelete': canDelete,
          'reason': canDelete ? null : 'Customer has sales history',
        };
      default:
        return <String, dynamic>{'canDelete': true, 'reason': null};
    }
  }
}
