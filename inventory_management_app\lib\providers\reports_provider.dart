import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/sale_service.dart';
import 'package:inventory_management_app/services/purchase_service.dart';
import 'package:inventory_management_app/services/expense_service.dart';
import 'package:inventory_management_app/services/customer_service.dart';
import 'package:inventory_management_app/services/supplier_service.dart';

/// Provider class for managing reports and analytics
class ReportsProvider extends ChangeNotifier {
  final ProductService _productService = ProductService();
  final SaleService _saleService = SaleService();
  final PurchaseService _purchaseService = PurchaseService();
  final ExpenseService _expenseService = ExpenseService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();

  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _dashboardData = <String, dynamic>{};
  List<Map<String, dynamic>> _salesReport = <Map<String, dynamic>>[];
  List<Map<String, dynamic>> _purchasesReport = <Map<String, dynamic>>[];
  List<Map<String, dynamic>> _lowStockReport = <Map<String, dynamic>>[];

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Get dashboard data
  Map<String, dynamic> get dashboardData => _dashboardData;

  /// Get sales report
  List<Map<String, dynamic>> get salesReport => _salesReport;

  /// Get purchases report
  List<Map<String, dynamic>> get purchasesReport => _purchasesReport;

  /// Get low stock report
  List<Map<String, dynamic>> get lowStockReport => _lowStockReport;

  /// Load dashboard summary data
  Future<void> loadDashboardData() async {
    _setLoading(true);
    _clearError();

    try {
      final List<num> results = await Future.wait(<Future<num>>[
        _productService.getProductCount(),
        _customerService.getCustomerCount(),
        _supplierService.getSupplierCount(),
        _saleService.getTotalSalesAmount(),
        _purchaseService.getTotalPurchasesAmount(),
        _expenseService.getTotalExpensesAmount(),
        _saleService.getSaleCount(),
        _purchaseService.getPurchaseCount(),
      ]);

      _dashboardData = <String, dynamic>{
        'totalProducts': results[0],
        'totalCustomers': results[1],
        'totalSuppliers': results[2],
        'totalSales': results[3],
        'totalPurchases': results[4],
        'totalExpenses': results[5],
        'salesCount': results[6],
        'purchasesCount': results[7],
        'netProfit': (results[3] as double) -
            (results[4] as double) -
            (results[5] as double),
      };

      notifyListeners();
    } catch (e) {
      _setError('Failed to load dashboard data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load sales report by date range
  Future<void> loadSalesReport(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Sale> sales =
          await _saleService.getSalesByDateRange(startDate, endDate);
      _salesReport = sales
          .map((Sale sale) => <String, Object?>{
                'id': sale.id,
                'customerId': sale.customerId,
                'date': sale.date,
                'total': sale.total ?? 0.0,
                'notes': sale.notes,
              })
          .toList();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load sales report: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load purchases report by date range
  Future<void> loadPurchasesReport(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Purchase> purchases =
          await _purchaseService.getPurchasesByDateRange(startDate, endDate);
      _purchasesReport = purchases
          .map((Purchase purchase) => <String, dynamic>{
                'id': purchase.id,
                'supplierId': purchase.supplierId,
                'date': purchase.date,
                'total': purchase.total ?? 0.0,
                'notes': purchase.notes,
              })
          .toList();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load purchases report: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load low stock report
  Future<void> loadLowStockReport(double threshold) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Product> lowStockProducts =
          await _productService.getLowStockProducts(threshold);
      _lowStockReport = lowStockProducts
          .map((Product product) => <String, Object?>{
                'id': product.id,
                'name': product.name,
                'currentStock': product.quantity ?? 0.0,
                'threshold': threshold,
                'status': (product.quantity ?? 0.0) == 0
                    ? 'Out of Stock'
                    : 'Low Stock',
                'categoryId': product.categoryId,
              })
          .toList();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load low stock report: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get sales summary for a specific period
  Future<Map<String, dynamic>> getSalesSummary(
      String startDate, String endDate) async {
    try {
      final List<Sale> sales =
          await _saleService.getSalesByDateRange(startDate, endDate);
      final double totalAmount = sales.fold<double>(
          0.0, (double sum, Sale sale) => sum + (sale.total ?? 0.0));

      return <String, dynamic>{
        'totalSales': totalAmount,
        'salesCount': sales.length,
        'averageSale': sales.isNotEmpty ? totalAmount / sales.length : 0.0,
        'period': '$startDate to $endDate',
      };
    } catch (e) {
      _setError('Failed to get sales summary: $e');
      return <String, dynamic>{};
    }
  }

  /// Get top selling products
  Future<List<Map<String, dynamic>>> getTopSellingProducts(
      {int limit = 10}) async {
    try {
      // This would require a more complex query in a real implementation
      // For now, return empty list as placeholder
      return <Map<String, dynamic>>[];
    } catch (e) {
      _setError('Failed to get top selling products: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get monthly sales trend
  Future<List<Map<String, dynamic>>> getMonthlySalesTrend(int year) async {
    try {
      // This would require month-wise aggregation
      // For now, return empty list as placeholder
      return <Map<String, dynamic>>[];
    } catch (e) {
      _setError('Failed to get monthly sales trend: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Clear all reports
  void clearReports() {
    _salesReport.clear();
    _purchasesReport.clear();
    _lowStockReport.clear();
    _dashboardData.clear();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
