import 'package:flutter/material.dart';

/// ثوابت التصميم والألوان للتطبيق
class AppDesignConstants {
  // معلومات التطبيق
  static const String appName = 'أسامة ماركت';
  static const String appNameEnglish = 'Osama Market';
  static const String appSlogan = 'إدارة ذكية لمتجرك';
  
  // ألوان التطبيق - تدرج أزرق وأخضر عصري
  static const Color primaryColor = Color(0xFF1976D2); // أزرق عميق
  static const Color primaryLightColor = Color(0xFF42A5F5); // أزرق فاتح
  static const Color primaryDarkColor = Color(0xFF0D47A1); // أزرق داكن
  static const Color secondaryColor = Color(0xFF43A047); // أخضر
  static const Color accentColor = Color(0xFF00BCD4); // سماوي
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFE53935);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  
  // تدرجات لونية للخلفيات
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: <Color>[primaryColor, primaryLightColor],
  );
  
  static const LinearGradient splashGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: <Color>[Color(0xFFE3F2FD), Color(0xFFFFFFFF)],
  );
  
  static const LinearGradient onboardingGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: <Color>[Color(0xFFF8F9FA), Color(0xFFFFFFFF)],
  );
  
  // أحجام النصوص
  static const double titleFontSize = 28.0;
  static const double subtitleFontSize = 20.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double largeTitleFontSize = 32.0;
  
  // المسافات
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  static const double extraSmallPadding = 4.0;
  
  // أحجام الأيقونات
  static const double smallIconSize = 20.0;
  static const double defaultIconSize = 24.0;
  static const double largeIconSize = 48.0;
  static const double extraLargeIconSize = 64.0;
  static const double splashIconSize = 80.0;
  
  // أحجام الأزرار
  static const double buttonHeight = 52.0;
  static const double smallButtonHeight = 40.0;
  static const double largeButtonHeight = 60.0;
  
  // نصف القطر للحواف
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 20.0;
  static const double extraLargeBorderRadius = 28.0;
  
  // مدة الرسوم المتحركة
  static const Duration shortAnimationDuration = Duration(milliseconds: 300);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 600);
  static const Duration longAnimationDuration = Duration(milliseconds: 1000);
  static const Duration splashAnimationDuration = Duration(milliseconds: 1500);
  
  // مدة عرض شاشة السبلاش
  static const Duration splashDuration = Duration(seconds: 3);
  
  // مفاتيح SharedPreferences
  static const String hasViewedOnboardingKey = 'has_viewed_onboarding';
  static const String isFirstLaunchKey = 'is_first_launch';
  static const String userTokenKey = 'user_token';
  
  // نصوص شاشة Onboarding
  static const List<OnboardingData> onboardingPages = <OnboardingData>[
    OnboardingData(
      title: 'إدارة المخزون بذكاء',
      description: 'تتبع كل منتج في المخزن والمتجر، وتلقي تنبيهات بالمخزون المنخفض لضمان عدم نفاد البضائع',
      icon: Icons.inventory_2_outlined,
      color: primaryColor,
    ),
    OnboardingData(
      title: 'تتبع المبيعات والأرباح',
      description: 'راقب مبيعاتك اليومية والشهرية، واحسب أرباحك بدقة مع تقارير مفصلة وسهلة الفهم',
      icon: Icons.trending_up_outlined,
      color: successColor,
    ),
    OnboardingData(
      title: 'إدارة العملاء والموردين',
      description: 'احتفظ بسجل كامل لعملائك وموردينك، وتتبع المدفوعات والديون بسهولة ووضوح',
      icon: Icons.people_outline,
      color: accentColor,
    ),
    OnboardingData(
      title: 'تقارير وإحصائيات شاملة',
      description: 'احصل على رؤى عميقة لأداء متجرك مع تقارير مفصلة تساعدك في اتخاذ قرارات ذكية',
      icon: Icons.analytics_outlined,
      color: warningColor,
    ),
  ];
  
  // نصوص تحفيزية
  static const List<String> motivationalTexts = <String>[
    'ابدأ رحلتك نحو إدارة أفضل لمتجرك!',
    'بين يديك الآن أداة قوية لنمو أعمالك',
    'حول متجرك إلى مشروع ناجح ومربح',
    'إدارة احترافية تبدأ من هنا',
  ];
  
  // أنماط النصوص
  static const TextStyle splashTitleStyle = TextStyle(
    fontSize: largeTitleFontSize,
    fontWeight: FontWeight.bold,
    color: primaryColor,
    letterSpacing: 1.2,
  );
  
  static const TextStyle splashSubtitleStyle = TextStyle(
    fontSize: subtitleFontSize,
    fontWeight: FontWeight.w500,
    color: textSecondaryColor,
    letterSpacing: 0.5,
  );
  
  static const TextStyle onboardingTitleStyle = TextStyle(
    fontSize: titleFontSize,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
    height: 1.3,
  );
  
  static const TextStyle onboardingDescriptionStyle = TextStyle(
    fontSize: bodyFontSize,
    fontWeight: FontWeight.w400,
    color: textSecondaryColor,
    height: 1.5,
  );
  
  // أنماط الأزرار
  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    minimumSize: const Size(double.infinity, buttonHeight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(defaultBorderRadius),
    ),
    elevation: 2,
  );
  
  static ButtonStyle secondaryButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: primaryColor,
    side: const BorderSide(color: primaryColor, width: 2),
    minimumSize: const Size(double.infinity, buttonHeight),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(defaultBorderRadius),
    ),
  );
  
  // أنماط الظلال
  static const List<BoxShadow> cardShadow = <BoxShadow>[
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];
  
  static const List<BoxShadow> buttonShadow = <BoxShadow>[
    BoxShadow(
      color: Color(0x26000000),
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];
}

/// بيانات صفحة Onboarding
class OnboardingData {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  
  const OnboardingData({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
