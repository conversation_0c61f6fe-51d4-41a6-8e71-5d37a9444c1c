import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';
import '../../utils/date_helper.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';

/// شاشة تفاصيل المنتج المحسنة
class EnhancedProductDetailsScreen extends StatefulWidget {
  final String productId;

  const EnhancedProductDetailsScreen({
    super.key,
    required this.productId,
  });

  @override
  State<EnhancedProductDetailsScreen> createState() => _EnhancedProductDetailsScreenState();
}

class _EnhancedProductDetailsScreenState extends State<EnhancedProductDetailsScreen> {
  Product? _product;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProduct();
  }

  void _loadProduct() {
    final ProductProvider productProvider = context.read<ProductProvider>();
    final Product? product = productProvider.products
        .where((Product p) => p.id.toString() == widget.productId)
        .firstOrNull;
    
    setState(() {
      _product = product;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _product == null
                ? _buildNotFoundState()
                : _buildProductDetails(),
      ),
    );
  }

  Widget _buildNotFoundState() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل المنتج'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'المنتج غير موجود',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            CustomPrimaryButton(
              text: 'العودة',
              onPressed: () => context.pop(),
              icon: Icons.arrow_back,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetails() {
    return CustomScrollView(
      slivers: <Widget>[
        _buildSliverAppBar(),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildStatusCard(),
                const SizedBox(height: AppDimensions.paddingM),
                _buildBasicInfoCard(),
                const SizedBox(height: AppDimensions.paddingM),
                _buildInventoryCard(),
                const SizedBox(height: AppDimensions.paddingM),
                _buildPricingCard(),
                const SizedBox(height: AppDimensions.paddingM),
                _buildAdditionalInfoCard(),
                const SizedBox(height: AppDimensions.paddingM),
                _buildHistoryCard(),
                const SizedBox(height: AppDimensions.paddingXL),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _product?.name ?? 'تفاصيل المنتج',
          style: AppStyles.titleMedium.copyWith(
            color: AppColors.textOnPrimary,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
          child: Stack(
            children: <Widget>[
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
              ),
              Positioned(
                bottom: 60,
                right: 20,
                left: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    if (_product?.category?.isNotEmpty == true)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        ),
                        child: Text(
                          _product!.category!,
                          style: AppStyles.labelSmall.copyWith(
                            color: AppColors.textOnPrimary,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: <Widget>[
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () => context.go('/products/edit/${widget.productId}'),
        ),
        IconButton(
          icon: const Icon(Icons.delete),
          onPressed: _deleteProduct,
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('مشاركة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'print',
              child: ListTile(
                leading: Icon(Icons.print),
                title: Text('طباعة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'barcode',
              child: ListTile(
                leading: Icon(Icons.qr_code),
                title: Text('إنشاء باركود'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusCard() {
    final double quantity = _product?.quantity ?? 0;
    final int minLevel = _product?.minLevel ?? 10;
    
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    if (quantity <= 0) {
      statusColor = AppColors.error;
      statusText = 'نفد المخزون';
      statusIcon = Icons.error;
    } else if (quantity <= minLevel) {
      statusColor = AppColors.warning;
      statusText = 'مخزون منخفض';
      statusIcon = Icons.warning;
    } else {
      statusColor = AppColors.success;
      statusText = 'متوفر';
      statusIcon = Icons.check_circle;
    }
    
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          gradient: LinearGradient(
            colors: <Color>[
              statusColor.withValues(alpha: 0.1),
              statusColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Icon(
                statusIcon,
                color: statusColor,
                size: AppDimensions.iconL,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    'حالة المخزون',
                    style: AppStyles.labelMedium,
                  ),
                  Text(
                    statusText,
                    style: AppStyles.titleMedium.copyWith(
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              '$quantity ${_product?.unit ?? ''}',
              style: AppStyles.headlineSmall.copyWith(
                color: statusColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'المعلومات الأساسية',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            _buildInfoRow('الاسم', _product?.name ?? '-'),
            if (_product?.description.isNotEmpty == true)
              _buildInfoRow('الوصف', _product!.description),
            _buildInfoRow('الفئة', _product?.category ?? '-'),
            if (_product?.barcode?.isNotEmpty == true)
              _buildInfoRow('الباركود', _product!.barcode!),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'معلومات المخزون',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: <Widget>[
                Expanded(
                  child: _buildStatItem(
                    'الكمية الحالية',
                    '${_product?.quantity ?? 0}',
                    _product?.unit ?? '',
                    Icons.inventory_2,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الحد الأدنى',
                    '${_product?.minLevel ?? 0}',
                    _product?.unit ?? '',
                    Icons.warning,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingCard() {
    final double purchasePrice = _product?.purchasePrice ?? 0;
    final double salePrice = _product?.salePrice ?? 0;
    final double profit = salePrice - purchasePrice;
    final num margin = purchasePrice > 0 ? (profit / purchasePrice) * 100 : 0;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'معلومات الأسعار',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: <Widget>[
                Expanded(
                  child: _buildStatItem(
                    'سعر الشراء',
                    Formatters.formatCurrency(purchasePrice),
                    '',
                    Icons.shopping_cart,
                    AppColors.info,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'سعر البيع',
                    Formatters.formatCurrency(salePrice),
                    '',
                    Icons.sell,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: profit >= 0 ? AppColors.successLight : AppColors.errorLight,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'الربح المتوقع',
                        style: AppStyles.labelMedium,
                      ),
                      Text(
                        Formatters.formatCurrency(profit),
                        style: AppStyles.titleSmall.copyWith(
                          color: profit >= 0 ? AppColors.success : AppColors.error,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        'هامش الربح',
                        style: AppStyles.labelMedium,
                      ),
                      Text(
                        '${margin.toStringAsFixed(1)}%',
                        style: AppStyles.titleSmall.copyWith(
                          color: margin >= 0 ? AppColors.success : AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'معلومات إضافية',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            _buildInfoRow('الوحدة', _product?.unit ?? '-'),
            if (_product?.createdAt != null)
              _buildInfoRow(
                'تاريخ الإضافة',
                DateHelper.formatDate(_product!.createdAt!),
              ),
            if (_product?.updatedAt != null)
              _buildInfoRow(
                'آخر تحديث',
                DateHelper.formatDate(_product!.updatedAt!),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'سجل الحركات',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
                CustomTextButton(
                  text: 'عرض الكل',
                  onPressed: () {
                    // TODO: فتح شاشة سجل الحركات
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد حركات مسجلة',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppStyles.labelMedium,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Column(
        children: <Widget>[
          Icon(icon, color: color, size: AppDimensions.iconM),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            label,
            style: AppStyles.labelSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          Text(
            value,
            style: AppStyles.titleSmall.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
          if (unit.isNotEmpty)
            Text(
              unit,
              style: AppStyles.labelSmall.copyWith(color: color),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareProduct();
        break;
      case 'print':
        _printProduct();
        break;
      case 'barcode':
        _generateBarcode();
        break;
    }
  }

  void _shareProduct() {
    // TODO: تنفيذ مشاركة المنتج
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ المشاركة قريباً');
  }

  void _printProduct() {
    // TODO: تنفيذ طباعة المنتج
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ الطباعة قريباً');
  }

  void _generateBarcode() {
    // TODO: تنفيذ إنشاء الباركود
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ إنشاء الباركود قريباً');
  }

  void _deleteProduct() async {
    final bool? confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: _product?.name ?? 'المنتج',
    );

    if (confirmed == true) {
      try {
        await context.read<ProductProvider>().deleteProduct(_product!.id!);
        SnackBarHelper.showSuccess(context, 'تم حذف المنتج بنجاح');
        context.pop();
      } catch (e) {
        SnackBarHelper.showError(context, 'فشل في حذف المنتج: $e');
      }
    }
  }
}
