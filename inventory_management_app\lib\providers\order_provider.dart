import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/order.dart';
import 'package:inventory_management_app/services/order_service.dart';

/// Provider class for managing order state and operations
class OrderProvider extends ChangeNotifier {
  List<Order> _orders = <Order>[];
  final OrderService _orderService = OrderService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of orders
  List<Order> get orders => _orders;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all orders from the database
  Future<void> fetchOrders() async {
    _setLoading(true);
    _clearError();

    try {
      _orders = await _orderService.getAllOrders();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch orders: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new order
  Future<void> addOrder(Order order) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.insertOrder(order);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to add order: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing order
  Future<void> updateOrder(Order order) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.updateOrder(order);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to update order: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an order
  Future<void> deleteOrder(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.deleteOrder(id);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to delete order: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update order status
  Future<void> updateOrderStatus(int orderId, String status) async {
    _setLoading(true);
    _clearError();

    try {
      await _orderService.updateOrderStatus(orderId, status);
      await fetchOrders();
    } catch (e) {
      _setError('Failed to update order status: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
