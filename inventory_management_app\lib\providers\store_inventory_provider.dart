import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import '../models/store_inventory_adjustment.dart';
import '../services/database_operations_service.dart';
import '../providers/product_provider.dart';

/// Provider class for managing store inventory adjustments
class StoreInventoryProvider extends ChangeNotifier {
  List<StoreInventoryAdjustment> _adjustments = <StoreInventoryAdjustment>[];
  final DatabaseOperationsService _databaseService = DatabaseOperationsService.instance;
  bool _isLoading = false;
  String? _error;

  /// Get the list of store inventory adjustments
  List<StoreInventoryAdjustment> get adjustments => _adjustments;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load adjustments from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة StoreInventoryProvider...');
      await loadAdjustments();
      debugPrint('✅ تم تهيئة StoreInventoryProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة StoreInventoryProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل تعديلات المخزون: $e';
      notifyListeners();
    }
  }

  /// Load all store inventory adjustments from database
  Future<void> loadAdjustments() async {
    _setLoading(true);
    _clearError();

    try {
      final List<Map<String, dynamic>> adjustmentMaps = await _databaseService.getStoreInventoryAdjustments();
      _adjustments = adjustmentMaps.map((Map<String, dynamic> map) => StoreInventoryAdjustment.fromMap(map)).toList();
      notifyListeners();
      debugPrint('📦 تم تحميل ${_adjustments.length} تعديل مخزون');
    } catch (e) {
      _setError('فشل في تحميل تعديلات المخزون: $e');
      debugPrint('❌ خطأ في تحميل التعديلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Perform store inventory adjustment
  Future<bool> performStoreInventoryAdjustment(
    int productId,
    int countedQuantity,
    String? notes,
    ProductProvider productProvider,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      // Get the product
      final Product? product = productProvider.getProductById(productId);
      if (product == null) {
        throw Exception('المنتج غير موجود');
      }

      final int recordedStoreQuantity = product.storeQuantity ?? 0;
      final int difference = countedQuantity - recordedStoreQuantity;
      final double retailPrice = product.retailPrice ?? 0;
      final double adjustmentValue = difference * retailPrice;

      // Create adjustment record
      final StoreInventoryAdjustment adjustment = StoreInventoryAdjustment(
        adjustmentDate: DateTime.now(),
        productId: productId,
        countedQuantity: countedQuantity,
        recordedStoreQuantity: recordedStoreQuantity,
        difference: difference,
        retailPriceAtAdjustment: retailPrice,
        adjustmentValue: adjustmentValue,
        notes: notes,
        productName: product.name,
      );

      // Save adjustment to database
      final int adjustmentId = await _databaseService.insertStoreInventoryAdjustment(adjustment.toMap());
      
      // Update product store quantity
      final bool updateSuccess = await productProvider.updateStoreQuantity(productId, countedQuantity);
      
      if (!updateSuccess) {
        throw Exception('فشل في تحديث كمية المنتج');
      }

      // Create the adjustment with the new ID
      final StoreInventoryAdjustment savedAdjustment = adjustment.copyWith(id: adjustmentId);
      
      // Add to local list
      _adjustments.insert(0, savedAdjustment); // Insert at beginning for newest first
      notifyListeners();

      debugPrint('✅ تم إجراء تعديل المخزون بنجاح - ID: $adjustmentId');
      debugPrint('📊 الفرق: $difference، القيمة: $adjustmentValue');
      return true;

    } catch (e) {
      _setError('فشل في إجراء تعديل المخزون: $e');
      debugPrint('❌ خطأ في تعديل المخزون: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get adjustments by date range
  Future<void> getAdjustmentsByDateRange(DateTime startDate, DateTime endDate) async {
    _setLoading(true);
    _clearError();

    try {
      final List<Map<String, dynamic>> adjustmentMaps = await _databaseService
          .getStoreInventoryAdjustmentsByDateRange(startDate, endDate);
      _adjustments = adjustmentMaps.map((Map<String, dynamic> map) => StoreInventoryAdjustment.fromMap(map)).toList();
      notifyListeners();
      debugPrint('📦 تم تحميل ${_adjustments.length} تعديل للفترة المحددة');
    } catch (e) {
      _setError('فشل في تحميل التعديلات للفترة المحددة: $e');
      debugPrint('❌ خطأ في تحميل التعديلات بالتاريخ: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get total adjustment value for a date range
  Future<double> getTotalAdjustmentValue(DateTime startDate, DateTime endDate) async {
    try {
      return await _databaseService.getTotalAdjustmentValue(startDate, endDate);
    } catch (e) {
      _setError('فشل في حساب إجمالي قيمة التعديلات: $e');
      return 0.0;
    }
  }

  /// Get negative adjustment value (losses) for a date range
  Future<double> getNegativeAdjustmentValue(DateTime startDate, DateTime endDate) async {
    try {
      return await _databaseService.getNegativeAdjustmentValue(startDate, endDate);
    } catch (e) {
      _setError('فشل في حساب قيمة الخسائر: $e');
      return 0.0;
    }
  }

  /// Get adjustments statistics
  Map<String, dynamic> getAdjustmentsStatistics() {
    if (_adjustments.isEmpty) {
      return <String, dynamic>{
        'totalAdjustments': 0,
        'positiveAdjustments': 0,
        'negativeAdjustments': 0,
        'neutralAdjustments': 0,
        'totalValue': 0.0,
        'totalLosses': 0.0,
        'totalGains': 0.0,
      };
    }

    int positiveCount = 0;
    int negativeCount = 0;
    int neutralCount = 0;
    double totalValue = 0.0;
    double totalLosses = 0.0;
    double totalGains = 0.0;

    for (final StoreInventoryAdjustment adjustment in _adjustments) {
      if (adjustment.isPositiveAdjustment) {
        positiveCount++;
        totalGains += adjustment.adjustmentValue;
      } else if (adjustment.isNegativeAdjustment) {
        negativeCount++;
        totalLosses += adjustment.adjustmentValue.abs();
      } else {
        neutralCount++;
      }
      totalValue += adjustment.adjustmentValue;
    }

    return <String, dynamic>{
      'totalAdjustments': _adjustments.length,
      'positiveAdjustments': positiveCount,
      'negativeAdjustments': negativeCount,
      'neutralAdjustments': neutralCount,
      'totalValue': totalValue,
      'totalLosses': totalLosses,
      'totalGains': totalGains,
    };
  }

  /// Clear all adjustments (useful for filtering)
  void clearAdjustments() {
    _adjustments.clear();
    notifyListeners();
  }

  /// Reset to show all adjustments
  Future<void> resetAdjustments() async {
    await loadAdjustments();
  }

  /// Get adjustments for today
  Future<void> getTodayAdjustments() async {
    final DateTime today = DateTime.now();
    final DateTime startOfDay = DateTime(today.year, today.month, today.day);
    final DateTime endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
    
    await getAdjustmentsByDateRange(startOfDay, endOfDay);
  }

  /// Get adjustments for this week
  Future<void> getWeekAdjustments() async {
    final DateTime now = DateTime.now();
    final DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final DateTime endOfWeek = startOfWeek.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
    
    await getAdjustmentsByDateRange(startOfWeek, endOfWeek);
  }

  /// Get adjustments for this month
  Future<void> getMonthAdjustments() async {
    final DateTime now = DateTime.now();
    final DateTime startOfMonth = DateTime(now.year, now.month, 1);
    final DateTime endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    
    await getAdjustmentsByDateRange(startOfMonth, endOfMonth);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
