{"logs": [{"outputFile": "com.example.inventory_management_app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,74,75,92,93,124,125,227,228,229,230,231,232,233,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,324,325,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,371,400,401,402,403,404,405,406,443,1965,1966,1970,1971,1975,2118,2119,2775,2792,2962,2995,3025,3058", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2981,3053,4350,4415,6505,6574,13567,13637,13705,13777,13847,13908,13982,15225,15286,15347,15409,15473,15535,15596,15664,15764,15824,15890,15963,16032,16089,16141,17089,17161,17237,17302,17361,17420,17480,17540,17600,17660,17720,17780,17840,17900,17960,18020,18079,18139,18199,18259,18319,18379,18439,18499,18559,18619,18679,18738,18798,18858,18917,18976,19035,19094,19153,19721,19756,20342,20397,20460,20515,20573,20631,20692,20755,20812,20863,20913,20974,21031,21097,21131,21166,22207,24226,24293,24365,24434,24503,24577,24649,28428,127457,127574,127775,127885,128086,139458,139530,161114,161718,169553,171284,172284,172966", "endLines": "29,74,75,92,93,124,125,227,228,229,230,231,232,233,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,324,325,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,371,400,401,402,403,404,405,406,443,1965,1969,1970,1974,1975,2118,2119,2780,2801,2994,3015,3057,3063", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,3048,3136,4410,4476,6569,6632,13632,13700,13772,13842,13903,13977,14050,15281,15342,15404,15468,15530,15591,15659,15759,15819,15885,15958,16027,16084,16136,16198,17156,17232,17297,17356,17415,17475,17535,17595,17655,17715,17775,17835,17895,17955,18015,18074,18134,18194,18254,18314,18374,18434,18494,18554,18614,18674,18733,18793,18853,18912,18971,19030,19089,19148,19207,19751,19786,20392,20455,20510,20568,20626,20687,20750,20807,20858,20908,20969,21026,21092,21126,21161,21196,22272,24288,24360,24429,24498,24572,24644,24732,28494,127569,127770,127880,128081,128210,139525,139592,161312,162014,171279,171960,172961,173128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,126,264,265,266,267,268,269,270,327,328,329,369,370,425,428,437,438,444,445,446,1519,1703,1706,1712,1718,1721,1727,1731,1734,1741,1747,1750,1756,1761,1766,1773,1775,1781,1787,1795,1800,1807,1812,1818,1822,1829,1833,1839,1845,1848,1852,1853,2766,2781,2920,2958,3100,3288,3306,3370,3380,3390,3397,3403,3507,3676,3693", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6637,16203,16267,16322,16390,16457,16522,16579,19834,19882,19930,22081,22144,27070,27320,28113,28157,28499,28638,28688,96894,110632,110737,110982,111320,111466,111806,112018,112181,112588,112926,113049,113388,113627,113884,114255,114315,114653,114939,115388,115680,116068,116373,116717,116962,117292,117499,117767,118040,118184,118385,118432,160794,161317,168103,169404,174346,180256,180884,182809,183091,183396,183658,183918,187434,193729,194259", "endLines": "63,126,264,265,266,267,268,269,270,327,328,329,369,370,425,428,437,440,444,445,446,1535,1705,1711,1717,1720,1726,1730,1733,1740,1746,1749,1755,1760,1765,1772,1774,1780,1786,1794,1799,1806,1811,1817,1821,1828,1832,1838,1844,1847,1851,1852,1853,2770,2791,2939,2961,3109,3295,3369,3379,3389,3396,3402,3445,3519,3692,3709", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6701,16262,16317,16385,16452,16517,16574,16631,19877,19925,19986,22139,22202,27103,27372,28152,28292,28633,28683,28731,98327,110732,110977,111315,111461,111801,112013,112176,112583,112921,113044,113383,113622,113879,114250,114310,114648,114934,115383,115675,116063,116368,116712,116957,117287,117494,117762,118035,118179,118380,118427,118483,160974,161713,168827,169548,174673,180499,182804,183086,183391,183653,183913,185336,187881,194254,194822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fed144bef681fc8fb80134095669b372\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "21499", "endColumns": "42", "endOffsets": "21537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\506e321e672d66bb2b4486d2ee8beed8\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "399", "startColumns": "4", "startOffsets": "24143", "endColumns": "82", "endOffsets": "24221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a4f2357047f2fdd854a789973139f996\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2248,2264,2270,3590,3606", "startColumns": "4,4,4,4,4", "startOffsets": "144135,144560,144738,190165,190576", "endLines": "2263,2269,2279,3605,3609", "endColumns": "24,24,24,24,24", "endOffsets": "144555,144733,145017,190571,190698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9bf0628666be1a9ac10b49d28bb02595\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "320,321,326,333,334,353,354,355,356,357", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19534,19574,19791,20129,20184,21201,21255,21307,21356,21417", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19569,19616,19829,20179,20226,21250,21302,21351,21412,21462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84cfce9c05a54a984dea12df260c2609\\transformed\\jetified-play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "368,415", "startColumns": "4,4", "startOffsets": "22013,25777", "endColumns": "67,166", "endOffsets": "22076,25939"}}, {"source": "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1536,1540", "startColumns": "4,4", "startOffsets": "98332,98513", "endLines": "1539,1542", "endColumns": "12,12", "endOffsets": "98508,98677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aadc3611709663a05036e28336f6e814\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "332,360", "startColumns": "4,4", "startOffsets": "20087,21542", "endColumns": "41,59", "endOffsets": "20124,21597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c99be856553367d8fad52c95155b00\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,318,2227,2233,3551,3559,3574", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19417,143265,143460,188615,188897,189511", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,318,2232,2237,3558,3573,3589", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19472,143455,143613,188892,189506,190160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a9e90969c21e54abbd28d118be0111db\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "86,87,88,89,225,226,426,429,430,431", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3986,4044,4110,4173,13424,13495,27108,27377,27444,27523", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4039,4105,4168,4230,13490,13562,27171,27439,27518,27587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\274b962bfc0ee3499460b07125b40653\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "21602", "endColumns": "53", "endOffsets": "21651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\db6569caad4edde0be5199a526eef45e\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2273,2338,2408,2472", "endColumns": "64,69,63,60", "endOffsets": "2333,2403,2467,2528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fe4c05eaffed710d6c61f97a8b8ac890\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "319,335,363,3016,3021", "startColumns": "4,4,4,4,4", "startOffsets": "19477,20231,21706,171965,172135", "endLines": "319,335,363,3020,3024", "endColumns": "56,64,63,24,24", "endOffsets": "19529,20291,21765,172130,172279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c7d3b67c81f17a9d01acb41ef7dc29fd\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21656", "endColumns": "49", "endOffsets": "21701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ee991f1e2a54faf463391e906c8be955\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2120,2830,2836", "startColumns": "4,4,4,4", "startOffsets": "164,139597,163376,163587", "endLines": "3,2122,2835,2919", "endColumns": "60,12,24,24", "endOffsets": "220,139737,163582,168098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\77b6482155e5a178adf635640ae2a82c\\transformed\\jetified-play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "94,95,96,97,98,99,100,101,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,3110,3520", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4481,4571,4651,4741,4831,4911,4992,5072,24737,24842,25023,25148,25255,25435,25558,25674,25944,26132,26237,26418,26543,26718,26866,26929,26991,174678,187886", "endLines": "94,95,96,97,98,99,100,101,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,3122,3538", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4566,4646,4736,4826,4906,4987,5067,5147,24837,25018,25143,25250,25430,25553,25669,25772,26127,26232,26413,26538,26713,26861,26924,26986,27065,174988,188298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9e5757bbf3b5c6f98be61ba84aed2ced\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "236,237,238,246,247,248,323,3452", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14228,14287,14335,15002,15077,15153,19655,185556", "endLines": "236,237,238,246,247,248,323,3471", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14282,14330,14386,15072,15148,15220,19716,186346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,90,91,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,234,235,239,240,241,242,243,244,245,271,272,273,274,275,276,277,278,314,315,316,317,322,330,331,336,358,364,365,366,367,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,442,447,448,449,450,451,452,460,461,465,469,473,478,484,491,495,499,504,508,512,516,520,524,528,534,538,544,548,554,558,563,567,570,574,580,584,590,594,600,603,607,611,615,619,623,624,625,626,629,632,635,638,642,643,644,645,646,649,651,653,655,660,661,665,671,675,676,678,689,690,694,700,704,705,706,710,737,741,742,746,774,944,970,1141,1167,1198,1206,1212,1226,1248,1253,1258,1268,1277,1286,1290,1297,1305,1312,1313,1322,1325,1328,1332,1336,1340,1343,1344,1349,1354,1364,1369,1376,1382,1383,1386,1390,1395,1397,1399,1402,1405,1407,1411,1414,1421,1424,1427,1431,1433,1437,1439,1441,1443,1447,1455,1463,1475,1481,1490,1493,1504,1507,1508,1513,1514,1543,1612,1682,1683,1693,1702,1854,1856,1860,1863,1866,1869,1872,1875,1878,1881,1885,1888,1891,1894,1898,1901,1905,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1931,1933,1934,1935,1936,1937,1938,1939,1940,1942,1943,1945,1946,1948,1950,1951,1953,1954,1955,1956,1957,1958,1960,1961,1962,1963,1964,1976,1978,1980,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2000,2001,2003,2007,2011,2012,2013,2014,2015,2016,2020,2021,2022,2023,2025,2027,2029,2031,2033,2034,2035,2036,2038,2040,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2056,2057,2058,2059,2061,2063,2064,2066,2067,2069,2071,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2086,2087,2088,2089,2091,2092,2093,2094,2095,2097,2099,2101,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2123,2198,2201,2204,2207,2221,2238,2280,2309,2336,2345,2407,2771,2802,2940,3064,3088,3094,3123,3144,3268,3296,3302,3446,3472,3539,3610,3710,3730,3785,3797,3823", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2533,2614,2675,2750,2826,2903,3141,3226,3308,3384,3460,3537,3615,3721,3827,3906,4235,4292,5152,5226,5301,5366,5432,5492,5553,5625,5698,5765,5833,5892,5951,6010,6069,6128,6182,6236,6289,6343,6397,6451,6706,6780,6859,6932,7006,7077,7149,7221,7294,7351,7409,7482,7556,7630,7705,7777,7850,7920,7991,8051,8112,8181,8250,8320,8394,8470,8534,8611,8687,8764,8829,8898,8975,9050,9119,9187,9264,9330,9391,9488,9553,9622,9721,9792,9851,9909,9966,10025,10089,10160,10232,10304,10376,10448,10515,10583,10651,10710,10773,10837,10927,11018,11078,11144,11211,11277,11347,11411,11464,11531,11592,11659,11772,11830,11893,11958,12023,12098,12171,12243,12292,12353,12414,12475,12537,12601,12665,12729,12794,12857,12917,12978,13044,13103,13163,13225,13296,13356,14055,14141,14391,14481,14568,14656,14738,14821,14911,16636,16688,16746,16791,16857,16921,16978,17035,19212,19269,19317,19366,19621,19991,20038,20296,21467,21770,21834,21896,21956,22277,22351,22421,22499,22553,22623,22708,22756,22802,22863,22926,22992,23056,23127,23190,23255,23319,23380,23441,23493,23566,23640,23709,23784,23858,23932,24073,28375,28736,28814,28904,28992,29088,29178,29760,29849,30096,30377,30629,30914,31307,31784,32006,32228,32504,32731,32961,33191,33421,33651,33878,34297,34523,34948,35178,35606,35825,36108,36316,36447,36674,37100,37325,37752,37973,38398,38518,38794,39095,39419,39710,40024,40161,40292,40397,40639,40806,41010,41218,41489,41601,41713,41818,41935,42149,42295,42435,42521,42869,42957,43203,43621,43870,43952,44050,44642,44742,44994,45418,45673,45767,45856,46093,48117,48359,48461,48714,50870,61402,62918,73549,75077,76834,77460,77880,78941,80206,80462,80698,81245,81739,82344,82542,83122,83686,84061,84179,84717,84874,85070,85343,85599,85769,85910,85974,86339,86706,87382,87646,87984,88337,88431,88617,88923,89185,89310,89437,89676,89887,90006,90199,90376,90831,91012,91134,91393,91506,91693,91795,91902,92031,92306,92814,93310,94187,94481,95051,95200,95932,96104,96188,96524,96616,98682,103928,109317,109379,109957,110541,118488,118601,118830,118990,119142,119313,119479,119648,119815,119978,120221,120391,120564,120735,121009,121208,121413,121743,121827,121923,122019,122117,122217,122319,122421,122523,122625,122727,122827,122923,123035,123164,123287,123418,123549,123647,123761,123855,123995,124129,124225,124337,124437,124553,124649,124761,124861,125001,125137,125301,125431,125589,125739,125880,126024,126159,126271,126421,126549,126677,126813,126945,127075,127205,127317,128215,128361,128505,128643,128709,128799,128875,128979,129069,129171,129279,129387,129487,129567,129659,129757,129867,129945,130051,130143,130247,130357,130479,130642,130799,130879,130979,131069,131179,131269,131510,131604,131710,131802,131902,132014,132128,132244,132360,132454,132568,132680,132782,132902,133024,133106,133210,133330,133456,133554,133648,133736,133848,133964,134086,134198,134373,134489,134575,134667,134779,134903,134970,135096,135164,135292,135436,135564,135633,135728,135843,135956,136055,136164,136275,136386,136487,136592,136692,136822,136913,137036,137130,137242,137328,137432,137528,137616,137734,137838,137942,138068,138156,138264,138364,138454,138564,138648,138750,138834,138888,138952,139058,139144,139254,139338,139742,142358,142476,142591,142671,143032,143618,145022,146366,147727,148115,150890,160979,162019,168832,173133,173884,174146,174993,175372,179650,180504,180733,185341,186351,188303,190703,194827,195571,197702,198042,199353", "endLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,90,91,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,234,235,239,240,241,242,243,244,245,271,272,273,274,275,276,277,278,314,315,316,317,322,330,331,336,358,364,365,366,367,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,442,447,448,449,450,451,459,460,464,468,472,477,483,490,494,498,503,507,511,515,519,523,527,533,537,543,547,553,557,562,566,569,573,579,583,589,593,599,602,606,610,614,618,622,623,624,625,628,631,634,637,641,642,643,644,645,648,650,652,654,659,660,664,670,674,675,677,688,689,693,699,703,704,705,709,736,740,741,745,773,943,969,1140,1166,1197,1205,1211,1225,1247,1252,1257,1267,1276,1285,1289,1296,1304,1311,1312,1321,1324,1327,1331,1335,1339,1342,1343,1348,1353,1363,1368,1375,1381,1382,1385,1389,1394,1396,1398,1401,1404,1406,1410,1413,1420,1423,1426,1430,1432,1436,1438,1440,1442,1446,1454,1462,1474,1480,1489,1492,1503,1506,1507,1512,1513,1518,1611,1681,1682,1692,1701,1702,1855,1859,1862,1865,1868,1871,1874,1877,1880,1884,1887,1890,1893,1897,1900,1904,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1930,1932,1933,1934,1935,1936,1937,1938,1939,1941,1942,1944,1945,1947,1949,1950,1952,1953,1954,1955,1956,1957,1959,1960,1961,1962,1963,1964,1977,1979,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1995,1996,1997,1998,1999,2000,2002,2006,2010,2011,2012,2013,2014,2015,2019,2020,2021,2022,2024,2026,2028,2030,2032,2033,2034,2035,2037,2039,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2055,2056,2057,2058,2060,2062,2063,2065,2066,2068,2070,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2085,2086,2087,2088,2090,2091,2092,2093,2094,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2197,2200,2203,2206,2220,2226,2247,2308,2335,2344,2406,2765,2774,2829,2957,3087,3093,3099,3143,3267,3287,3301,3305,3451,3506,3550,3675,3729,3784,3796,3822,3829", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2609,2670,2745,2821,2898,2976,3221,3303,3379,3455,3532,3610,3716,3822,3901,3981,4287,4345,5221,5296,5361,5427,5487,5548,5620,5693,5760,5828,5887,5946,6005,6064,6123,6177,6231,6284,6338,6392,6446,6500,6775,6854,6927,7001,7072,7144,7216,7289,7346,7404,7477,7551,7625,7700,7772,7845,7915,7986,8046,8107,8176,8245,8315,8389,8465,8529,8606,8682,8759,8824,8893,8970,9045,9114,9182,9259,9325,9386,9483,9548,9617,9716,9787,9846,9904,9961,10020,10084,10155,10227,10299,10371,10443,10510,10578,10646,10705,10768,10832,10922,11013,11073,11139,11206,11272,11342,11406,11459,11526,11587,11654,11767,11825,11888,11953,12018,12093,12166,12238,12287,12348,12409,12470,12532,12596,12660,12724,12789,12852,12912,12973,13039,13098,13158,13220,13291,13351,13419,14136,14223,14476,14563,14651,14733,14816,14906,14997,16683,16741,16786,16852,16916,16973,17030,17084,19264,19312,19361,19412,19650,20033,20082,20337,21494,21829,21891,21951,22008,22346,22416,22494,22548,22618,22703,22751,22797,22858,22921,22987,23051,23122,23185,23250,23314,23375,23436,23488,23561,23635,23704,23779,23853,23927,24068,24138,28423,28809,28899,28987,29083,29173,29755,29844,30091,30372,30624,30909,31302,31779,32001,32223,32499,32726,32956,33186,33416,33646,33873,34292,34518,34943,35173,35601,35820,36103,36311,36442,36669,37095,37320,37747,37968,38393,38513,38789,39090,39414,39705,40019,40156,40287,40392,40634,40801,41005,41213,41484,41596,41708,41813,41930,42144,42290,42430,42516,42864,42952,43198,43616,43865,43947,44045,44637,44737,44989,45413,45668,45762,45851,46088,48112,48354,48456,48709,50865,61397,62913,73544,75072,76829,77455,77875,78936,80201,80457,80693,81240,81734,82339,82537,83117,83681,84056,84174,84712,84869,85065,85338,85594,85764,85905,85969,86334,86701,87377,87641,87979,88332,88426,88612,88918,89180,89305,89432,89671,89882,90001,90194,90371,90826,91007,91129,91388,91501,91688,91790,91897,92026,92301,92809,93305,94182,94476,95046,95195,95927,96099,96183,96519,96611,96889,103923,109312,109374,109952,110536,110627,118596,118825,118985,119137,119308,119474,119643,119810,119973,120216,120386,120559,120730,121004,121203,121408,121738,121822,121918,122014,122112,122212,122314,122416,122518,122620,122722,122822,122918,123030,123159,123282,123413,123544,123642,123756,123850,123990,124124,124220,124332,124432,124548,124644,124756,124856,124996,125132,125296,125426,125584,125734,125875,126019,126154,126266,126416,126544,126672,126808,126940,127070,127200,127312,127452,128356,128500,128638,128704,128794,128870,128974,129064,129166,129274,129382,129482,129562,129654,129752,129862,129940,130046,130138,130242,130352,130474,130637,130794,130874,130974,131064,131174,131264,131505,131599,131705,131797,131897,132009,132123,132239,132355,132449,132563,132675,132777,132897,133019,133101,133205,133325,133451,133549,133643,133731,133843,133959,134081,134193,134368,134484,134570,134662,134774,134898,134965,135091,135159,135287,135431,135559,135628,135723,135838,135951,136050,136159,136270,136381,136482,136587,136687,136817,136908,137031,137125,137237,137323,137427,137523,137611,137729,137833,137937,138063,138151,138259,138359,138449,138559,138643,138745,138829,138883,138947,139053,139139,139249,139333,139453,142353,142471,142586,142666,143027,143260,144130,146361,147722,148110,150885,160789,161109,163371,169399,173879,174141,174341,175367,179645,180251,180728,180879,185551,187429,188610,193724,195566,197697,198037,199348,199551"}}, {"source": "C:\\Users\\<USER>\\Downloads\\new invent\\inventory_management_app\\build\\app\\generated\\res\\google-services\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,391,493,619,720", "endColumns": "143,81,109,101,125,100,77", "endOffsets": "194,276,386,488,614,715,793"}, "to": {"startLines": "427,432,433,434,435,436,441", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27176,27592,27674,27784,27886,28012,28297", "endColumns": "143,81,109,101,125,100,77", "endOffsets": "27315,27669,27779,27881,28007,28108,28370"}}]}]}