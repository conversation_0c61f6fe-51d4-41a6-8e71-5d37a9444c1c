import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/models/expense.dart';

class ExpenseDetailsScreen extends StatefulWidget {
  final Expense? expense;

  ExpenseDetailsScreen({this.expense});

  @override
  _ExpenseDetailsScreenState createState() => _ExpenseDetailsScreenState();
}

class _ExpenseDetailsScreenState extends State<ExpenseDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late DateTime _expenseDate;
  late String _category;
  late double _amount;
  late String _description;

  @override
  void initState() {
    super.initState();
    _expenseDate = widget.expense?.expenseDate ?? DateTime.now();
    _category = widget.expense?.category ?? '';
    _amount = widget.expense?.amount ?? 0.0;
    _description = widget.expense?.description ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.expense == null ? 'Add Expense' : 'Edit Expense'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              TextFormField(
                initialValue: _expenseDate.toString(),
                decoration: const InputDecoration(labelText: 'Expense Date'),
                onSaved: (String? value) =>
                    _expenseDate = DateTime.parse(value!),
              ),
              TextFormField(
                initialValue: _category,
                decoration: const InputDecoration(labelText: 'Category'),
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a category';
                  }
                  return null;
                },
                onSaved: (String? value) => _category = value!,
              ),
              TextFormField(
                initialValue: _amount.toString(),
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  return null;
                },
                onSaved: (String? value) => _amount = double.parse(value!),
              ),
              TextFormField(
                initialValue: _description,
                decoration: const InputDecoration(labelText: 'Description'),
                onSaved: (String? value) => _description = value!,
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final Expense expense = Expense(
                      id: widget.expense?.id,
                      expenseDate: _expenseDate,
                      category: _category,
                      amount: _amount,
                      description: _description,
                    );
                    final ExpenseProvider expenseProvider =
                        Provider.of<ExpenseProvider>(context, listen: false);
                    if (widget.expense == null) {
                      expenseProvider.addExpense(expense);
                    } else {
                      expenseProvider.updateExpense(expense);
                    }
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
