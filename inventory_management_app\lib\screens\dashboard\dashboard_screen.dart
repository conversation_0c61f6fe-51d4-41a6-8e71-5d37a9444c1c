import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/reports_provider.dart';
import 'package:inventory_management_app/widgets/custom_app_bar.dart';
import 'package:inventory_management_app/widgets/empty_state_widget.dart'
    as widgets;

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReportsProvider>().loadDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Dashboard',
        showBackButton: false,
      ),
      body: Consumer<ReportsProvider>(
        builder: (BuildContext context, ReportsProvider reportsProvider, Widget? child) {
          if (reportsProvider.isLoading) {
            return const widgets.LoadingWidget(message: 'Loading dashboard...');
          }

          if (reportsProvider.error != null) {
            return widgets.ErrorWidget(
              message: reportsProvider.error!,
              onRetry: () => reportsProvider.loadDashboardData(),
            );
          }

          final Map<String, dynamic> data = reportsProvider.dashboardData;
          if (data.isEmpty) {
            return const widgets.EmptyStateWidget(
              title: 'No Data Available',
              message: 'Dashboard data is not available at the moment',
              icon: Icons.dashboard,
            );
          }

          return RefreshIndicator(
            onRefresh: () => reportsProvider.loadDashboardData(),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    'Overview',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildStatsGrid(data),
                  const SizedBox(height: 24),
                  Text(
                    'Financial Summary',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildFinancialCards(data),
                  const SizedBox(height: 24),
                  _buildQuickActions(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatsGrid(Map<String, dynamic> data) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: <Widget>[
        _buildStatCard(
          'Products',
          '${data['totalProducts'] ?? 0}',
          Icons.inventory,
          Colors.blue,
        ),
        _buildStatCard(
          'Customers',
          '${data['totalCustomers'] ?? 0}',
          Icons.people,
          Colors.green,
        ),
        _buildStatCard(
          'Suppliers',
          '${data['totalSuppliers'] ?? 0}',
          Icons.business,
          Colors.orange,
        ),
        _buildStatCard(
          'Sales',
          '${data['salesCount'] ?? 0}',
          Icons.shopping_cart,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialCards(Map<String, dynamic> data) {
    return Column(
      children: <Widget>[
        _buildFinancialCard(
          'Total Sales',
          '\$${(data['totalSales'] as double?)?.toStringAsFixed(2) ?? '0.00'}',
          Icons.trending_up,
          Colors.green,
        ),
        const SizedBox(height: 12),
        _buildFinancialCard(
          'Total Purchases',
          '\$${(data['totalPurchases'] as double?)?.toStringAsFixed(2) ?? '0.00'}',
          Icons.shopping_bag,
          Colors.orange,
        ),
        const SizedBox(height: 12),
        _buildFinancialCard(
          'Total Expenses',
          '\$${(data['totalExpenses'] as double?)?.toStringAsFixed(2) ?? '0.00'}',
          Icons.money_off,
          Colors.red,
        ),
        const SizedBox(height: 12),
        _buildFinancialCard(
          'Net Profit',
          '\$${(data['netProfit'] as double?)?.toStringAsFixed(2) ?? '0.00'}',
          Icons.account_balance_wallet,
          (data['netProfit'] as double? ?? 0) >= 0 ? Colors.green : Colors.red,
        ),
      ],
    );
  }

  Widget _buildFinancialCard(
      String title, String amount, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        trailing: Text(
          amount,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2,
          children: <Widget>[
            _buildActionCard('Add Product', Icons.add_box, () {}),
            _buildActionCard('New Sale', Icons.point_of_sale, () {}),
            _buildActionCard('View Reports', Icons.analytics, () {}),
            _buildActionCard('Low Stock', Icons.warning, () {}),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: <Widget>[
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
