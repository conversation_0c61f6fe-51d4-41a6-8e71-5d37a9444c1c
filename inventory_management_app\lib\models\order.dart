/// Model class representing a customer order
class Order {
  /// Unique identifier for the order
  int? id;

  /// Customer ID for this order
  int? customerId;

  /// Date of the order
  String? date;

  /// Status of the order (e.g., "pending", "completed", "cancelled")
  String? status;

  /// Total amount of the order
  double? total;

  /// Constructor for creating an Order instance
  Order({
    this.id,
    this.customerId,
    this.date,
    this.status,
    this.total,
  });

  /// Converts the Order instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'customerId': customerId,
      'date': date,
      'status': status,
      'total': total,
    };
  }

  /// Creates an Order instance from a Map (typically from database)
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      date: map['date'] as String?,
      status: map['status'] as String?,
      total: map['total']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'Order{id: $id, customerId: $customerId, date: $date, '
        'status: $status, total: $total}';
  }
}
