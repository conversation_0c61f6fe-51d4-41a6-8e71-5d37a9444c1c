import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/business_logic_service.dart';

/// Provider class for managing product state and operations
class ProductProvider extends ChangeNotifier {
  List<Product> _products = <Product>[];
  final ProductService _productService = ProductService();
  final BusinessLogicService _businessLogic = BusinessLogicService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of products
  List<Product> get products => _products;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load products from database
  Future<void> initialize() async {
    await fetchProducts();
  }

  /// Fetch all products from the database
  Future<void> fetchProducts() async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getAllProducts();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch products: $e');
      // Keep existing products if fetch fails
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new product
  Future<void> addProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      // Add to database
      await _productService.insertProduct(product);

      // Add to local list for immediate UI update
      _products.add(product);
      notifyListeners();

      // Refresh from database to get the ID
      await fetchProducts();
    } catch (e) {
      _setError('Failed to add product: $e');
      // Remove from local list if database operation failed
      _products.removeWhere((p) => p.name == product.name);
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing product
  Future<void> updateProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProduct(product);
      await fetchProducts();
    } catch (e) {
      _setError('Failed to update product: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a product
  Future<void> deleteProduct(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.deleteProduct(id);
      await fetchProducts();
    } catch (e) {
      _setError('Failed to delete product: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search products by name
  Future<void> searchProducts(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.searchProductsByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search products: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Validate product name uniqueness within category
  Future<bool> validateProductName(String name, int? categoryId,
      {int? excludeId}) async {
    try {
      return await _businessLogic.isProductNameUniqueInCategory(
          name, categoryId,
          excludeProductId: excludeId);
    } catch (e) {
      _setError('Failed to validate product name: $e');
      return false;
    }
  }

  /// Get low stock products
  Future<List<Map<String, dynamic>>> getLowStockReport(double threshold) async {
    try {
      return await _businessLogic.getLowStockReport(threshold);
    } catch (e) {
      _setError('Failed to get low stock report: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get products by category with loading state
  Future<void> getProductsByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getProductsByCategory(categoryId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get products by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get products by supplier with loading state
  Future<void> getProductsBySupplier(int supplierId) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getProductsBySupplier(supplierId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get products by supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get low stock products with loading state
  Future<void> getLowStockProducts(double threshold) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getLowStockProducts(threshold);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get low stock products: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update product quantity
  Future<void> updateProductQuantity(int productId, double newQuantity) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProductQuantity(productId, newQuantity);
      await fetchProducts(); // Refresh the list
    } catch (e) {
      _setError('Failed to update product quantity: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get product count
  Future<int> getProductCount() async {
    try {
      return await _productService.getProductCount();
    } catch (e) {
      _setError('Failed to get product count: $e');
      return 0;
    }
  }

  /// Clear all products (useful for filtering)
  void clearProducts() {
    _products.clear();
    notifyListeners();
  }

  /// Reset to show all products
  Future<void> resetProducts() async {
    await fetchProducts();
  }
}
