import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التلعيب والتحفيز
class GamificationService {
  static final GamificationService _instance = GamificationService._internal();
  factory GamificationService() => _instance;
  GamificationService._internal();

  static GamificationService get instance => _instance;

  // مفاتيح التخزين
  static const String _pointsKey = 'user_points';
  static const String _levelKey = 'user_level';
  static const String _badgesKey = 'user_badges';
  static const String _streakKey = 'daily_streak';
  static const String _lastLoginKey = 'last_login_date';
  static const String _achievementsKey = 'achievements';

  /// الحصول على نقاط المستخدم
  Future<int> getUserPoints() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_pointsKey) ?? 0;
  }

  /// إضافة نقاط للمستخدم
  Future<void> addPoints(int points, String reason) async {
    final prefs = await SharedPreferences.getInstance();
    final currentPoints = await getUserPoints();
    final newPoints = currentPoints + points;
    
    await prefs.setInt(_pointsKey, newPoints);
    
    // التحقق من ترقية المستوى
    await _checkLevelUp(newPoints);
    
    debugPrint('تم إضافة $points نقطة للمستخدم. السبب: $reason');
    debugPrint('إجمالي النقاط: $newPoints');
  }

  /// الحصول على مستوى المستخدم
  Future<int> getUserLevel() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_levelKey) ?? 1;
  }

  /// حساب المستوى من النقاط
  int calculateLevelFromPoints(int points) {
    // كل 1000 نقطة = مستوى واحد
    return (points / 1000).floor() + 1;
  }

  /// التحقق من ترقية المستوى
  Future<bool> _checkLevelUp(int points) async {
    final prefs = await SharedPreferences.getInstance();
    final currentLevel = await getUserLevel();
    final newLevel = calculateLevelFromPoints(points);
    
    if (newLevel > currentLevel) {
      await prefs.setInt(_levelKey, newLevel);
      debugPrint('تهانينا! تم ترقيتك إلى المستوى $newLevel');
      return true;
    }
    return false;
  }

  /// الحصول على الشارات
  Future<List<String>> getUserBadges() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_badgesKey) ?? [];
  }

  /// إضافة شارة جديدة
  Future<void> addBadge(String badgeId, String badgeName) async {
    final prefs = await SharedPreferences.getInstance();
    final badges = await getUserBadges();
    
    if (!badges.contains(badgeId)) {
      badges.add(badgeId);
      await prefs.setStringList(_badgesKey, badges);
      debugPrint('تم الحصول على شارة جديدة: $badgeName');
    }
  }

  /// الحصول على السلسلة اليومية
  Future<int> getDailyStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_streakKey) ?? 0;
  }

  /// تحديث السلسلة اليومية
  Future<void> updateDailyStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final lastLoginString = prefs.getString(_lastLoginKey);
    
    if (lastLoginString != null) {
      final lastLogin = DateTime.parse(lastLoginString);
      final daysDifference = today.difference(lastLogin).inDays;
      
      if (daysDifference == 1) {
        // يوم متتالي
        final currentStreak = await getDailyStreak();
        await prefs.setInt(_streakKey, currentStreak + 1);
        await addPoints(50, 'تسجيل دخول يومي متتالي');
      } else if (daysDifference > 1) {
        // انقطعت السلسلة
        await prefs.setInt(_streakKey, 1);
      }
      // إذا كان نفس اليوم، لا نفعل شيئاً
    } else {
      // أول تسجيل دخول
      await prefs.setInt(_streakKey, 1);
    }
    
    await prefs.setString(_lastLoginKey, today.toIso8601String());
  }

  /// إضافة نقاط لإضافة منتج
  Future<void> onProductAdded() async {
    await addPoints(100, 'إضافة منتج جديد');
    await _checkAchievement('first_product', 'أول منتج');
  }

  /// إضافة نقاط لإضافة عميل
  Future<void> onCustomerAdded() async {
    await addPoints(75, 'إضافة عميل جديد');
    await _checkAchievement('first_customer', 'أول عميل');
  }

  /// إضافة نقاط لإتمام بيع
  Future<void> onSaleCompleted(double amount) async {
    final points = (amount / 10).round(); // نقطة لكل 10 ريال
    await addPoints(points, 'إتمام عملية بيع');
    await _checkAchievement('first_sale', 'أول بيع');
    
    // شارات خاصة للمبيعات الكبيرة
    if (amount >= 1000) {
      await addBadge('big_sale', 'بيع كبير');
    }
    if (amount >= 5000) {
      await addBadge('mega_sale', 'بيع ضخم');
    }
  }

  /// إضافة نقاط لاستخدام ميزة متقدمة
  Future<void> onAdvancedFeatureUsed(String feature) async {
    await addPoints(25, 'استخدام ميزة متقدمة: $feature');
  }

  /// إضافة نقاط للنسخ الاحتياطي
  Future<void> onBackupCreated() async {
    await addPoints(200, 'إنشاء نسخة احتياطية');
    await addBadge('backup_master', 'خبير النسخ الاحتياطي');
  }

  /// التحقق من الإنجازات
  Future<void> _checkAchievement(String achievementId, String achievementName) async {
    final prefs = await SharedPreferences.getInstance();
    final achievements = prefs.getStringList(_achievementsKey) ?? [];
    
    if (!achievements.contains(achievementId)) {
      achievements.add(achievementId);
      await prefs.setStringList(_achievementsKey, achievements);
      await addPoints(500, 'إنجاز جديد: $achievementName');
      debugPrint('تم فتح إنجاز جديد: $achievementName');
    }
  }

  /// الحصول على الإنجازات
  Future<List<String>> getAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_achievementsKey) ?? [];
  }

  /// الحصول على التقدم نحو المستوى التالي
  Future<Map<String, dynamic>> getLevelProgress() async {
    final points = await getUserPoints();
    final currentLevel = await getUserLevel();
    final pointsForCurrentLevel = (currentLevel - 1) * 1000;
    final pointsForNextLevel = currentLevel * 1000;
    final progressPoints = points - pointsForCurrentLevel;
    final totalPointsNeeded = pointsForNextLevel - pointsForCurrentLevel;
    final progressPercentage = (progressPoints / totalPointsNeeded * 100).clamp(0, 100);
    
    return {
      'current_level': currentLevel,
      'next_level': currentLevel + 1,
      'progress_points': progressPoints,
      'total_points_needed': totalPointsNeeded,
      'progress_percentage': progressPercentage,
      'points_to_next_level': totalPointsNeeded - progressPoints,
    };
  }

  /// الحصول على إحصائيات المستخدم
  Future<Map<String, dynamic>> getUserStats() async {
    final points = await getUserPoints();
    final level = await getUserLevel();
    final badges = await getUserBadges();
    final streak = await getDailyStreak();
    final achievements = await getAchievements();
    final levelProgress = await getLevelProgress();
    
    return {
      'points': points,
      'level': level,
      'badges_count': badges.length,
      'daily_streak': streak,
      'achievements_count': achievements.length,
      'level_progress': levelProgress,
    };
  }

  /// إعادة تعيين جميع البيانات
  Future<void> resetAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_pointsKey);
    await prefs.remove(_levelKey);
    await prefs.remove(_badgesKey);
    await prefs.remove(_streakKey);
    await prefs.remove(_lastLoginKey);
    await prefs.remove(_achievementsKey);
    debugPrint('تم إعادة تعيين جميع بيانات التلعيب');
  }

  /// الحصول على قائمة الشارات المتاحة
  Map<String, Map<String, String>> getAvailableBadges() {
    return {
      'first_product': {
        'name': 'أول منتج',
        'description': 'أضف أول منتج لك',
        'icon': '🏷️',
      },
      'first_customer': {
        'name': 'أول عميل',
        'description': 'أضف أول عميل لك',
        'icon': '👤',
      },
      'first_sale': {
        'name': 'أول بيع',
        'description': 'أتمم أول عملية بيع',
        'icon': '💰',
      },
      'big_sale': {
        'name': 'بيع كبير',
        'description': 'أتمم بيع بقيمة 1000 ريال أو أكثر',
        'icon': '💎',
      },
      'mega_sale': {
        'name': 'بيع ضخم',
        'description': 'أتمم بيع بقيمة 5000 ريال أو أكثر',
        'icon': '🏆',
      },
      'backup_master': {
        'name': 'خبير النسخ الاحتياطي',
        'description': 'أنشئ نسخة احتياطية من بياناتك',
        'icon': '💾',
      },
      'streak_7': {
        'name': 'أسبوع متتالي',
        'description': 'سجل دخول لمدة 7 أيام متتالية',
        'icon': '🔥',
      },
      'streak_30': {
        'name': 'شهر متتالي',
        'description': 'سجل دخول لمدة 30 يوم متتالي',
        'icon': '⭐',
      },
    };
  }

  /// الحصول على قائمة الإنجازات المتاحة
  Map<String, Map<String, String>> getAvailableAchievements() {
    return {
      'first_product': {
        'name': 'بداية الرحلة',
        'description': 'أضف أول منتج لك',
        'points': '500',
      },
      'first_customer': {
        'name': 'أول صديق',
        'description': 'أضف أول عميل لك',
        'points': '500',
      },
      'first_sale': {
        'name': 'أول نجاح',
        'description': 'أتمم أول عملية بيع',
        'points': '500',
      },
      'products_10': {
        'name': 'تاجر صغير',
        'description': 'أضف 10 منتجات',
        'points': '1000',
      },
      'products_50': {
        'name': 'تاجر كبير',
        'description': 'أضف 50 منتج',
        'points': '2500',
      },
      'sales_100': {
        'name': 'بائع محترف',
        'description': 'أتمم 100 عملية بيع',
        'points': '5000',
      },
    };
  }
}

/// Provider للتلعيب
class GamificationProvider extends ChangeNotifier {
  final GamificationService _service = GamificationService.instance;
  
  int _points = 0;
  int _level = 1;
  List<String> _badges = [];
  int _dailyStreak = 0;
  Map<String, dynamic> _levelProgress = {};
  bool _isLoading = false;

  int get points => _points;
  int get level => _level;
  List<String> get badges => _badges;
  int get dailyStreak => _dailyStreak;
  Map<String, dynamic> get levelProgress => _levelProgress;
  bool get isLoading => _isLoading;

  /// تهيئة البيانات
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _service.updateDailyStreak();
      await _loadUserStats();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل إحصائيات المستخدم
  Future<void> _loadUserStats() async {
    _points = await _service.getUserPoints();
    _level = await _service.getUserLevel();
    _badges = await _service.getUserBadges();
    _dailyStreak = await _service.getDailyStreak();
    _levelProgress = await _service.getLevelProgress();
    notifyListeners();
  }

  /// إضافة نقاط
  Future<void> addPoints(int points, String reason) async {
    await _service.addPoints(points, reason);
    await _loadUserStats();
  }

  /// إضافة شارة
  Future<void> addBadge(String badgeId, String badgeName) async {
    await _service.addBadge(badgeId, badgeName);
    await _loadUserStats();
  }

  /// إضافة منتج
  Future<void> onProductAdded() async {
    await _service.onProductAdded();
    await _loadUserStats();
  }

  /// إضافة عميل
  Future<void> onCustomerAdded() async {
    await _service.onCustomerAdded();
    await _loadUserStats();
  }

  /// إتمام بيع
  Future<void> onSaleCompleted(double amount) async {
    await _service.onSaleCompleted(amount);
    await _loadUserStats();
  }

  /// استخدام ميزة متقدمة
  Future<void> onAdvancedFeatureUsed(String feature) async {
    await _service.onAdvancedFeatureUsed(feature);
    await _loadUserStats();
  }

  /// إنشاء نسخة احتياطية
  Future<void> onBackupCreated() async {
    await _service.onBackupCreated();
    await _loadUserStats();
  }

  /// إعادة تعيين البيانات
  Future<void> resetAllData() async {
    await _service.resetAllData();
    await _loadUserStats();
  }
}
