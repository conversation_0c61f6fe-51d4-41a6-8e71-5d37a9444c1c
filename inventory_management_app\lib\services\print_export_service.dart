import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../models/sale.dart';
import '../models/product.dart';
import '../models/customer.dart';

/// خدمة الطباعة والتصدير
class PrintExportService {
  static final PrintExportService _instance = PrintExportService._internal();
  factory PrintExportService() => _instance;
  PrintExportService._internal();

  static PrintExportService get instance => _instance;

  /// إنشاء فاتورة PDF
  Future<String?> generateInvoicePDF(Sale sale, Customer? customer, List<Product> products) async {
    try {
      // في المستقبل يمكن إضافة pdf package هنا
      debugPrint('إنشاء فاتورة PDF للبيع رقم ${sale.id}');
      
      final invoiceContent = _generateInvoiceContent(sale, customer, products);
      
      // محاكاة إنشاء PDF
      await Future.delayed(const Duration(seconds: 2));
      
      final directory = await getApplicationDocumentsDirectory();
      final invoicesDir = Directory('${directory.path}/invoices');
      
      if (!await invoicesDir.exists()) {
        await invoicesDir.create(recursive: true);
      }
      
      final fileName = 'invoice_${sale.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${invoicesDir.path}/$fileName';
      
      // محاكاة حفظ الملف
      final file = File(filePath);
      await file.writeAsString(invoiceContent);
      
      debugPrint('تم إنشاء الفاتورة: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في إنشاء فاتورة PDF: $e');
      return null;
    }
  }

  /// طباعة الفاتورة
  Future<bool> printInvoice(Sale sale, Customer? customer, List<Product> products) async {
    try {
      // في المستقبل يمكن إضافة printing package هنا
      debugPrint('طباعة الفاتورة للبيع رقم ${sale.id}');
      
      final invoiceContent = _generateInvoiceContent(sale, customer, products);
      
      // محاكاة الطباعة
      await Future.delayed(const Duration(seconds: 3));
      
      debugPrint('تم إرسال الفاتورة للطابعة');
      return true;
    } catch (e) {
      debugPrint('خطأ في طباعة الفاتورة: $e');
      return false;
    }
  }

  /// تصدير تقرير المبيعات إلى Excel
  Future<String?> exportSalesReportToExcel(List<Sale> sales, DateTime startDate, DateTime endDate) async {
    try {
      // في المستقبل يمكن إضافة excel package هنا
      debugPrint('تصدير تقرير المبيعات إلى Excel');
      
      final reportContent = _generateSalesReportContent(sales, startDate, endDate);
      
      // محاكاة إنشاء Excel
      await Future.delayed(const Duration(seconds: 2));
      
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');
      
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }
      
      final fileName = 'sales_report_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${reportsDir.path}/$fileName';
      
      // محاكاة حفظ الملف
      final file = File(filePath);
      await file.writeAsString(reportContent);
      
      debugPrint('تم تصدير التقرير: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير Excel: $e');
      return null;
    }
  }

  /// تصدير قائمة المنتجات إلى CSV
  Future<String?> exportProductsToCSV(List<Product> products) async {
    try {
      debugPrint('تصدير قائمة المنتجات إلى CSV');
      
      final csvContent = _generateProductsCSVContent(products);
      
      final directory = await getApplicationDocumentsDirectory();
      final exportsDir = Directory('${directory.path}/exports');
      
      if (!await exportsDir.exists()) {
        await exportsDir.create(recursive: true);
      }
      
      final fileName = 'products_${DateTime.now().millisecondsSinceEpoch}.csv';
      final filePath = '${exportsDir.path}/$fileName';
      
      final file = File(filePath);
      await file.writeAsString(csvContent, encoding: const Utf8Codec());
      
      debugPrint('تم تصدير المنتجات: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير CSV: $e');
      return null;
    }
  }

  /// إنشاء باركود للمنتج
  Future<Uint8List?> generateProductBarcode(String productCode) async {
    try {
      // في المستقبل يمكن إضافة barcode package هنا
      debugPrint('إنشاء باركود للمنتج: $productCode');
      
      // محاكاة إنشاء الباركود
      await Future.delayed(const Duration(seconds: 1));
      
      // إنشاء صورة باركود وهمية (مربع أسود)
      final barcodeData = Uint8List(1000);
      for (int i = 0; i < barcodeData.length; i++) {
        barcodeData[i] = (i % 2 == 0) ? 0 : 255;
      }
      
      debugPrint('تم إنشاء الباركود للمنتج');
      return barcodeData;
    } catch (e) {
      debugPrint('خطأ في إنشاء الباركود: $e');
      return null;
    }
  }

  /// طباعة ملصقات المنتجات
  Future<bool> printProductLabels(List<Product> products) async {
    try {
      debugPrint('طباعة ملصقات المنتجات');
      
      // محاكاة طباعة الملصقات
      await Future.delayed(const Duration(seconds: 2));
      
      for (final product in products) {
        debugPrint('طباعة ملصق للمنتج: ${product.name}');
      }
      
      debugPrint('تم إرسال الملصقات للطابعة');
      return true;
    } catch (e) {
      debugPrint('خطأ في طباعة الملصقات: $e');
      return false;
    }
  }

  /// إرسال الفاتورة بالبريد الإلكتروني
  Future<bool> emailInvoice(String email, String invoicePath, Sale sale) async {
    try {
      // في المستقبل يمكن إضافة mailer package هنا
      debugPrint('إرسال الفاتورة بالبريد الإلكتروني إلى: $email');
      
      // محاكاة إرسال البريد الإلكتروني
      await Future.delayed(const Duration(seconds: 2));
      
      debugPrint('تم إرسال الفاتورة بالبريد الإلكتروني');
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال البريد الإلكتروني: $e');
      return false;
    }
  }

  /// طباعة تقرير المخزون
  Future<bool> printInventoryReport(List<Product> products) async {
    try {
      debugPrint('طباعة تقرير المخزون');
      
      final reportContent = _generateInventoryReportContent(products);
      
      // محاكاة الطباعة
      await Future.delayed(const Duration(seconds: 2));
      
      debugPrint('تم إرسال تقرير المخزون للطابعة');
      return true;
    } catch (e) {
      debugPrint('خطأ في طباعة تقرير المخزون: $e');
      return false;
    }
  }

  /// إنشاء QR Code للفاتورة
  Future<Uint8List?> generateInvoiceQRCode(Sale sale) async {
    try {
      // في المستقبل يمكن إضافة qr package هنا
      debugPrint('إنشاء QR Code للفاتورة رقم ${sale.id}');
      
      // محاكاة إنشاء QR Code
      await Future.delayed(const Duration(seconds: 1));
      
      // إنشاء QR Code وهمي
      final qrData = Uint8List(500);
      for (int i = 0; i < qrData.length; i++) {
        qrData[i] = (i % 3 == 0) ? 0 : 255;
      }
      
      debugPrint('تم إنشاء QR Code للفاتورة');
      return qrData;
    } catch (e) {
      debugPrint('خطأ في إنشاء QR Code: $e');
      return null;
    }
  }

  /// حفظ الفاتورة كصورة
  Future<String?> saveInvoiceAsImage(Sale sale, Customer? customer, List<Product> products) async {
    try {
      debugPrint('حفظ الفاتورة كصورة');
      
      // محاكاة إنشاء صورة الفاتورة
      await Future.delayed(const Duration(seconds: 2));
      
      final directory = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${directory.path}/invoice_images');
      
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }
      
      final fileName = 'invoice_${sale.id}_${DateTime.now().millisecondsSinceEpoch}.png';
      final filePath = '${imagesDir.path}/$fileName';
      
      // محاكاة حفظ الصورة
      final file = File(filePath);
      await file.writeAsBytes(Uint8List(1000));
      
      debugPrint('تم حفظ الفاتورة كصورة: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('خطأ في حفظ الفاتورة كصورة: $e');
      return null;
    }
  }

  /// إنشاء محتوى الفاتورة
  String _generateInvoiceContent(Sale sale, Customer? customer, List<Product> products) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== فاتورة مبيعات ===');
    buffer.writeln('رقم الفاتورة: ${sale.id}');
    buffer.writeln('التاريخ: ${sale.date ?? DateTime.now().toIso8601String()}');
    buffer.writeln('');
    
    if (customer != null) {
      buffer.writeln('بيانات العميل:');
      buffer.writeln('الاسم: ${customer.name}');
      buffer.writeln('الهاتف: ${customer.phone ?? 'غير محدد'}');
      buffer.writeln('العنوان: ${customer.address ?? 'غير محدد'}');
      buffer.writeln('');
    }
    
    buffer.writeln('تفاصيل المنتجات:');
    buffer.writeln('المنتج\t\tالسعر\t\tالكمية\t\tالإجمالي');
    buffer.writeln('----------------------------------------');
    
    double totalAmount = 0;
    for (final product in products) {
      final price = product.price ?? 0;
      final quantity = product.quantity ?? 0;
      final itemTotal = price * quantity;
      totalAmount += itemTotal;
      
      buffer.writeln('${product.name}\t\t${price.toStringAsFixed(2)}\t\t${quantity.toStringAsFixed(0)}\t\t${itemTotal.toStringAsFixed(2)}');
    }
    
    buffer.writeln('----------------------------------------');
    buffer.writeln('المجموع الكلي: ${totalAmount.toStringAsFixed(2)} ر.س');
    buffer.writeln('');
    buffer.writeln('شكراً لتعاملكم معنا');
    
    return buffer.toString();
  }

  /// إنشاء محتوى تقرير المبيعات
  String _generateSalesReportContent(List<Sale> sales, DateTime startDate, DateTime endDate) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== تقرير المبيعات ===');
    buffer.writeln('من: ${startDate.day}/${startDate.month}/${startDate.year}');
    buffer.writeln('إلى: ${endDate.day}/${endDate.month}/${endDate.year}');
    buffer.writeln('');
    
    double totalRevenue = 0;
    buffer.writeln('رقم البيع\t\tالتاريخ\t\tالمبلغ');
    buffer.writeln('--------------------------------');
    
    for (final sale in sales) {
      final amount = sale.total ?? 0;
      totalRevenue += amount;
      buffer.writeln('${sale.id}\t\t${sale.date ?? 'غير محدد'}\t\t${amount.toStringAsFixed(2)}');
    }
    
    buffer.writeln('--------------------------------');
    buffer.writeln('إجمالي الإيرادات: ${totalRevenue.toStringAsFixed(2)} ر.س');
    buffer.writeln('عدد المبيعات: ${sales.length}');
    buffer.writeln('متوسط البيع: ${sales.isNotEmpty ? (totalRevenue / sales.length).toStringAsFixed(2) : '0.00'} ر.س');
    
    return buffer.toString();
  }

  /// إنشاء محتوى CSV للمنتجات
  String _generateProductsCSVContent(List<Product> products) {
    final buffer = StringBuffer();
    
    // رأس الجدول
    buffer.writeln('الرقم,اسم المنتج,الوصف,السعر,الكمية,القيمة الإجمالية');
    
    // بيانات المنتجات
    for (final product in products) {
      final price = product.price ?? 0;
      final quantity = product.quantity ?? 0;
      final totalValue = price * quantity;
      
      buffer.writeln('${product.id ?? ''},"${product.name}","${product.description ?? ''}",${price.toStringAsFixed(2)},${quantity.toStringAsFixed(0)},${totalValue.toStringAsFixed(2)}');
    }
    
    return buffer.toString();
  }

  /// إنشاء محتوى تقرير المخزون
  String _generateInventoryReportContent(List<Product> products) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== تقرير المخزون ===');
    buffer.writeln('تاريخ التقرير: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}');
    buffer.writeln('');
    
    buffer.writeln('المنتج\t\tالكمية\t\tالسعر\t\tالقيمة');
    buffer.writeln('----------------------------------------');
    
    double totalValue = 0;
    int lowStockCount = 0;
    
    for (final product in products) {
      final price = product.price ?? 0;
      final quantity = product.quantity ?? 0;
      final itemValue = price * quantity;
      totalValue += itemValue;
      
      if (quantity <= 10) lowStockCount++;
      
      buffer.writeln('${product.name}\t\t${quantity.toStringAsFixed(0)}\t\t${price.toStringAsFixed(2)}\t\t${itemValue.toStringAsFixed(2)}');
    }
    
    buffer.writeln('----------------------------------------');
    buffer.writeln('إجمالي قيمة المخزون: ${totalValue.toStringAsFixed(2)} ر.س');
    buffer.writeln('عدد المنتجات: ${products.length}');
    buffer.writeln('منتجات منخفضة المخزون: $lowStockCount');
    
    return buffer.toString();
  }
}

/// Provider للطباعة والتصدير
class PrintExportProvider extends ChangeNotifier {
  final PrintExportService _service = PrintExportService.instance;
  
  bool _isGeneratingPDF = false;
  bool _isPrinting = false;
  bool _isExporting = false;
  String? _lastGeneratedFile;

  bool get isGeneratingPDF => _isGeneratingPDF;
  bool get isPrinting => _isPrinting;
  bool get isExporting => _isExporting;
  String? get lastGeneratedFile => _lastGeneratedFile;

  /// إنشاء فاتورة PDF
  Future<String?> generateInvoicePDF(Sale sale, Customer? customer, List<Product> products) async {
    _isGeneratingPDF = true;
    notifyListeners();

    try {
      _lastGeneratedFile = await _service.generateInvoicePDF(sale, customer, products);
      return _lastGeneratedFile;
    } finally {
      _isGeneratingPDF = false;
      notifyListeners();
    }
  }

  /// طباعة الفاتورة
  Future<bool> printInvoice(Sale sale, Customer? customer, List<Product> products) async {
    _isPrinting = true;
    notifyListeners();

    try {
      return await _service.printInvoice(sale, customer, products);
    } finally {
      _isPrinting = false;
      notifyListeners();
    }
  }

  /// تصدير تقرير المبيعات
  Future<String?> exportSalesReport(List<Sale> sales, DateTime startDate, DateTime endDate) async {
    _isExporting = true;
    notifyListeners();

    try {
      _lastGeneratedFile = await _service.exportSalesReportToExcel(sales, startDate, endDate);
      return _lastGeneratedFile;
    } finally {
      _isExporting = false;
      notifyListeners();
    }
  }

  /// تصدير المنتجات
  Future<String?> exportProducts(List<Product> products) async {
    _isExporting = true;
    notifyListeners();

    try {
      _lastGeneratedFile = await _service.exportProductsToCSV(products);
      return _lastGeneratedFile;
    } finally {
      _isExporting = false;
      notifyListeners();
    }
  }
}
