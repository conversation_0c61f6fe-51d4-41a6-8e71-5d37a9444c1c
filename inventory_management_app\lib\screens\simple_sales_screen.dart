import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/sale_provider.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../models/sale.dart';

class SimpleSalesScreen extends StatefulWidget {
  const SimpleSalesScreen({super.key});

  @override
  State<SimpleSalesScreen> createState() => _SimpleSalesScreenState();
}

class _SimpleSalesScreenState extends State<SimpleSalesScreen> {
  final _totalController = TextEditingController();
  final _searchController = TextEditingController();
  String? _selectedCustomer;
  String? _selectedProduct;

  List<Sale> _filteredSales = [];

  @override
  void dispose() {
    _totalController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterSales(String query) {
    final saleProvider = context.read<SaleProvider>();
    setState(() {
      if (query.isEmpty) {
        _filteredSales = saleProvider.sales;
      } else {
        _filteredSales = saleProvider.sales
            .where((sale) => sale.id.toString().contains(query) ||
                (sale.totalAmount?.toString().contains(query) ?? false))
            .toList();
      }
    });
  }

  void _addSale() {
    if (_totalController.text.isNotEmpty) {
      final sale = Sale(
        customerId: _selectedCustomer != null ? int.tryParse(_selectedCustomer!) : null,
        total: double.tryParse(_totalController.text) ?? 0.0,
        date: DateTime.now().toIso8601String(),
      );

      context.read<SaleProvider>().addSale(sale);

      _totalController.clear();
      _selectedCustomer = null;
      _selectedProduct = null;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إضافة البيع بنجاح')),
      );

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('إدارة المبيعات'),
          backgroundColor: Colors.purple,
          foregroundColor: Colors.white,
        ),
        body: Column(
          children: [
            // نموذج إضافة بيع
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إضافة بيع جديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // اختيار العميل
                  Consumer<CustomerProvider>(
                    builder: (context, customerProvider, child) {
                      return DropdownButtonFormField<String>(
                        value: _selectedCustomer,
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.person),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('عميل نقدي'),
                          ),
                          ...customerProvider.customers.map((customer) {
                            return DropdownMenuItem<String>(
                              value: customer.id.toString(),
                              child: Text(customer.name),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCustomer = value;
                          });
                        },
                      );
                    },
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // اختيار المنتج
                  Consumer<ProductProvider>(
                    builder: (context, productProvider, child) {
                      return DropdownButtonFormField<String>(
                        value: _selectedProduct,
                        decoration: const InputDecoration(
                          labelText: 'المنتج',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.inventory_2),
                        ),
                        items: productProvider.products.map((product) {
                          return DropdownMenuItem<String>(
                            value: product.id.toString(),
                            child: Text('${product.name} - ${product.price?.toStringAsFixed(2)} ر.س'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedProduct = value;
                            // تعبئة السعر تلقائياً
                            if (value != null) {
                              final product = productProvider.products
                                  .firstWhere((p) => p.id.toString() == value);
                              _totalController.text = product.price?.toStringAsFixed(2) ?? '0.00';
                            }
                          });
                        },
                      );
                    },
                  ),
                  
                  const SizedBox(height: 12),
                  
                  TextField(
                    controller: _totalController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'المبلغ الإجمالي *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                      suffixText: 'ر.س',
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _addSale,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إضافة البيع'),
                    ),
                  ),
                ],
              ),
            ),

            // حقل البحث
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  labelText: 'البحث في المبيعات',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _filterSales('');
                          },
                        )
                      : null,
                ),
                onChanged: _filterSales,
              ),
            ),

            const SizedBox(height: 16),

            // قائمة المبيعات
            Expanded(
              child: Consumer3<SaleProvider, CustomerProvider, ProductProvider>(
                builder: (context, saleProvider, customerProvider, productProvider, child) {
                  final salesToShow = _searchController.text.isEmpty
                      ? saleProvider.sales
                      : _filteredSales;

                  if (salesToShow.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد مبيعات',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'أضف بيعك الأول باستخدام النموذج أعلاه',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: salesToShow.length,
                    itemBuilder: (context, index) {
                      final sale = salesToShow[index];
                      final customer = sale.customerId != null
                          ? customerProvider.customers
                              .where((c) => c.id == sale.customerId)
                              .firstOrNull
                          : null;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        child: ListTile(
                          leading: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.purple.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.receipt,
                              color: Colors.purple,
                            ),
                          ),
                          title: Text(
                            'فاتورة رقم ${sale.id}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('العميل: ${customer?.name ?? 'عميل نقدي'}'),
                              if (sale.date != null)
                                Text('التاريخ: ${DateTime.parse(sale.date!).day}/${DateTime.parse(sale.date!).month}/${DateTime.parse(sale.date!).year}'),
                            ],
                          ),
                          trailing: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '${sale.totalAmount?.toStringAsFixed(2) ?? '0.00'} ر.س',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  'مكتمل',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.green,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          onTap: () {
                            // TODO: فتح تفاصيل البيع
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
