import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/order_provider.dart';
import '../../providers/product_provider.dart';
import '../../models/order.dart';
import '../../models/order_item.dart';
import '../../models/product.dart';

class CreateShopOrderScreen extends StatefulWidget {
  final Order? existingOrder; // للتعديل

  const CreateShopOrderScreen({
    super.key,
    this.existingOrder,
  });

  @override
  State<CreateShopOrderScreen> createState() => _CreateShopOrderScreenState();
}

class _CreateShopOrderScreenState extends State<CreateShopOrderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _orderNumberController = TextEditingController();
  final _dateController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  List<OrderItem> _orderItems = [];
  bool _isLoading = false;
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.existingOrder != null;
    _initializeForm();
  }

  void _initializeForm() {
    if (_isEditMode && widget.existingOrder != null) {
      final order = widget.existingOrder!;
      _orderNumberController.text = order.id?.toString() ?? '';
      _selectedDate = DateTime.parse(order.date ?? DateTime.now().toIso8601String());
      _dateController.text = _formatDate(_selectedDate);
      _notesController.text = order.notes ?? '';

      // TODO: تحميل عناصر الطلبية
    } else {
      _orderNumberController.text = _generateOrderNumber();
      _dateController.text = _formatDate(_selectedDate);
    }
  }

  String _generateOrderNumber() {
    final now = DateTime.now();
    return 'ORD-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  double get _totalAmount {
    return _orderItems.fold(0.0, (sum, item) => sum + (item.totalPrice ?? 0));
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(_isEditMode ? 'تعديل طلبية المحل' : 'تسجيل طلبية للمحل'),
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveOrder,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // رقم الطلبية
                      TextFormField(
                        controller: _orderNumberController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الطلبية',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.receipt_long),
                        ),
                        readOnly: true,
                      ),

                      const SizedBox(height: 16),

                      // التاريخ
                      TextFormField(
                        controller: _dateController,
                        decoration: const InputDecoration(
                          labelText: 'التاريخ',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectDate,
                      ),

                      const SizedBox(height: 24),

                      // أزرار إضافة المنتجات
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _addLowStockProducts,
                              icon: const Icon(Icons.auto_awesome),
                              label: const Text('إضافة منتجات منخفضة المخزون'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _addProductManually,
                              icon: const Icon(Icons.add),
                              label: const Text('إضافة منتج يدوياً'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // عنوان المنتجات
                      const Text(
                        'المنتجات المطلوبة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // قائمة المنتجات
                      _buildProductsList(),

                      const SizedBox(height: 24),

                      // المبلغ التقديري
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Text(
                          'المبلغ التقديري: ${_totalAmount.toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // الملاحظات
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // شريط الأزرار السفلي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveOrder,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            _isEditMode ? 'حفظ التعديلات' : 'حفظ الطلبية',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    if (_orderItems.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Column(
          children: [
            Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'لم يتم إضافة أي منتجات بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _orderItems.length,
      itemBuilder: (context, index) {
        final item = _orderItems[index];
        return _buildProductItem(item, index);
      },
    );
  }

  Widget _buildProductItem(OrderItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName ?? 'منتج غير محدد',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الكمية المطلوبة: ${item.quantity?.toStringAsFixed(0) ?? '0'}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  if (item.currentStock != null)
                    Text(
                      'المخزون الحالي: ${item.currentStock?.toStringAsFixed(0) ?? '0'}',
                      style: const TextStyle(color: Colors.red),
                    ),
                ],
              ),
            ),
            Column(
              children: [
                Text(
                  '${item.totalPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () => _editProductQuantity(index),
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      iconSize: 20,
                    ),
                    IconButton(
                      onPressed: () => _removeProduct(index),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      iconSize: 20,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _addLowStockProducts() async {
    try {
      final productProvider = context.read<ProductProvider>();
      await productProvider.fetchProducts();

      final lowStockProducts = productProvider.products.where((product) {
        final currentStock = product.quantity ?? 0;
        final minStock = product.minLevel ?? 10;
        return currentStock <= minStock;
      }).toList();

      if (lowStockProducts.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا توجد منتجات منخفضة المخزون')),
          );
        }
        return;
      }

      setState(() {
        for (final product in lowStockProducts) {
          final currentStock = product.quantity ?? 0;
          final minStock = product.minLevel ?? 10;
          final suggestedQuantity = (minStock * 2) - currentStock;

          final orderItem = OrderItem(
            productId: product.id,
            productName: product.name,
            quantity: suggestedQuantity > 0 ? suggestedQuantity.toDouble() : minStock.toDouble(),
            price: product.price ?? 0,
            currentStock: currentStock,
            minStock: minStock.toDouble(),
          );

          _orderItems.add(orderItem);
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إضافة ${lowStockProducts.length} منتج منخفض المخزون')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  Future<void> _addProductManually() async {
    // TODO: فتح شاشة اختيار المنتج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير اختيار المنتج يدوياً قريباً')),
    );
  }

  void _editProductQuantity(int index) {
    final item = _orderItems[index];
    final quantityController = TextEditingController(
      text: item.quantity?.toString() ?? '1',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الكمية المطلوبة'),
        content: TextFormField(
          controller: quantityController,
          decoration: const InputDecoration(
            labelText: 'الكمية',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final newQuantity = double.tryParse(quantityController.text);
              if (newQuantity != null && newQuantity > 0) {
                setState(() {
                  _orderItems[index] = _orderItems[index].copyWith(
                    quantity: newQuantity,
                  );
                });
              }
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _removeProduct(int index) {
    setState(() {
      _orderItems.removeAt(index);
    });
  }

  Future<void> _saveOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_orderItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة منتج واحد على الأقل')),
      );
      return;
    }

    // عرض تأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_isEditMode ? 'تأكيد التعديل' : 'تأكيد الحفظ'),
        content: Text(_isEditMode
            ? 'هل أنت متأكد من تعديل طلبية المحل هذه؟'
            : 'هل أنت متأكد من حفظ طلبية المحل هذه؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final order = Order(
        id: _isEditMode ? widget.existingOrder!.id : null,
        date: _selectedDate.toIso8601String(),
        total: _totalAmount,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        status: 'pending',
        type: 'manual',
      );

      final orderProvider = context.read<OrderProvider>();

      if (_isEditMode) {
        await orderProvider.updateOrder(order, _orderItems);
      } else {
        await orderProvider.addOrder(order, _orderItems);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditMode ? 'تم تعديل الطلبية بنجاح' : 'تم حفظ الطلبية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _dateController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
