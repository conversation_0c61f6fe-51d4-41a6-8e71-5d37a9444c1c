import 'package:flutter/material.dart';

class CreateShopOrderScreen extends StatefulWidget {
  const CreateShopOrderScreen({super.key});

  @override
  State<CreateShopOrderScreen> createState() => _CreateShopOrderScreenState();
}

class _CreateShopOrderScreenState extends State<CreateShopOrderScreen> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('تسجيل طلبية للمحل'),
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: () {
                // TODO: تنفيذ حفظ الطلبية
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('سيتم تطوير هذه الميزة قريباً')),
                );
              },
            ),
          ],
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory, size: 64, color: Colors.orange),
              SizedBox(height: 16),
              Text(
                'شاشة تسجيل طلبية للمحل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'سيتم تطوير هذه الشاشة قريباً',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
