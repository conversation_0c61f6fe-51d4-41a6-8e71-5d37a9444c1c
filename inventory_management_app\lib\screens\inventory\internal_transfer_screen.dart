import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/internal_transfer_provider.dart';
import '../../providers/product_provider.dart';
import '../../models/internal_transfer.dart';
import '../../models/product.dart';
import '../dialogs/select_product_for_transfer_dialog.dart';
import '../dialogs/confirmation_dialog.dart';

class InternalTransferScreen extends StatefulWidget {
  const InternalTransferScreen({super.key});

  @override
  State<InternalTransferScreen> createState() => _InternalTransferScreenState();
}

class _InternalTransferScreenState extends State<InternalTransferScreen> {
  final _formKey = GlobalKey<FormState>();
  final _transferDateController = TextEditingController();
  final _productNameController = TextEditingController();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  Product? _selectedProduct;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _transferDateController.text = _formatDate(_selectedDate);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  double get _totalWholesaleValue {
    if (_selectedProduct == null) return 0.0;
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    return quantity * (_selectedProduct!.price ?? 0);
  }

  double get _totalRetailValue {
    if (_selectedProduct == null) return 0.0;
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    return quantity * (_selectedProduct!.retailPrice ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('تحويل داخلي للمخزون'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveTransfer,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // تاريخ التحويل
                      TextFormField(
                        controller: _transferDateController,
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التحويل',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectDate,
                      ),

                      const SizedBox(height: 16),

                      // اختيار المنتج
                      TextFormField(
                        controller: _productNameController,
                        decoration: const InputDecoration(
                          labelText: 'المنتج',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.inventory_2),
                          suffixIcon: Icon(Icons.search),
                        ),
                        readOnly: true,
                        onTap: _selectProduct,
                        validator: (value) {
                          if (_selectedProduct == null) {
                            return 'يرجى اختيار المنتج';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // تفاصيل المنتج المحدد
                      if (_selectedProduct != null) ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تفاصيل المنتج: ${_selectedProduct!.name}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('سعر الجملة: ${_selectedProduct!.price?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                                        Text('سعر التجزئة: ${_selectedProduct!.retailPrice?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'المخزن: ${_selectedProduct!.warehouseQuantity?.toString() ?? '0'}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                        Text('البقالة: ${_selectedProduct!.storeQuantity?.toString() ?? '0'}'),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),
                      ],

                      // الكمية المراد تحويلها
                      TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'الكمية المراد تحويلها',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                          suffixText: 'قطعة',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الكمية';
                          }
                          final quantity = int.tryParse(value);
                          if (quantity == null || quantity <= 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          if (_selectedProduct != null) {
                            final warehouseQuantity = _selectedProduct!.warehouseQuantity ?? 0;
                            if (quantity > warehouseQuantity) {
                              return 'الكمية أكبر من المتوفر في المخزن ($warehouseQuantity)';
                            }
                          }
                          return null;
                        },
                        onChanged: (value) {
                          setState(() {}); // لتحديث القيم المحسوبة
                        },
                      ),

                      const SizedBox(height: 16),

                      // عرض القيم المحسوبة
                      if (_selectedProduct != null && _quantityController.text.isNotEmpty) ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'القيم المحسوبة:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Text('القيمة بسعر الجملة: ${_totalWholesaleValue.toStringAsFixed(2)} ر.س'),
                                  ),
                                  Expanded(
                                    child: Text('القيمة بسعر التجزئة: ${_totalRetailValue.toStringAsFixed(2)} ر.س'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),
                      ],

                      // الملاحظات
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // شريط الأزرار السفلي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveTransfer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'حفظ التحويل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _transferDateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _selectProduct() async {
    final Product? product = await showDialog<Product>(
      context: context,
      builder: (context) => const SelectProductForTransferDialog(),
    );

    if (product != null) {
      setState(() {
        _selectedProduct = product;
        _productNameController.text = product.name;
        // مسح الكمية عند تغيير المنتج
        _quantityController.clear();
      });
    }
  }

  Future<void> _saveTransfer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // عرض تأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: 'تأكيد التحويل',
        content: 'هل أنت متأكد من إتمام تحويل ${_quantityController.text} من ${_selectedProduct!.name} من المخزن إلى البقالة؟\n\nهذا الإجراء سيؤثر على كميات المخزون.',
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final transferProvider = context.read<InternalTransferProvider>();
      final productProvider = context.read<ProductProvider>();

      final quantity = int.parse(_quantityController.text);

      // إنشاء كائن التحويل
      final transfer = InternalTransfer(
        transferDate: _selectedDate,
        productId: _selectedProduct!.id!,
        transferredQuantity: quantity,
        wholesalePriceAtTransfer: _selectedProduct!.price ?? 0,
        retailPriceAtTransfer: _selectedProduct!.retailPrice ?? 0,
        totalWholesaleValue: _totalWholesaleValue,
        totalRetailValue: _totalRetailValue,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        productName: _selectedProduct!.name,
      );

      // إضافة التحويل
      final bool success = await transferProvider.addInternalTransfer(transfer, productProvider);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ التحويل الداخلي بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حفظ التحويل: ${transferProvider.error ?? 'خطأ غير معروف'}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _transferDateController.dispose();
    _productNameController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
