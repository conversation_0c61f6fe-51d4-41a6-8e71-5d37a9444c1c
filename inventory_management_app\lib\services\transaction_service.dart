import 'package:sqflite/sqflite.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/models/purchase_item.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/business_logic_service.dart';

/// Service for handling complex transactions that involve multiple tables
class TransactionService {
  final DatabaseService _databaseService = DatabaseService();
  final BusinessLogicService _businessLogic = BusinessLogicService();

  /// Create a complete sale with items and update stock
  Future<int> createSaleWithItems(
    Sale sale, 
    List<SaleItem> items
  ) async {
    final Database db = await _databaseService.database;
    
    return await db.transaction((Transaction txn) async {
      // Validate stock availability for all items
      for (final SaleItem item in items) {
        if (item.productId != null && item.quantity != null) {
          final bool hasStock = await _businessLogic.hasEnoughStock(
              item.productId!, item.quantity!);
          if (!hasStock) {
            throw Exception('Insufficient stock for product ${item.productId}');
          }
        }
      }

      // Insert sale
      final int saleId = await txn.insert('sales', sale.toMap());

      // Insert sale items and update stock
      for (final SaleItem item in items) {
        item.saleId = saleId;
        await txn.insert('sale_items', item.toMap());
        
        // Update product stock
        if (item.productId != null && item.quantity != null) {
          await _updateProductStock(txn, item.productId!, -item.quantity!);
        }
      }

      return saleId;
    });
  }

  /// Create a complete purchase with items and update stock
  Future<int> createPurchaseWithItems(
    Purchase purchase, 
    List<PurchaseItem> items
  ) async {
    final Database db = await _databaseService.database;
    
    return await db.transaction((Transaction txn) async {
      // Insert purchase
      final int purchaseId = await txn.insert('purchases', purchase.toMap());

      // Insert purchase items and update stock
      for (final PurchaseItem item in items) {
        item.purchaseId = purchaseId;
        await txn.insert('purchase_items', item.toMap());
        
        // Update product stock
        if (item.productId != null && item.quantity != null) {
          await _updateProductStock(txn, item.productId!, item.quantity!);
        }
      }

      return purchaseId;
    });
  }

  /// Delete sale and restore stock
  Future<void> deleteSaleAndRestoreStock(int saleId) async {
    final Database db = await _databaseService.database;
    
    await db.transaction((Transaction txn) async {
      // Get sale items to restore stock
      final List<Map<String, Object?>> saleItems = await txn.query(
        'sale_items',
        where: 'saleId = ?',
        whereArgs: <Object?>[saleId],
      );

      // Restore stock for each item
      for (final Map<String, Object?> itemMap in saleItems) {
        final SaleItem item = SaleItem.fromMap(itemMap);
        if (item.productId != null && item.quantity != null) {
          await _updateProductStock(txn, item.productId!, item.quantity!);
        }
      }

      // Delete sale items
      await txn.delete('sale_items', where: 'saleId = ?', whereArgs: <Object?>[saleId]);
      
      // Delete sale
      await txn.delete('sales', where: 'id = ?', whereArgs: <Object?>[saleId]);
    });
  }

  /// Delete purchase and adjust stock
  Future<void> deletePurchaseAndAdjustStock(int purchaseId) async {
    final Database db = await _databaseService.database;
    
    await db.transaction((Transaction txn) async {
      // Get purchase items to adjust stock
      final List<Map<String, Object?>> purchaseItems = await txn.query(
        'purchase_items',
        where: 'purchaseId = ?',
        whereArgs: <Object?>[purchaseId],
      );

      // Adjust stock for each item
      for (final Map<String, Object?> itemMap in purchaseItems) {
        final PurchaseItem item = PurchaseItem.fromMap(itemMap);
        if (item.productId != null && item.quantity != null) {
          await _updateProductStock(txn, item.productId!, -item.quantity!);
        }
      }

      // Delete purchase items
      await txn.delete('purchase_items', 
          where: 'purchaseId = ?', whereArgs: <Object?>[purchaseId]);
      
      // Delete purchase
      await txn.delete('purchases', where: 'id = ?', whereArgs: <Object?>[purchaseId]);
    });
  }

  /// Update product stock within a transaction
  Future<void> _updateProductStock(
    Transaction txn, 
    int productId, 
    double quantityChange
  ) async {
    // Get current product
    final List<Map<String, Object?>> productMaps = await txn.query(
      'products',
      where: 'id = ?',
      whereArgs: <Object?>[productId],
    );

    if (productMaps.isNotEmpty) {
      final double currentQuantity = productMaps.first['quantity'] as double? ?? 0;
      final double newQuantity = currentQuantity + quantityChange;
      
      // Ensure quantity doesn't go negative
      if (newQuantity < 0) {
        throw Exception('Stock cannot be negative for product $productId');
      }

      await txn.update(
        'products',
        <String, Object?>{'quantity': newQuantity},
        where: 'id = ?',
        whereArgs: <Object?>[productId],
      );
    }
  }

  /// Transfer stock between products (for product merging/splitting)
  Future<void> transferStock(
    int fromProductId, 
    int toProductId, 
    double quantity
  ) async {
    final Database db = await _databaseService.database;
    
    await db.transaction((Transaction txn) async {
      await _updateProductStock(txn, fromProductId, -quantity);
      await _updateProductStock(txn, toProductId, quantity);
    });
  }
}
