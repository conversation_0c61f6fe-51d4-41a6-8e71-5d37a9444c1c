import 'package:flutter/material.dart';

/// نظام الألوان المحسن للتطبيق وفقاً للخطة الجديدة
class AppColors {
  // الألوان الأساسية - خلفية بيضاء نقية
  static const Color background = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surface = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surfaceVariant = Color(0xFFF5F5F5); // رمادي فاتح جداً للبطاقات
  static const Color surfaceSecondary = Color(0xFFEEEEEE); // رمادي أفتح للطبقات
  
  // الألوان الرئيسية - أزرق سماوي عصري
  static const Color primary = Color(0xFF42A5F5); // أزرق سماوي (lightBlue.shade600)
  static const Color primaryLight = Color(0xFF80D6FF);
  static const Color primaryDark = Color(0xFF0077C2);
  
  // الألوان الثانوية - كهرماني للتحذيرات والتمييز
  static const Color accent = Color(0xFFFFA726); // كهرماني (amber.shade700)
  static const Color accentLight = Color(0xFFFFD95A);
  static const Color accentDark = Color(0xFFC77800);
  
  // الألوان المكملة
  static const Color secondary = Color(0xFF26A69A); // تيل للنجاح (teal.shade500)
  static const Color secondaryLight = Color(0xFF64D8CB);
  static const Color secondaryDark = Color(0xFF00766C);
  
  // ألوان النصوص - وضوح قصوى
  static const Color textPrimary = Color(0xFF212121); // أسود للنصوص الرئيسية
  static const Color textSecondary = Color(0xFF757575); // رمادي للنصوص الثانوية
  static const Color textTertiary = Color(0xFF9E9E9E); // رمادي فاتح للتفاصيل
  static const Color textOnPrimary = Colors.white; // أبيض على الألوان الأساسية
  static const Color textOnSurface = Color(0xFF212121); // أسود على الخلفيات البيضاء
  
  // ألوان الحالة - واضحة ومميزة
  static const Color success = Color(0xFF4CAF50); // أخضر للنجاح
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color error = Color(0xFFE53935); // أحمر للأخطاء والحذف
  static const Color errorLight = Color(0xFFFFEBEE);
  static const Color warning = Color(0xFFFF9800); // برتقالي للتحذيرات
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color info = Color(0xFF2196F3); // أزرق للمعلومات
  static const Color infoLight = Color(0xFFE3F2FD);
  
  // ألوان إضافية للتصميم
  static const Color divider = Color(0xFFE0E0E0); // فواصل
  static const Color shadow = Color(0x1A000000); // ظلال خفيفة
  static const Color overlay = Color(0x66000000); // طبقات شفافة
  static const Color disabled = Color(0xFFBDBDBD); // عناصر معطلة
  
  // ألوان خاصة للمخزون
  static const Color lowStock = Color(0xFFE53935); // أحمر للمخزون المنخفض
  static const Color lowStockLight = Color(0xFFFFEBEE);
  static const Color inStock = Color(0xFF4CAF50); // أخضر للمخزون المتاح
  static const Color inStockLight = Color(0xFFE8F5E8);
  
  // تدرجات لونية للبطاقات والعناصر التفاعلية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
