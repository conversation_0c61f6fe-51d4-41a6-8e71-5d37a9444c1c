import 'package:flutter/material.dart';

/// نظام الألوان المحسن للتطبيق وفقاً للخطة الجديدة
class AppColors {
  // الألوان الأساسية - خلفية بيضاء نقية
  static const Color background = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surface = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surfaceVariant =
      Color(0xFFF5F5F5); // رمادي فاتح جداً للبطاقات
  static const Color surfaceSecondary = Color(0xFFEEEEEE); // رمادي أفتح للطبقات

  // الألوان الرئيسية - أزرق سماوي عصري
  static const Color primary =
      Color(0xFF42A5F5); // أزرق سماوي (lightBlue.shade600)
  static const Color primaryLight = Color(0xFF80D6FF);
  static const Color primaryDark = Color(0xFF0077C2);

  // الألوان الثانوية - كهرماني للتحذيرات والتمييز
  static const Color accent = Color(0xFFFFA726); // كهرماني (amber.shade700)
  static const Color accentLight = Color(0xFFFFD95A);
  static const Color accentDark = Color(0xFFC77800);

  // الألوان المكملة
  static const Color secondary =
      Color(0xFF26A69A); // تيل للنجاح (teal.shade500)
  static const Color secondaryLight = Color(0xFF64D8CB);
  static const Color secondaryDark = Color(0xFF00766C);

  // ألوان النصوص - وضوح قصوى
  static const Color textPrimary = Color(0xFF212121); // أسود للنصوص الرئيسية
  static const Color textSecondary = Color(0xFF757575); // رمادي للنصوص الثانوية
  static const Color textTertiary = Color(0xFF9E9E9E); // رمادي فاتح للتفاصيل
  static const Color textOnPrimary = Colors.white; // أبيض على الألوان الأساسية
  static const Color textOnSurface =
      Color(0xFF212121); // أسود على الخلفيات البيضاء

  // ألوان الحالة - واضحة ومميزة
  static const Color success = Color(0xFF4CAF50); // أخضر للنجاح
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color error = Color(0xFFE53935); // أحمر للأخطاء والحذف
  static const Color errorLight = Color(0xFFFFEBEE);
  static const Color warning = Color(0xFFFF9800); // برتقالي للتحذيرات
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color info = Color(0xFF2196F3); // أزرق للمعلومات
  static const Color infoLight = Color(0xFFE3F2FD);

  // ألوان إضافية للتصميم
  static const Color divider = Color(0xFFE0E0E0); // فواصل
  static const Color dividerDark = Color(0xFF424242); // فواصل داكنة
  static const Color shadow = Color(0x1A000000); // ظلال خفيفة
  static const Color shadowDark = Color(0x3A000000); // ظلال داكنة
  static const Color overlay = Color(0x66000000); // طبقات شفافة
  static const Color disabled = Color(0xFFBDBDBD); // عناصر معطلة

  // ألوان للوضع الداكن
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color surfaceVariantDark = Color(0xFF2C2C2C);
  static const Color textLight = Colors.white;

  // ألوان خاصة للمخزون
  static const Color lowStock = Color(0xFFE53935); // أحمر للمخزون المنخفض
  static const Color lowStockLight = Color(0xFFFFEBEE);
  static const Color inStock = Color(0xFF4CAF50); // أخضر للمخزون المتاح
  static const Color inStockLight = Color(0xFFE8F5E8);

  // تدرجات لونية للبطاقات والعناصر التفاعلية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: <Color>[primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: <Color>[accent, accentDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: <Color>[success, Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ========== ألوان خاصة بأسامة ماركت ==========

  /// لون المبيعات - أخضر نعناعي
  static const Color sales = Color(0xFF00BCD4);
  static const Color salesLight = Color(0xFFB2EBF2);

  /// لون المشتريات - بنفسجي هادئ
  static const Color purchases = Color(0xFF9C27B0);
  static const Color purchasesLight = Color(0xFFE1BEE7);

  /// لون المخزون - برتقالي ذهبي
  static const Color inventory = Color(0xFFFF9800);
  static const Color inventoryLight = Color(0xFFFFF3E0);

  /// لون العملاء - أزرق سماوي
  static const Color customers = Color(0xFF03A9F4);
  static const Color customersLight = Color(0xFFE1F5FE);

  /// لون الموردين - أخضر زيتوني
  static const Color suppliers = Color(0xFF8BC34A);
  static const Color suppliersLight = Color(0xFFF1F8E9);

  /// لون التقارير - بنفسجي داكن
  static const Color reports = Color(0xFF673AB7);
  static const Color reportsLight = Color(0xFFEDE7F6);

  // ========== ألوان الظلال ==========

  /// ظل خفيف للبطاقات
  static const List<BoxShadow> cardShadow = <BoxShadow>[
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 8,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  /// ظل متوسط للعناصر المرتفعة
  static const List<BoxShadow> elevatedShadow = <BoxShadow>[
    BoxShadow(
      color: Color(0x14000000),
      blurRadius: 12,
      offset: Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  /// ظل قوي للعناصر البارزة
  static const List<BoxShadow> prominentShadow = <BoxShadow>[
    BoxShadow(
      color: Color(0x1F000000),
      blurRadius: 16,
      offset: Offset(0, 8),
      spreadRadius: 0,
    ),
  ];
}
