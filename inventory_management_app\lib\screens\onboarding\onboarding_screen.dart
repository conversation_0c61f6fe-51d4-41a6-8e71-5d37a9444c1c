import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../widgets/custom_buttons.dart';

/// شاشة الجولة التعريفية للتطبيق
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      icon: Icons.inventory_2,
      title: 'إدارة المخزون',
      description: 'تتبع جميع منتجاتك بسهولة\nومراقبة مستويات المخزون',
      color: AppColors.primary,
    ),
    OnboardingPage(
      icon: Icons.point_of_sale,
      title: 'إدارة المبيعات',
      description: 'إنشاء فواتير البيع بسرعة\nوتتبع جميع المعاملات',
      color: AppColors.accent,
    ),
    OnboardingPage(
      icon: Icons.shopping_cart,
      title: 'إدارة المشتريات',
      description: 'تسجيل المشتريات من الموردين\nوإدارة التكاليف',
      color: AppColors.secondary,
    ),
    OnboardingPage(
      icon: Icons.analytics,
      title: 'التقارير والإحصائيات',
      description: 'احصل على تقارير مفصلة\nلاتخاذ قرارات أفضل',
      color: AppColors.info,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() {
    context.go('/');
  }

  void _skipOnboarding() {
    context.go('/');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // شريط علوي مع زر التخطي
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomTextButton(
                    text: 'تخطي',
                    onPressed: _skipOnboarding,
                    textColor: AppColors.textSecondary,
                  ),
                  Text(
                    '${_currentPage + 1} من ${_pages.length}',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),

            // مؤشر الصفحات
            _buildPageIndicator(),

            // أزرار التنقل
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.color,
            ),
          ),

          const SizedBox(height: 40),

          // العنوان
          Text(
            page.title,
            style: AppStyles.headlineSmall.copyWith(
              color: page.color,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // الوصف
          Text(
            page.description,
            style: AppStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: _currentPage == index ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentPage == index
                  ? AppColors.primary
                  : AppColors.textTertiary,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // زر السابق
          if (_currentPage > 0)
            Expanded(
              child: CustomSecondaryButton(
                text: 'السابق',
                onPressed: _previousPage,
                icon: Icons.arrow_back,
                isFullWidth: true,
              ),
            ),

          if (_currentPage > 0) const SizedBox(width: 16),

          // زر التالي/البدء
          Expanded(
            flex: _currentPage == 0 ? 1 : 1,
            child: CustomPrimaryButton(
              text: _currentPage == _pages.length - 1 ? 'ابدأ الآن' : 'التالي',
              onPressed: _nextPage,
              icon: _currentPage == _pages.length - 1
                  ? Icons.rocket_launch
                  : Icons.arrow_forward,
              isFullWidth: true,
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج صفحة الجولة التعريفية
class OnboardingPage {
  final IconData icon;
  final String title;
  final String description;
  final Color color;

  OnboardingPage({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });
}
