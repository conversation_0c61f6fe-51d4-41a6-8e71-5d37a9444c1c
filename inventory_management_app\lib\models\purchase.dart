class Purchase {
  int? id;
  int supplierId;
  DateTime? purchaseDate;
  double? totalAmount;
  String? notes;

  // Getters for compatibility
  DateTime? get date => purchaseDate;
  double? get total => totalAmount;

  Purchase({
    this.id,
    required this.supplierId,
    this.purchaseDate,
    this.totalAmount,
    this.notes,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'supplierId': supplierId,
      'purchaseDate': purchaseDate?.toIso8601String(),
      'totalAmount': totalAmount,
      'notes': notes,
    };
  }

  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map['id'],
      supplierId: map['supplierId'],
      purchaseDate: map['purchaseDate'] != null
          ? DateTime.parse(map['purchaseDate'])
          : null,
      totalAmount: map['totalAmount'],
      notes: map['notes'],
    );
  }
}
