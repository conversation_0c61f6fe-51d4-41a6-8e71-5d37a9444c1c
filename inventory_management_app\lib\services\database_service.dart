import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:inventory_management_app/data/database_helper.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static DatabaseService get instance => _instance;
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDatabase();
    return _database!;
  }

  Future<Database> _initializeDatabase() async {
    final String databasePath = await getDatabasesPath();
    final String path = join(databasePath, 'inventory_management.db');

    return openDatabase(
      path,
      version: 6, // إصلاح مشكلة الفهارس المكررة
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      print('🔄 Creating database tables (version $version)...');

      // إنشاء الجداول الأساسية
      await db.execute(createProductsTable);
      await db.execute(createCustomersTable);
      await db.execute(createSuppliersTable);
      await db.execute(createCategoriesTable);
      await db.execute(createUnitsTable);
      print('✅ Created basic tables');

      // إنشاء جداول المعاملات
      await db.execute(createOrdersTable);
      await db.execute(createOrderItemsTable);
      await db.execute(createSalesTable);
      await db.execute(createSaleItemsTable);
      await db.execute(createPurchasesTable);
      await db.execute(createPurchaseItemsTable);
      print('✅ Created transaction tables');

      // إنشاء الجداول المساعدة
      await db.execute(createExpensesTable);
      await db.execute(createBackupsTable);
      await db.execute(createActivitiesTable);
      await db.execute(createTransactionsTable);
      await db.execute(createDailySummaryTable);
      await db.execute(createCustomerStatementTable);
      await db.execute(createSupplierStatementTable);
      print('✅ Created auxiliary tables');

      // إنشاء الجداول المتقدمة
      await db.execute(createInternalTransfersTable);
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created advanced tables');

      // إنشاء الفهارس
      await _createIndexes(db);

      print('🎉 Database created successfully with version $version');

    } catch (e) {
      print('❌ Error creating database: $e');
      rethrow;
    }
  }

  /// Handle database upgrades safely without losing data
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('🔄 Upgrading database from version $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // Upgrade from version 1 to 2: Fix expenses table structure
      await _upgradeToVersion2(db);
    }

    if (oldVersion < 3) {
      // Upgrade from version 2 to 3: Add warehouse and store system support
      await _upgradeToVersion3(db);
    }

    if (oldVersion < 4) {
      // Upgrade from version 3 to 4: Add internal transfers support
      await _upgradeToVersion4(db);
    }

    if (oldVersion < 5) {
      // Upgrade from version 4 to 5: Add store inventory adjustments support
      await _upgradeToVersion5(db);
    }

    if (oldVersion < 6) {
      // Upgrade from version 5 to 6: Fix duplicate indexes issue
      await _upgradeToVersion6(db);
    }

    // Future upgrades can be added here
    // if (oldVersion < 7) {
    //   await _upgradeToVersion7(db);
    // }
  }

  /// Upgrade to version 2: Fix expenses table structure
  Future<void> _upgradeToVersion2(Database db) async {
    try {
      print('📊 Upgrading expenses table structure...');

      // Step 1: Check if old expenses table exists and has data
      final List<Map<String, dynamic>> existingExpenses = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
      );

      List<Map<String, dynamic>> oldExpensesData = [];
      if (existingExpenses.isNotEmpty) {
        // Backup existing data
        try {
          oldExpensesData = await db.query('expenses');
          print('💾 Backed up ${oldExpensesData.length} existing expenses');
        } catch (e) {
          print('⚠️ No existing expenses data to backup: $e');
        }
      }

      // Step 2: Drop old expenses table if exists
      await db.execute('DROP TABLE IF EXISTS expenses');
      print('🗑️ Dropped old expenses table');

      // Step 3: Create new expenses table with correct structure
      await db.execute(createExpensesTable);
      print('✅ Created new expenses table');

      // Step 4: Migrate old data if any exists
      if (oldExpensesData.isNotEmpty) {
        await _migrateExpensesData(db, oldExpensesData);
      }

      print('🎉 Successfully upgraded expenses table to version 2');

    } catch (e) {
      print('❌ Error upgrading to version 2: $e');
      // If upgrade fails, recreate the table with new structure
      await db.execute('DROP TABLE IF EXISTS expenses');
      await db.execute(createExpensesTable);
      print('🔧 Recreated expenses table after upgrade error');
    }
  }

  /// Upgrade to version 3: Add warehouse and store system support
  Future<void> _upgradeToVersion3(Database db) async {
    try {
      print('🏪 Upgrading to warehouse and store system (version 3)...');

      // Add new columns to products table
      await db.execute('ALTER TABLE products ADD COLUMN retailPrice REAL DEFAULT 0');
      await db.execute('ALTER TABLE products ADD COLUMN warehouseQuantity INTEGER DEFAULT 0');
      await db.execute('ALTER TABLE products ADD COLUMN storeQuantity INTEGER DEFAULT 0');
      print('✅ Added new columns to products table');

      // Add new columns to sales table
      await db.execute('ALTER TABLE sales ADD COLUMN totalWholesaleAmount REAL DEFAULT 0');
      await db.execute('ALTER TABLE sales ADD COLUMN totalRetailAmount REAL DEFAULT 0');
      await db.execute('ALTER TABLE sales ADD COLUMN remainingRetailAmount REAL DEFAULT 0');
      await db.execute('ALTER TABLE sales ADD COLUMN notesForRetailItems TEXT');
      print('✅ Added new columns to sales table');

      // Add new column to sale_items table
      await db.execute('ALTER TABLE sale_items ADD COLUMN itemType TEXT DEFAULT \'wholesale\'');
      print('✅ Added itemType column to sale_items table');

      // Migrate existing quantity data to warehouse
      await db.execute('''
        UPDATE products
        SET warehouseQuantity = COALESCE(quantity, 0),
            storeQuantity = 0
        WHERE warehouseQuantity IS NULL OR warehouseQuantity = 0
      ''');
      print('📦 Migrated existing quantities to warehouse');

      // Set default retail prices (same as wholesale for now)
      await db.execute('''
        UPDATE products
        SET retailPrice = COALESCE(price, 0)
        WHERE retailPrice IS NULL OR retailPrice = 0
      ''');
      print('💰 Set default retail prices');

      print('🎉 Successfully upgraded to version 3 (warehouse and store system)');

    } catch (e) {
      print('❌ Error upgrading to version 3: $e');
      rethrow;
    }
  }

  /// Upgrade to version 4: Add internal transfers support
  Future<void> _upgradeToVersion4(Database db) async {
    try {
      print('🔄 Upgrading to internal transfers system (version 4)...');

      // Create internal_transfers table
      await db.execute(createInternalTransfersTable);
      print('✅ Created internal_transfers table');

      // Note: Indexes will be created by _createIndexes() if needed

      print('🎉 Successfully upgraded to version 4 (internal transfers system)');

    } catch (e) {
      print('❌ Error upgrading to version 4: $e');
      rethrow;
    }
  }

  /// Upgrade to version 5: Add store inventory adjustments support
  Future<void> _upgradeToVersion5(Database db) async {
    try {
      print('🔄 Upgrading to store inventory adjustments system (version 5)...');

      // Create store_inventory_adjustments table
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created store_inventory_adjustments table');

      // Note: Indexes will be created by _createIndexes() if needed

      print('🎉 Successfully upgraded to version 5 (store inventory adjustments system)');

    } catch (e) {
      print('❌ Error upgrading to version 5: $e');
      rethrow;
    }
  }

  /// Upgrade to version 6: Fix duplicate indexes issue
  Future<void> _upgradeToVersion6(Database db) async {
    try {
      print('🔧 Upgrading to fix indexes issue (version 6)...');

      // إعادة إنشاء جميع الفهارس بشكل صحيح
      await _createIndexes(db);

      print('🎉 Successfully upgraded to version 6 (fixed indexes issue)');

    } catch (e) {
      print('❌ Error upgrading to version 6: $e');
      // لا نرمي الخطأ هنا لأن الفهارس ليست حرجة للتشغيل
      print('⚠️ Continuing without some indexes...');
    }
  }

  /// Migrate old expenses data to new table structure
  Future<void> _migrateExpensesData(Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldExpense in oldData) {
        // Convert old structure to new structure
        final Map<String, dynamic> newExpense = {
          'expenseDate': oldExpense['date'], // date -> expenseDate
          'category': _convertCategoryIdToString(oldExpense['categoryId']), // categoryId -> category
          'amount': oldExpense['amount'],
          'description': oldExpense['notes'] ?? '', // notes -> description (fallback)
          'notes': oldExpense['notes'],
          'status': 'active', // default status
        };

        await db.insert('expenses', newExpense);
      }
      print('📦 Migrated ${oldData.length} expenses to new structure');
    } catch (e) {
      print('⚠️ Error migrating expenses data: $e');
    }
  }

  /// Convert old categoryId to new category string
  String _convertCategoryIdToString(dynamic categoryId) {
    if (categoryId == null) return 'miscellaneous';

    // Map old category IDs to new category strings
    switch (categoryId) {
      case 1: return 'rent';
      case 2: return 'salaries';
      case 3: return 'supplies';
      case 4: return 'utilities';
      case 5: return 'maintenance';
      case 6: return 'marketing';
      case 7: return 'transportation';
      default: return 'miscellaneous';
    }
  }

  /// Create performance indexes for frequently queried columns
  Future<void> _createIndexes(Database db) async {
    // استخدام الفهارس الموحدة من database_helper.dart
    for (final String index in createIndexes) {
      try {
        await db.execute(index);
      } catch (e) {
        print('⚠️ Warning creating index: $e');
        // Continue with other indexes even if one fails
      }
    }
    print('✅ All database indexes created successfully');
  }

  /// Delete database (for testing purposes only)
  /// ⚠️ WARNING: This will delete all data permanently!
  Future<void> deleteDatabaseForTesting() async {
    try {
      final String databasePath = await getDatabasesPath();
      final String path = join(databasePath, 'inventory_management.db');

      // Close current database connection
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Delete the database file
      await deleteDatabase(path);
      print('🗑️ Database deleted successfully for testing');

    } catch (e) {
      print('❌ Error deleting database: $e');
    }
  }

  /// Reset database connection (force recreation)
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Get database file path
  Future<String> getDatabasePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  // ==================== Internal Transfers CRUD Operations ====================

  /// Insert a new internal transfer record
  Future<int> insertInternalTransfer(Map<String, dynamic> transferMap) async {
    try {
      final Database db = await database;
      final int id = await db.insert('internal_transfers', transferMap);
      print('✅ Internal transfer inserted with ID: $id');
      return id;
    } catch (e) {
      print('❌ Error inserting internal transfer: $e');
      rethrow;
    }
  }

  /// Get all internal transfers with product information
  Future<List<Map<String, dynamic>>> getInternalTransfers() async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        ORDER BY it.transferDate DESC
      ''');
      print('📦 Retrieved ${transfers.length} internal transfers');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get internal transfers by date range
  Future<List<Map<String, dynamic>>> getInternalTransfersByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final Database db = await database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        WHERE it.transferDate BETWEEN ? AND ?
        ORDER BY it.transferDate DESC
      ''', [startTimestamp, endTimestamp]);

      print('📦 Retrieved ${transfers.length} internal transfers for date range');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers by date range: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get internal transfers by product ID
  Future<List<Map<String, dynamic>>> getInternalTransfersByProduct(int productId) async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> transfers = await db.rawQuery('''
        SELECT
          it.*,
          p.name as productName
        FROM internal_transfers it
        LEFT JOIN products p ON it.productId = p.id
        WHERE it.productId = ?
        ORDER BY it.transferDate DESC
      ''', [productId]);

      print('📦 Retrieved ${transfers.length} internal transfers for product $productId');
      return transfers;
    } catch (e) {
      print('❌ Error getting internal transfers by product: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Update an existing internal transfer
  Future<int> updateInternalTransfer(Map<String, dynamic> transferMap) async {
    try {
      final Database db = await database;
      final int id = transferMap['id'] as int;
      final int rowsAffected = await db.update(
        'internal_transfers',
        transferMap,
        where: 'id = ?',
        whereArgs: [id],
      );
      print('✅ Internal transfer updated: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error updating internal transfer: $e');
      rethrow;
    }
  }

  /// Delete an internal transfer
  Future<int> deleteInternalTransfer(int id) async {
    try {
      final Database db = await database;
      final int rowsAffected = await db.delete(
        'internal_transfers',
        where: 'id = ?',
        whereArgs: [id],
      );
      print('✅ Internal transfer deleted: $rowsAffected rows affected');
      return rowsAffected;
    } catch (e) {
      print('❌ Error deleting internal transfer: $e');
      rethrow;
    }
  }

  /// Get total transferred quantity for a product
  Future<int> getTotalTransferredQuantity(int productId) async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COALESCE(SUM(transferredQuantity), 0) as totalTransferred
        FROM internal_transfers
        WHERE productId = ?
      ''', [productId]);

      final int totalTransferred = result.first['totalTransferred'] as int;
      print('📊 Total transferred quantity for product $productId: $totalTransferred');
      return totalTransferred;
    } catch (e) {
      print('❌ Error getting total transferred quantity: $e');
      return 0;
    }
  }

  /// Get internal transfers count
  Future<int> getInternalTransfersCount() async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COUNT(*) as count FROM internal_transfers
      ''');

      final int count = result.first['count'] as int;
      print('📊 Total internal transfers count: $count');
      return count;
    } catch (e) {
      print('❌ Error getting internal transfers count: $e');
      return 0;
    }
  }

  // ==================== Store Inventory Adjustments CRUD Operations ====================

  /// Insert a new store inventory adjustment record
  Future<int> insertStoreInventoryAdjustment(Map<String, dynamic> adjustmentMap) async {
    try {
      final Database db = await database;
      final int id = await db.insert('store_inventory_adjustments', adjustmentMap);
      print('✅ Store inventory adjustment inserted with ID: $id');
      return id;
    } catch (e) {
      print('❌ Error inserting store inventory adjustment: $e');
      rethrow;
    }
  }

  /// Get all store inventory adjustments with product information
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustments() async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> adjustments = await db.rawQuery('''
        SELECT
          sia.*,
          p.name as productName
        FROM store_inventory_adjustments sia
        LEFT JOIN products p ON sia.productId = p.id
        ORDER BY sia.adjustmentDate DESC
      ''');
      print('📦 Retrieved ${adjustments.length} store inventory adjustments');
      return adjustments;
    } catch (e) {
      print('❌ Error getting store inventory adjustments: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get store inventory adjustments by date range
  Future<List<Map<String, dynamic>>> getStoreInventoryAdjustmentsByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final Database db = await database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> adjustments = await db.rawQuery('''
        SELECT
          sia.*,
          p.name as productName
        FROM store_inventory_adjustments sia
        LEFT JOIN products p ON sia.productId = p.id
        WHERE sia.adjustmentDate BETWEEN ? AND ?
        ORDER BY sia.adjustmentDate DESC
      ''', [startTimestamp, endTimestamp]);

      print('📦 Retrieved ${adjustments.length} store inventory adjustments for date range');
      return adjustments;
    } catch (e) {
      print('❌ Error getting store inventory adjustments by date range: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get total adjustment value for a date range
  Future<double> getTotalAdjustmentValue(DateTime startDate, DateTime endDate) async {
    try {
      final Database db = await database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COALESCE(SUM(adjustmentValue), 0) as totalAdjustmentValue
        FROM store_inventory_adjustments
        WHERE adjustmentDate BETWEEN ? AND ?
      ''', [startTimestamp, endTimestamp]);

      final double totalAdjustmentValue = result.first['totalAdjustmentValue']?.toDouble() ?? 0.0;
      print('📊 Total adjustment value for date range: $totalAdjustmentValue');
      return totalAdjustmentValue;
    } catch (e) {
      print('❌ Error getting total adjustment value: $e');
      return 0.0;
    }
  }

  /// Get negative adjustment value (losses) for a date range
  Future<double> getNegativeAdjustmentValue(DateTime startDate, DateTime endDate) async {
    try {
      final Database db = await database;
      final int startTimestamp = startDate.millisecondsSinceEpoch;
      final int endTimestamp = endDate.millisecondsSinceEpoch;

      final List<Map<String, dynamic>> result = await db.rawQuery('''
        SELECT COALESCE(SUM(adjustmentValue), 0) as negativeAdjustmentValue
        FROM store_inventory_adjustments
        WHERE adjustmentDate BETWEEN ? AND ? AND adjustmentValue < 0
      ''', [startTimestamp, endTimestamp]);

      final double negativeAdjustmentValue = result.first['negativeAdjustmentValue']?.toDouble() ?? 0.0;
      print('📊 Negative adjustment value for date range: $negativeAdjustmentValue');
      return negativeAdjustmentValue;
    } catch (e) {
      print('❌ Error getting negative adjustment value: $e');
      return 0.0;
    }
  }

  // ==================== Backup and Restore Operations ====================

  /// Get the full path to the database file
  Future<String> getDatabaseFilePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  /// Copy database file to a destination path
  Future<void> copyDatabaseFile(String destinationPath) async {
    try {
      // Get current database file path
      final String sourcePath = await getDatabaseFilePath();

      // Ensure database is closed before copying
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Copy the file
      final File sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        await sourceFile.copy(destinationPath);
        print('✅ Database copied successfully to: $destinationPath');
      } else {
        throw Exception('Database file does not exist at: $sourcePath');
      }

      // Reopen database connection
      await database;

    } catch (e) {
      print('❌ Error copying database file: $e');
      rethrow;
    }
  }

  /// Restore database from a backup file
  Future<void> restoreDatabaseFromFile(String sourcePath) async {
    try {
      // Verify source file exists
      final File sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw Exception('Backup file does not exist at: $sourcePath');
      }

      // Get destination path
      final String destinationPath = await getDatabaseFilePath();

      // Close current database connection
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Copy backup file to database location
      await sourceFile.copy(destinationPath);
      print('✅ Database restored successfully from: $sourcePath');

      // Reopen database connection
      await database;

    } catch (e) {
      print('❌ Error restoring database from file: $e');
      rethrow;
    }
  }

  /// Create a backup file with timestamp
  Future<String> createTimestampedBackup(String backupDirectory) async {
    try {
      // Create backup directory if it doesn't exist
      final Directory backupDir = Directory(backupDirectory);
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Generate timestamped filename
      final DateTime now = DateTime.now();
      final String timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
      final String backupFileName = 'inventory_backup_$timestamp.db';
      final String backupPath = join(backupDirectory, backupFileName);

      // Copy database to backup location
      await copyDatabaseFile(backupPath);

      print('✅ Timestamped backup created: $backupPath');
      return backupPath;

    } catch (e) {
      print('❌ Error creating timestamped backup: $e');
      rethrow;
    }
  }

  /// Get database file size in bytes
  Future<int> getDatabaseFileSize() async {
    try {
      final String databasePath = await getDatabaseFilePath();
      final File databaseFile = File(databasePath);

      if (await databaseFile.exists()) {
        final int size = await databaseFile.length();
        print('📊 Database file size: $size bytes');
        return size;
      } else {
        print('⚠️ Database file does not exist');
        return 0;
      }
    } catch (e) {
      print('❌ Error getting database file size: $e');
      return 0;
    }
  }

  /// Verify database integrity
  Future<bool> verifyDatabaseIntegrity() async {
    try {
      final Database db = await database;
      final List<Map<String, dynamic>> result = await db.rawQuery('PRAGMA integrity_check');

      final bool isIntact = result.isNotEmpty &&
                           result.first.containsKey('integrity_check') &&
                           result.first['integrity_check'] == 'ok';

      print(isIntact ? '✅ Database integrity check passed' : '❌ Database integrity check failed');
      return isIntact;

    } catch (e) {
      print('❌ Error verifying database integrity: $e');
      return false;
    }
  }

  /// Get database statistics for backup info
  Future<Map<String, dynamic>> getDatabaseStatistics() async {
    try {
      final Database db = await database;

      // Get table counts
      final Map<String, int> tableCounts = {};
      final List<String> tables = [
        'products', 'customers', 'suppliers', 'categories', 'units',
        'orders', 'order_items', 'sales', 'sale_items', 'purchases', 'purchase_items',
        'expenses', 'activities', 'transactions', 'daily_summary',
        'customer_statement', 'supplier_statement', 'internal_transfers',
        'store_inventory_adjustments'
      ];

      for (final String table in tables) {
        try {
          final List<Map<String, dynamic>> result = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
          tableCounts[table] = result.first['count'] as int;
        } catch (e) {
          tableCounts[table] = 0;
        }
      }

      // Get database file size
      final int fileSize = await getDatabaseFileSize();

      // Get database version
      final List<Map<String, dynamic>> versionResult = await db.rawQuery('PRAGMA user_version');
      final int version = versionResult.first['user_version'] as int;

      final Map<String, dynamic> statistics = {
        'version': version,
        'fileSize': fileSize,
        'tableCounts': tableCounts,
        'totalRecords': tableCounts.values.fold<int>(0, (sum, count) => sum + count),
        'lastBackup': DateTime.now().toIso8601String(),
      };

      print('📊 Database statistics collected');
      return statistics;

    } catch (e) {
      print('❌ Error getting database statistics: $e');
      return <String, dynamic>{};
    }
  }
}
