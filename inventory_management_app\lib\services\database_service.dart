import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:inventory_management_app/data/database_helper.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static DatabaseService get instance => _instance;
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDatabase();
    return _database!;
  }

  Future<Database> _initializeDatabase() async {
    final String databasePath = await getDatabasesPath();
    final String path = join(databasePath, 'inventory_management.db');

    return openDatabase(
      path,
      version: 2, // زيادة الإصدار لتفعيل onUpgrade
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute(createProductsTable);
    await db.execute(createCustomersTable);
    await db.execute(createSuppliersTable);
    await db.execute(createCategoriesTable);
    await db.execute(createUnitsTable);
    await db.execute(createOrdersTable);
    await db.execute(createOrderItemsTable);
    await db.execute(createSalesTable);
    await db.execute(createSaleItemsTable);
    await db.execute(createPurchasesTable);
    await db.execute(createPurchaseItemsTable);
    await db.execute(createExpensesTable);
    await db.execute(createBackupsTable);
    await db.execute(createActivitiesTable);
    await db.execute(createTransactionsTable);
    await db.execute(createDailySummaryTable);
    await db.execute(createCustomerStatementTable);
    await db.execute(createSupplierStatementTable);

    // Create performance indexes
    await _createIndexes(db);
  }

  /// Handle database upgrades safely without losing data
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('🔄 Upgrading database from version $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // Upgrade from version 1 to 2: Fix expenses table structure
      await _upgradeToVersion2(db);
    }

    // Future upgrades can be added here
    // if (oldVersion < 3) {
    //   await _upgradeToVersion3(db);
    // }
  }

  /// Upgrade to version 2: Fix expenses table structure
  Future<void> _upgradeToVersion2(Database db) async {
    try {
      print('📊 Upgrading expenses table structure...');

      // Step 1: Check if old expenses table exists and has data
      final List<Map<String, dynamic>> existingExpenses = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
      );

      List<Map<String, dynamic>> oldExpensesData = [];
      if (existingExpenses.isNotEmpty) {
        // Backup existing data
        try {
          oldExpensesData = await db.query('expenses');
          print('💾 Backed up ${oldExpensesData.length} existing expenses');
        } catch (e) {
          print('⚠️ No existing expenses data to backup: $e');
        }
      }

      // Step 2: Drop old expenses table if exists
      await db.execute('DROP TABLE IF EXISTS expenses');
      print('🗑️ Dropped old expenses table');

      // Step 3: Create new expenses table with correct structure
      await db.execute(createExpensesTable);
      print('✅ Created new expenses table');

      // Step 4: Migrate old data if any exists
      if (oldExpensesData.isNotEmpty) {
        await _migrateExpensesData(db, oldExpensesData);
      }

      print('🎉 Successfully upgraded expenses table to version 2');

    } catch (e) {
      print('❌ Error upgrading to version 2: $e');
      // If upgrade fails, recreate the table with new structure
      await db.execute('DROP TABLE IF EXISTS expenses');
      await db.execute(createExpensesTable);
      print('🔧 Recreated expenses table after upgrade error');
    }
  }

  /// Migrate old expenses data to new table structure
  Future<void> _migrateExpensesData(Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldExpense in oldData) {
        // Convert old structure to new structure
        final Map<String, dynamic> newExpense = {
          'expenseDate': oldExpense['date'], // date -> expenseDate
          'category': _convertCategoryIdToString(oldExpense['categoryId']), // categoryId -> category
          'amount': oldExpense['amount'],
          'description': oldExpense['notes'] ?? '', // notes -> description (fallback)
          'notes': oldExpense['notes'],
          'status': 'active', // default status
        };

        await db.insert('expenses', newExpense);
      }
      print('📦 Migrated ${oldData.length} expenses to new structure');
    } catch (e) {
      print('⚠️ Error migrating expenses data: $e');
    }
  }

  /// Convert old categoryId to new category string
  String _convertCategoryIdToString(dynamic categoryId) {
    if (categoryId == null) return 'miscellaneous';

    // Map old category IDs to new category strings
    switch (categoryId) {
      case 1: return 'rent';
      case 2: return 'salaries';
      case 3: return 'supplies';
      case 4: return 'utilities';
      case 5: return 'maintenance';
      case 6: return 'marketing';
      case 7: return 'transportation';
      default: return 'miscellaneous';
    }
  }

  /// Create performance indexes for frequently queried columns
  Future<void> _createIndexes(Database db) async {
    const List<String> indexes = <String>[
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
      'CREATE INDEX IF NOT EXISTS idx_products_category '
          'ON products(categoryId)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email '
          'ON customers(email)',
      'CREATE INDEX IF NOT EXISTS idx_suppliers_email '
          'ON suppliers(email)',
      'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date)',
      'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(date)',
      'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customerId)',
      'CREATE INDEX IF NOT EXISTS idx_purchases_supplier '
          'ON purchases(supplierId)',
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expenseDate)',
      'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)',
    ];

    for (final String index in indexes) {
      await db.execute(index);
    }
  }

  /// Delete database (for testing purposes only)
  /// ⚠️ WARNING: This will delete all data permanently!
  Future<void> deleteDatabaseForTesting() async {
    try {
      final String databasePath = await getDatabasesPath();
      final String path = join(databasePath, 'inventory_management.db');

      // Close current database connection
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Delete the database file
      await deleteDatabase(path);
      print('🗑️ Database deleted successfully for testing');

    } catch (e) {
      print('❌ Error deleting database: $e');
    }
  }

  /// Reset database connection (force recreation)
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Get database file path
  Future<String> getDatabasePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }
}
