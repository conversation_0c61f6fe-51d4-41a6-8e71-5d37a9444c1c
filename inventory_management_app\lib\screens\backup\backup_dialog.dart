import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/backup_provider.dart';

class BackupDialog extends StatelessWidget {
  const BackupDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Consumer<BackupProvider>(
        builder: (context, backupProvider, child) {
          return AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.backup, color: Colors.blue),
                SizedBox(width: 8),
                Text('النسخ الاحتياطي'),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (backupProvider.isLoading)
                    const Padding(
                      padding: EdgeInsets.all(20),
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('جاري المعالجة...'),
                        ],
                      ),
                    )
                  else ...[
                    // Success/Error Messages
                    if (backupProvider.successMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.check_circle, color: Colors.green, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'تم بنجاح',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              backupProvider.successMessage!,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    
                    if (backupProvider.error != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.error, color: Colors.red, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'حدث خطأ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              backupProvider.error!,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),

                    // Backup Options
                    _buildBackupOption(
                      context: context,
                      icon: Icons.cloud_upload,
                      title: 'إنشاء نسخة احتياطية',
                      subtitle: 'حفظ نسخة من قاعدة البيانات',
                      color: Colors.green,
                      onTap: () => _createBackup(context, backupProvider),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildBackupOption(
                      context: context,
                      icon: Icons.cloud_download,
                      title: 'استعادة نسخة احتياطية',
                      subtitle: 'استعادة قاعدة البيانات من ملف',
                      color: Colors.orange,
                      onTap: () => _restoreBackup(context, backupProvider),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildBackupOption(
                      context: context,
                      icon: Icons.folder,
                      title: 'إدارة النسخ الاحتياطية',
                      subtitle: 'عرض وحذف النسخ المحفوظة',
                      color: Colors.blue,
                      onTap: () => _manageBackups(context, backupProvider),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  backupProvider.clearMessages();
                  Navigator.pop(context);
                },
                child: const Text('إغلاق'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBackupOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createBackup(BuildContext context, BackupProvider provider) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إنشاء النسخة الاحتياطية'),
        content: const Text('هل أنت متأكد من إنشاء نسخة احتياطية من قاعدة البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await provider.createLocalBackup();
    }
  }

  Future<void> _restoreBackup(BuildContext context, BackupProvider provider) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير: استعادة النسخة الاحتياطية'),
        content: const Text(
          'تحذير: استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية وتستبدلها ببيانات النسخة الاحتياطية.\n\nهل أنت متأكد من المتابعة؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await provider.restoreLocalBackup();
    }
  }

  Future<void> _manageBackups(BuildContext context, BackupProvider provider) async {
    showDialog(
      context: context,
      builder: (context) => const ManageBackupsDialog(),
    );
  }
}

class ManageBackupsDialog extends StatefulWidget {
  const ManageBackupsDialog({super.key});

  @override
  State<ManageBackupsDialog> createState() => _ManageBackupsDialogState();
}

class _ManageBackupsDialogState extends State<ManageBackupsDialog> {
  List<FileSystemEntity> _backups = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    final provider = context.read<BackupProvider>();
    final backups = await provider.getLocalBackups();
    setState(() {
      _backups = backups;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: const Text('إدارة النسخ الاحتياطية'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _backups.isEmpty
                  ? const Center(
                      child: Text('لا توجد نسخ احتياطية محفوظة'),
                    )
                  : ListView.builder(
                      itemCount: _backups.length,
                      itemBuilder: (context, index) {
                        final backup = _backups[index];
                        final provider = context.read<BackupProvider>();
                        final info = provider.getBackupInfo(backup);
                        
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: const Icon(Icons.backup, color: Colors.blue),
                            title: Text(
                              info['name'],
                              style: const TextStyle(fontSize: 14),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('الحجم: ${info['sizeFormatted']}'),
                                Text('التاريخ: ${info['lastModified'].day}/${info['lastModified'].month}/${info['lastModified'].year}'),
                              ],
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _deleteBackup(info['path']),
                            ),
                          ),
                        );
                      },
                    ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBackup(String path) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final provider = context.read<BackupProvider>();
      final success = await provider.deleteBackup(path);
      
      if (success) {
        _loadBackups(); // Reload the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف النسخة الاحتياطية بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }
  }
}
