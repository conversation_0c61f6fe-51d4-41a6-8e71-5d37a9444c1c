import 'package:flutter/material.dart';
import '../models/activity.dart';
import '../models/sale.dart';
import '../models/purchase.dart';
import '../models/expense.dart';
import '../models/order.dart';
import '../services/sale_service.dart';
import '../services/purchase_service.dart';
import '../services/expense_service.dart';
import '../services/order_service.dart';

class ActivityProvider extends ChangeNotifier {
  final SaleService _saleService = SaleService();
  final PurchaseService _purchaseService = PurchaseService();
  final ExpenseService _expenseService = ExpenseService();
  final OrderService _orderService = OrderService();

  List<Activity> _activities = [];
  List<Activity> _filteredActivities = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  String _selectedFilter = 'الكل';
  DateTime? _startDate;
  DateTime? _endDate;

  // Getters
  List<Activity> get activities => _filteredActivities;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  String get selectedFilter => _selectedFilter;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;

  // Filter options
  final List<String> filterOptions = [
    'الكل',
    'مبيعات',
    'مشتريات', 
    'مصروفات',
    'طلبيات المحل'
  ];

  /// Load all activities from different sources
  Future<void> loadActivities() async {
    _setLoading(true);
    _clearError();

    try {
      _activities.clear();

      // Load sales activities
      final sales = await _saleService.getAllSales();
      for (final sale in sales) {
        _activities.add(Activity(
          id: sale.id,
          type: 'مبيعات',
          description: 'فاتورة بيع رقم ${sale.id} - العميل: ${sale.customerId ?? 'غير محدد'}',
          date: sale.date ?? DateTime.now().toIso8601String(),
        ));
      }

      // Load purchases activities
      final purchases = await _purchaseService.getAllPurchases();
      for (final purchase in purchases) {
        _activities.add(Activity(
          id: purchase.id,
          type: 'مشتريات',
          description: 'فاتورة توريد رقم ${purchase.id} - المورد: ${purchase.supplierId ?? 'غير محدد'}',
          date: purchase.date ?? DateTime.now().toIso8601String(),
        ));
      }

      // Load expenses activities
      final expenses = await _expenseService.getAllExpenses();
      for (final expense in expenses) {
        _activities.add(Activity(
          id: expense.id,
          type: 'مصروفات',
          description: 'مصروف: ${expense.description ?? 'غير محدد'} - المبلغ: ${expense.amount?.toStringAsFixed(2) ?? '0'} ر.س',
          date: expense.expenseDate ?? DateTime.now().toIso8601String(),
        ));
      }

      // Load orders activities
      final orders = await _orderService.getAllOrders();
      for (final order in orders) {
        _activities.add(Activity(
          id: order.id,
          type: 'طلبيات المحل',
          description: 'طلبية رقم ${order.id} - الحالة: ${_getStatusInArabic(order.status)} - النوع: ${_getTypeInArabic(order.type)}',
          date: order.date ?? DateTime.now().toIso8601String(),
        ));
      }

      // Sort activities by date (newest first)
      _activities.sort((a, b) {
        final dateA = DateTime.tryParse(a.date ?? '') ?? DateTime.now();
        final dateB = DateTime.tryParse(b.date ?? '') ?? DateTime.now();
        return dateB.compareTo(dateA);
      });

      _applyFilters();
      
    } catch (e) {
      _setError('فشل في تحميل الأنشطة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Apply search and filter
  void _applyFilters() {
    _filteredActivities = _activities.where((activity) {
      // Apply type filter
      bool matchesFilter = _selectedFilter == 'الكل' || activity.type == _selectedFilter;
      
      // Apply search query
      bool matchesSearch = _searchQuery.isEmpty ||
          (activity.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
          (activity.type?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      
      // Apply date range filter
      bool matchesDateRange = true;
      if (_startDate != null && _endDate != null) {
        final activityDate = DateTime.tryParse(activity.date ?? '');
        if (activityDate != null) {
          matchesDateRange = activityDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
                           activityDate.isBefore(_endDate!.add(const Duration(days: 1)));
        }
      }
      
      return matchesFilter && matchesSearch && matchesDateRange;
    }).toList();
    
    notifyListeners();
  }

  /// Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  /// Set filter
  void setFilter(String filter) {
    _selectedFilter = filter;
    _applyFilters();
  }

  /// Set date range
  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    _applyFilters();
  }

  /// Clear date range
  void clearDateRange() {
    _startDate = null;
    _endDate = null;
    _applyFilters();
  }

  /// Clear all filters
  void clearAllFilters() {
    _searchQuery = '';
    _selectedFilter = 'الكل';
    _startDate = null;
    _endDate = null;
    _applyFilters();
  }

  /// Get activity count by type
  int getActivityCountByType(String type) {
    if (type == 'الكل') return _activities.length;
    return _activities.where((activity) => activity.type == type).length;
  }

  /// Get activity icon
  IconData getActivityIcon(String? type) {
    switch (type) {
      case 'مبيعات':
        return Icons.shopping_cart;
      case 'مشتريات':
        return Icons.local_shipping;
      case 'مصروفات':
        return Icons.money_off;
      case 'طلبيات المحل':
        return Icons.inventory;
      default:
        return Icons.activity_zone;
    }
  }

  /// Get activity color
  Color getActivityColor(String? type) {
    switch (type) {
      case 'مبيعات':
        return Colors.blue;
      case 'مشتريات':
        return Colors.green;
      case 'مصروفات':
        return Colors.red;
      case 'طلبيات المحل':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// Get formatted date
  String getFormattedDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  String _getStatusInArabic(String? status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'converted':
        return 'محولة';
      default:
        return 'غير محدد';
    }
  }

  String _getTypeInArabic(String? type) {
    switch (type) {
      case 'manual':
        return 'يدوية';
      case 'auto_low_stock':
        return 'تلقائية - مخزون منخفض';
      case 'customer_order':
        return 'طلب عميل';
      default:
        return 'غير محدد';
    }
  }

  /// Initialize provider
  Future<void> initialize() async {
    await loadActivities();
  }
}
