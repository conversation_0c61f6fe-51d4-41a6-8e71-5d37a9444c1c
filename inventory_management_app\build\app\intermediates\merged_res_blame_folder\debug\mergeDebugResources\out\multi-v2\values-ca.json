{"logs": [{"outputFile": "com.example.inventory_management_app-mergeDebugResources-49:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2825,2921,3023,3122,3219,3325,3430,6706", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "2916,3018,3117,3214,3320,3425,3551,6802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a9e90969c21e54abbd28d118be0111db\\transformed\\browser-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5873,6082,6185,6296", "endColumns": "112,102,110,108", "endOffsets": "5981,6180,6291,6400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\77b6482155e5a178adf635640ae2a82c\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3661,3813,3940,4049,4199,4326,4449,4692,4863,4972,5131,5262,5426,5584,5649,5717", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "3656,3808,3935,4044,4194,4321,4444,4552,4858,4967,5126,5257,5421,5579,5644,5712,5799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84cfce9c05a54a984dea12df260c2609\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4557", "endColumns": "134", "endOffsets": "4687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744ef0616acd167077bd442bba141275\\transformed\\preference-1.2.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5804,5986,6405,6482,6807,6976,7063", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "5868,6077,6477,6620,6971,7058,7139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4c7ccc4495020ec151e51318a1fa1ad\\transformed\\appcompat-1.1.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,518,624,750,834,914,1005,1097,1190,1285,1384,1477,1570,1664,1755,1846,1926,2037,2145,2243,2353,2458,2566,2726,2825", "endColumns": "117,104,106,82,105,125,83,79,90,91,92,94,98,92,92,93,90,90,79,110,107,97,109,104,107,159,98,80", "endOffsets": "218,323,430,513,619,745,829,909,1000,1092,1185,1280,1379,1472,1565,1659,1750,1841,1921,2032,2140,2238,2348,2453,2561,2721,2820,2901"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,518,624,750,834,914,1005,1097,1190,1285,1384,1477,1570,1664,1755,1846,1926,2037,2145,2243,2353,2458,2566,2726,6625", "endColumns": "117,104,106,82,105,125,83,79,90,91,92,94,98,92,92,93,90,90,79,110,107,97,109,104,107,159,98,80", "endOffsets": "218,323,430,513,619,745,829,909,1000,1092,1185,1280,1379,1472,1565,1659,1750,1841,1921,2032,2140,2238,2348,2453,2561,2721,2820,6701"}}]}]}