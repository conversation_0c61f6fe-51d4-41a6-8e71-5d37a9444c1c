import 'package:flutter/material.dart';
import 'package:contacts_service/contacts_service.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  runApp(const TestContactsApp());
}

class TestContactsApp extends StatelessWidget {
  const TestContactsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Contacts Import',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestContactsScreen(),
    );
  }
}

class TestContactsScreen extends StatefulWidget {
  const TestContactsScreen({super.key});

  @override
  State<TestContactsScreen> createState() => _TestContactsScreenState();
}

class _TestContactsScreenState extends State<TestContactsScreen> {
  List<Contact> _contacts = <dynamic>[];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Contacts Import'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            ElevatedButton(
              onPressed: _loadContacts,
              child: const Text('Load Contacts'),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const CircularProgressIndicator()
            else if (_errorMessage != null)
              Text(
                'Error: $_errorMessage',
                style: const TextStyle(color: Colors.red),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _contacts.length,
                  itemBuilder: (BuildContext context, int index) {
                    final contact = _contacts[index];
                    return ListTile(
                      title: Text(contact.displayName ?? 'No Name'),
                      subtitle: Text(
                        contact.phones?.isNotEmpty == true
                            ? contact.phones!.first.value ?? 'No Phone'
                            : 'No Phone',
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadContacts() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Request permission
      final PermissionStatus permission = await Permission.contacts.request();
      
      if (permission == PermissionStatus.granted) {
        // Load contacts
        final contacts = await ContactsService.getContacts();
        
        setState(() {
          _contacts = contacts.toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Permission denied';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }
}
