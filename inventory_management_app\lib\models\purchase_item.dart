/// Model class representing an item in a purchase transaction
class PurchaseItem {
  /// Unique identifier for the purchase item
  int? id;
  
  /// Purchase ID that this item belongs to
  int? purchaseId;
  
  /// Product ID for this purchase item
  int? productId;
  
  /// Quantity of the product purchased
  double? quantity;
  
  /// Price per unit for this purchase item
  double? price;

  /// Constructor for creating a PurchaseItem instance
  PurchaseItem({
    this.id,
    this.purchaseId,
    this.productId,
    this.quantity,
    this.price,
  });

  /// Converts the PurchaseItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'purchaseId': purchaseId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
    };
  }

  /// Creates a PurchaseItem instance from a Map (typically from database)
  factory PurchaseItem.fromMap(Map<String, dynamic> map) {
    return PurchaseItem(
      id: map['id'] as int?,
      purchaseId: map['purchaseId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'PurchaseItem{id: $id, purchaseId: $purchaseId, productId: $productId, '
        'quantity: $quantity, price: $price}';
  }
}
