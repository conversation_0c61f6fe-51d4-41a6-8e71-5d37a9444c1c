import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_constants.dart';
import '../utils/formatters.dart';
import '../utils/date_helper.dart';

/// فئة مساعدة للتصدير والطباعة
class ExportHelper {
  /// تصدير البيانات إلى CSV
  static Future<String> exportToCSV({
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    required String fileName,
  }) async {
    if (data.isEmpty) {
      throw Exception('لا توجد بيانات للتصدير');
    }

    final StringBuffer csvContent = StringBuffer();
    
    // إضافة العناوين
    csvContent.writeln(headers.join(','));
    
    // إضافة البيانات
    for (final Map<String, dynamic> row in data) {
      final List<String> values = headers.map((String header) {
        final String value = row[header]?.toString() ?? '';
        // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
        if (value.contains(',') || value.contains('"') || value.contains('\n')) {
          return '"${value.replaceAll('"', '""')}"';
        }
        return value;
      }).toList();
      
      csvContent.writeln(values.join(','));
    }

    // حفظ الملف
    final File file = await _saveFile(fileName, csvContent.toString(), 'csv');
    return file.path;
  }

  /// تصدير البيانات إلى JSON
  static Future<String> exportToJSON({
    required List<Map<String, dynamic>> data,
    required String fileName,
  }) async {
    if (data.isEmpty) {
      throw Exception('لا توجد بيانات للتصدير');
    }

    final String jsonContent = json.encode(<String, Object>{
      'exportDate': DateTime.now().toIso8601String(),
      'appName': AppConstants.appName,
      'version': AppConstants.appVersion,
      'data': data,
    });

    final File file = await _saveFile(fileName, jsonContent, 'json');
    return file.path;
  }

  /// تصدير تقرير المنتجات
  static Future<String> exportProductsReport(List<Map<String, dynamic>> products) async {
    final List<String> headers = <String>[
      'اسم المنتج',
      'الكمية',
      'سعر الشراء',
      'سعر البيع',
      'الحد الأدنى',
      'الفئة',
      'الوحدة',
      'تاريخ الإضافة',
    ];

    final List<Map<String, dynamic>> processedData = products.map((Map<String, dynamic> product) => <String, dynamic>{
      'اسم المنتج': product['name'] ?? '',
      'الكمية': product['quantity']?.toString() ?? '0',
      'سعر الشراء': Formatters.formatCurrency(product['purchasePrice']?.toDouble() ?? 0),
      'سعر البيع': Formatters.formatCurrency(product['salePrice']?.toDouble() ?? 0),
      'الحد الأدنى': product['minLevel']?.toString() ?? '0',
      'الفئة': product['category'] ?? '',
      'الوحدة': product['unit'] ?? '',
      'تاريخ الإضافة': product['createdAt'] != null 
          ? Formatters.formatDate(DateTime.parse(product['createdAt']))
          : '',
    }).toList();

    final String fileName = 'تقرير_المنتجات_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd')}';
    return await exportToCSV(
      data: processedData,
      headers: headers,
      fileName: fileName,
    );
  }

  /// تصدير تقرير المبيعات
  static Future<String> exportSalesReport(List<Map<String, dynamic>> sales) async {
    final List<String> headers = <String>[
      'رقم الفاتورة',
      'العميل',
      'التاريخ',
      'المبلغ الإجمالي',
      'طريقة الدفع',
      'الحالة',
      'الملاحظات',
    ];

    final List<Map<String, dynamic>> processedData = sales.map((Map<String, dynamic> sale) => <String, dynamic>{
      'رقم الفاتورة': sale['invoiceNumber'] ?? '',
      'العميل': sale['customerName'] ?? 'عميل نقدي',
      'التاريخ': sale['date'] != null 
          ? Formatters.formatDate(DateTime.parse(sale['date']))
          : '',
      'المبلغ الإجمالي': Formatters.formatCurrency(sale['total']?.toDouble() ?? 0),
      'طريقة الدفع': Formatters.formatPaymentMethod(sale['paymentMethod'] ?? ''),
      'الحالة': Formatters.formatInvoiceStatus(sale['status'] ?? ''),
      'الملاحظات': sale['notes'] ?? '',
    }).toList();

    final String fileName = 'تقرير_المبيعات_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd')}';
    return await exportToCSV(
      data: processedData,
      headers: headers,
      fileName: fileName,
    );
  }

  /// تصدير تقرير المشتريات
  static Future<String> exportPurchasesReport(List<Map<String, dynamic>> purchases) async {
    final List<String> headers = <String>[
      'رقم الفاتورة',
      'المورد',
      'التاريخ',
      'المبلغ الإجمالي',
      'طريقة الدفع',
      'الحالة',
      'الملاحظات',
    ];

    final List<Map<String, dynamic>> processedData = purchases.map((Map<String, dynamic> purchase) => <String, dynamic>{
      'رقم الفاتورة': purchase['invoiceNumber'] ?? '',
      'المورد': purchase['supplierName'] ?? '',
      'التاريخ': purchase['date'] != null 
          ? Formatters.formatDate(DateTime.parse(purchase['date']))
          : '',
      'المبلغ الإجمالي': Formatters.formatCurrency(purchase['total']?.toDouble() ?? 0),
      'طريقة الدفع': Formatters.formatPaymentMethod(purchase['paymentMethod'] ?? ''),
      'الحالة': Formatters.formatInvoiceStatus(purchase['status'] ?? ''),
      'الملاحظات': purchase['notes'] ?? '',
    }).toList();

    final String fileName = 'تقرير_المشتريات_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd')}';
    return await exportToCSV(
      data: processedData,
      headers: headers,
      fileName: fileName,
    );
  }

  /// تصدير تقرير المصروفات
  static Future<String> exportExpensesReport(List<Map<String, dynamic>> expenses) async {
    final List<String> headers = <String>[
      'الوصف',
      'المبلغ',
      'الفئة',
      'التاريخ',
      'طريقة الدفع',
      'الملاحظات',
    ];

    final List<Map<String, dynamic>> processedData = expenses.map((Map<String, dynamic> expense) => <String, dynamic>{
      'الوصف': expense['description'] ?? '',
      'المبلغ': Formatters.formatCurrency(expense['amount']?.toDouble() ?? 0),
      'الفئة': expense['category'] ?? '',
      'التاريخ': expense['date'] != null 
          ? Formatters.formatDate(DateTime.parse(expense['date']))
          : '',
      'طريقة الدفع': Formatters.formatPaymentMethod(expense['paymentMethod'] ?? ''),
      'الملاحظات': expense['notes'] ?? '',
    }).toList();

    final String fileName = 'تقرير_المصروفات_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd')}';
    return await exportToCSV(
      data: processedData,
      headers: headers,
      fileName: fileName,
    );
  }

  /// تصدير كشف حساب عميل
  static Future<String> exportCustomerStatement({
    required String customerName,
    required List<Map<String, dynamic>> transactions,
    required double balance,
  }) async {
    final List<String> headers = <String>[
      'التاريخ',
      'نوع المعاملة',
      'رقم الفاتورة',
      'المبلغ',
      'الرصيد',
      'الملاحظات',
    ];

    final List<Map<String, dynamic>> processedData = transactions.map((Map<String, dynamic> transaction) => <String, dynamic>{
      'التاريخ': transaction['date'] != null 
          ? Formatters.formatDate(DateTime.parse(transaction['date']))
          : '',
      'نوع المعاملة': Formatters.formatTransactionType(transaction['type'] ?? ''),
      'رقم الفاتورة': transaction['invoiceNumber'] ?? '',
      'المبلغ': Formatters.formatCurrency(transaction['amount']?.toDouble() ?? 0),
      'الرصيد': Formatters.formatCurrency(transaction['balance']?.toDouble() ?? 0),
      'الملاحظات': transaction['notes'] ?? '',
    }).toList();

    final String fileName = 'كشف_حساب_${customerName}_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd')}';
    return await exportToCSV(
      data: processedData,
      headers: headers,
      fileName: fileName,
    );
  }

  /// إنشاء نسخة احتياطية من البيانات
  static Future<String> createBackup(Map<String, List<Map<String, dynamic>>> allData) async {
    final Map<String, Object> backupData = <String, Object>{
      'backupDate': DateTime.now().toIso8601String(),
      'appName': AppConstants.appName,
      'version': AppConstants.appVersion,
      'data': allData,
    };

    final String fileName = 'نسخة_احتياطية_${DateHelper.formatDate(DateTime.now(), format: 'yyyyMMdd_HHmmss')}';
    return await exportToJSON(
      data: <Map<String, dynamic>>[backupData],
      fileName: fileName,
    );
  }

  /// استعادة البيانات من النسخة الاحتياطية
  static Future<Map<String, List<Map<String, dynamic>>>> restoreBackup(String filePath) async {
    try {
      final File file = File(filePath);
      final String content = await file.readAsString();
      final jsonData = json.decode(content);
      
      if (jsonData is List && jsonData.isNotEmpty) {
        final backupData = jsonData.first;
        if (backupData['data'] != null) {
          final Map<String, dynamic> data = backupData['data'] as Map<String, dynamic>;
          return data.map((String key, value) => MapEntry(
            key,
            (value as List).cast<Map<String, dynamic>>(),
          ));
        }
      }
      
      throw Exception('تنسيق ملف النسخة الاحتياطية غير صحيح');
    } catch (e) {
      throw Exception('فشل في قراءة ملف النسخة الاحتياطية: $e');
    }
  }

  /// حفظ الملف في مجلد التطبيق
  static Future<File> _saveFile(String fileName, String content, String extension) async {
    try {
      Directory directory;
      
      if (kIsWeb) {
        // في الويب، نستخدم التحميل المباشر
        throw UnimplementedError('التصدير في الويب يتطلب تنفيذ خاص');
      } else {
        // في التطبيقات المحلية
        directory = await getApplicationDocumentsDirectory();
      }

      final File file = File('${directory.path}/$fileName.$extension');
      await file.writeAsString(content, encoding: utf8);
      
      return file;
    } catch (e) {
      throw Exception('فشل في حفظ الملف: $e');
    }
  }

  /// الحصول على مسار مجلد التصدير
  static Future<String> getExportDirectory() async {
    if (kIsWeb) {
      return 'Downloads'; // مجلد التحميلات في الويب
    } else {
      final Directory directory = await getApplicationDocumentsDirectory();
      final Directory exportDir = Directory('${directory.path}/exports');
      
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }
      
      return exportDir.path;
    }
  }

  /// حذف الملفات القديمة
  static Future<void> cleanOldExports({int daysToKeep = 30}) async {
    if (kIsWeb) return; // لا يمكن حذف الملفات في الويب
    
    try {
      final String exportDir = await getExportDirectory();
      final Directory directory = Directory(exportDir);
      
      if (await directory.exists()) {
        final List<FileSystemEntity> files = await directory.list().toList();
        final DateTime cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
        
        for (final FileSystemEntity file in files) {
          if (file is File) {
            final FileStat stat = await file.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await file.delete();
            }
          }
        }
      }
    } catch (e) {
      // تجاهل الأخطاء في التنظيف
    }
  }

  /// الحصول على قائمة الملفات المصدرة
  static Future<List<FileInfo>> getExportedFiles() async {
    if (kIsWeb) return <FileInfo>[]; // لا يمكن قراءة الملفات في الويب
    
    try {
      final String exportDir = await getExportDirectory();
      final Directory directory = Directory(exportDir);
      
      if (!await directory.exists()) {
        return <FileInfo>[];
      }
      
      final List<FileSystemEntity> files = await directory.list().toList();
      final List<FileInfo> fileInfos = <FileInfo>[];
      
      for (final FileSystemEntity file in files) {
        if (file is File) {
          final FileStat stat = await file.stat();
          fileInfos.add(FileInfo(
            name: file.path.split('/').last,
            path: file.path,
            size: stat.size,
            modifiedDate: stat.modified,
          ));
        }
      }
      
      // ترتيب حسب تاريخ التعديل (الأحدث أولاً)
      fileInfos.sort((FileInfo a, FileInfo b) => b.modifiedDate.compareTo(a.modifiedDate));
      
      return fileInfos;
    } catch (e) {
      return <FileInfo>[];
    }
  }
}

/// معلومات الملف
class FileInfo {
  final String name;
  final String path;
  final int size;
  final DateTime modifiedDate;

  FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.modifiedDate,
  });

  String get formattedSize => Formatters.formatFileSize(size);
  String get formattedDate => Formatters.formatDateTime(modifiedDate);
}
