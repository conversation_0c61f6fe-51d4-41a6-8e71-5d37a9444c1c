import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/activity.dart';
import 'package:provider/provider.dart';
import '../../providers/activity_provider.dart';

class ActivityListScreen extends StatefulWidget {
  const ActivityListScreen({super.key});

  @override
  State<ActivityListScreen> createState() => _ActivityListScreenState();
}

class _ActivityListScreenState extends State<ActivityListScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityProvider>().loadActivities();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('قائمة الأنشطة'),
          backgroundColor: Colors.purple,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                context.read<ActivityProvider>().loadActivities();
              },
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(),
            ),
          ],
        ),
        body: Consumer<ActivityProvider>(
          builder: (BuildContext context, ActivityProvider activityProvider, Widget? child) {
            return Column(
              children: <Widget>[
                // Search and Filter Section
                _buildSearchAndFilterSection(activityProvider),

                // Content Section
                Expanded(
                  child: _buildContent(activityProvider),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchAndFilterSection(ActivityProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: <Widget>[
          // Search Field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الأنشطة...',
              prefixIcon: const Icon(Icons.search, color: Colors.purple),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        provider.setSearchQuery('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.purple),
              ),
            ),
            onChanged: (String value) {
              provider.setSearchQuery(value);
            },
          ),

          const SizedBox(height: 12),

          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: provider.filterOptions.map((String filter) {
                final bool isSelected = provider.selectedFilter == filter;
                final int count = provider.getActivityCountByType(filter);

                return Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: FilterChip(
                    label: Text('$filter ($count)'),
                    selected: isSelected,
                    onSelected: (bool selected) {
                      provider.setFilter(filter);
                    },
                    selectedColor: Colors.purple.withOpacity(0.2),
                    checkmarkColor: Colors.purple,
                  ),
                );
              }).toList(),
            ),
          ),

          // Date Range Display
          if (provider.startDate != null && provider.endDate != null)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const Icon(Icons.date_range, size: 16, color: Colors.purple),
                  const SizedBox(width: 4),
                  Text(
                    'من ${provider.startDate!.day}/${provider.startDate!.month} إلى ${provider.endDate!.day}/${provider.endDate!.month}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () => provider.clearDateRange(),
                    child: const Icon(Icons.close, size: 16, color: Colors.purple),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent(ActivityProvider provider) {
    if (provider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.purple),
      );
    }

    if (provider.error != null) {
      return _buildErrorWidget(provider.error!);
    }

    if (provider.activities.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () => provider.loadActivities(),
      color: Colors.purple,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: provider.activities.length,
        itemBuilder: (BuildContext context, int index) {
          final Activity activity = provider.activities[index];
          return _buildActivityCard(activity, provider);
        },
      ),
    );
  }

  Widget _buildActivityCard(activity, ActivityProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: provider.getActivityColor(activity.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                provider.getActivityIcon(activity.type),
                color: provider.getActivityColor(activity.type),
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: provider.getActivityColor(activity.type),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          activity.type ?? 'غير محدد',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        provider.getFormattedDate(activity.date),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  Text(
                    activity.description ?? 'لا يوجد وصف',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.list_alt_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد أنشطة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'لم يتم العثور على أي أنشطة مطابقة للفلاتر المحددة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ActivityProvider>().loadActivities();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Future<void> _showFilterDialog() async {
    final ActivityProvider provider = context.read<ActivityProvider>();

    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تصفية الأنشطة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            // Date Range Picker
            ListTile(
              leading: const Icon(Icons.date_range, color: Colors.purple),
              title: const Text('تحديد فترة زمنية'),
              subtitle: provider.startDate != null && provider.endDate != null
                  ? Text('من ${provider.startDate!.day}/${provider.startDate!.month} إلى ${provider.endDate!.day}/${provider.endDate!.month}')
                  : const Text('جميع التواريخ'),
              onTap: () async {
                final DateTimeRange? picked = await showDateRangePicker(
                  context: context,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                  initialDateRange: provider.startDate != null && provider.endDate != null
                      ? DateTimeRange(start: provider.startDate!, end: provider.endDate!)
                      : null,
                );

                if (picked != null) {
                  provider.setDateRange(picked.start, picked.end);
                }
              },
            ),

            // Clear Date Range
            if (provider.startDate != null && provider.endDate != null)
              ListTile(
                leading: const Icon(Icons.clear, color: Colors.red),
                title: const Text('مسح الفترة الزمنية'),
                onTap: () {
                  provider.clearDateRange();
                },
              ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAllFilters();
              Navigator.pop(context);
            },
            child: const Text('مسح جميع الفلاتر'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
