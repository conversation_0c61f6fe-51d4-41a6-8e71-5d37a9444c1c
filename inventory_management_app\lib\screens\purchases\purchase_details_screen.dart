import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:intl/intl.dart';

class PurchaseDetailsScreen extends StatefulWidget {
  final Purchase? purchase;

  PurchaseDetailsScreen({this.purchase});

  @override
  _PurchaseDetailsScreenState createState() => _PurchaseDetailsScreenState();
}

class _PurchaseDetailsScreenState extends State<PurchaseDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late int _supplierId;
  late DateTime? _purchaseDate;
  late double? _totalAmount;
  late String? _notes;

  @override
  void initState() {
    super.initState();
    _supplierId = widget.purchase?.supplierId ?? 0;
    _purchaseDate = widget.purchase?.purchaseDate;
    _totalAmount = widget.purchase?.totalAmount;
    _notes = widget.purchase?.notes ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.purchase == null ? 'Add Purchase' : 'Edit Purchase'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                initialValue: _supplierId.toString(),
                decoration: InputDecoration(labelText: 'Supplier ID'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a Supplier ID';
                  }
                  return null;
                },
                onSaved: (value) => _supplierId = int.parse(value!),
              ),
              TextFormField(
                initialValue: _purchaseDate != null
                    ? DateFormat('yyyy-MM-dd').format(_purchaseDate!)
                    : null,
                decoration: InputDecoration(labelText: 'Purchase Date'),
                onTap: () async {
                  DateTime? pickedDate = await showDatePicker(
                      context: context,
                      initialDate:
                          widget.purchase?.purchaseDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2101));
                  if (pickedDate != null) {
                    setState(() {
                      _purchaseDate = pickedDate;
                    });
                  }
                },
                readOnly: true,
                onSaved: (value) => _purchaseDate =
                    value != null ? DateTime.parse(value) : null,
              ),
              TextFormField(
                initialValue: _totalAmount?.toString(),
                decoration: InputDecoration(labelText: 'Total Amount'),
                keyboardType: TextInputType.number,
                onSaved: (value) => _totalAmount = double.tryParse(value!),
              ),
              TextFormField(
                initialValue: _notes,
                decoration: InputDecoration(labelText: 'Notes'),
                onSaved: (value) => _notes = value,
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final purchase = Purchase(
                      id: widget.purchase?.id,
                      supplierId: _supplierId,
                      purchaseDate: _purchaseDate,
                      totalAmount: _totalAmount,
                      notes: _notes,
                    );
                    final purchaseProvider =
                        Provider.of<PurchaseProvider>(context, listen: false);
                    if (widget.purchase == null) {
                      purchaseProvider.addPurchase(purchase);
                    } else {
                      purchaseProvider.updatePurchase(purchase);
                    }
                    Navigator.pop(context);
                  }
                },
                child: Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
