import 'package:flutter/material.dart';
import '../models/supplier.dart';
import '../services/supplier_service.dart';

/// Provider for managing supplier operations
class SupplierProvider extends ChangeNotifier {
  final SupplierService _supplierService = SupplierService();

  List<Supplier> _suppliers = [];
  List<Supplier> _filteredSuppliers = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  String _balanceFilter = 'الكل';
  String _categoryFilter = 'الكل';

  /// Getters
  List<Supplier> get suppliers => _filteredSuppliers.isEmpty && _searchQuery.isEmpty && _balanceFilter == 'الكل' && _categoryFilter == 'الكل'
      ? _suppliers
      : _filteredSuppliers;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  String get balanceFilter => _balanceFilter;
  String get categoryFilter => _categoryFilter;

  /// Initialize the provider
  Future<void> initialize() async {
    await loadSuppliers();
  }

  /// Load all suppliers from database
  Future<void> loadSuppliers() async {
    _setLoading(true);
    _clearError();

    try {
      _suppliers = await _supplierService.getAllSuppliers();
      _applyFilters();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load suppliers: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new supplier
  Future<bool> addSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      // Set creation date if not provided
      if (supplier.createdDate == null) {
        supplier = supplier.copyWith(
          createdDate: DateTime.now().toIso8601String(),
          status: 'active',
        );
      }

      await _supplierService.insertSupplier(supplier);
      await loadSuppliers();
      return true;
    } catch (e) {
      _setError('Failed to add supplier: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing supplier
  Future<bool> updateSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      await _supplierService.updateSupplier(supplier);
      await loadSuppliers();
      return true;
    } catch (e) {
      _setError('Failed to update supplier: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a supplier
  Future<bool> deleteSupplier(int supplierId) async {
    _setLoading(true);
    _clearError();

    try {
      await _supplierService.deleteSupplier(supplierId);
      await loadSuppliers();
      return true;
    } catch (e) {
      _setError('Failed to delete supplier: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Search suppliers by name or phone
  void searchSuppliers(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// Filter suppliers by balance status
  void filterByBalance(String balanceFilter) {
    _balanceFilter = balanceFilter;
    _applyFilters();
    notifyListeners();
  }

  /// Filter suppliers by category
  void filterByCategory(String categoryFilter) {
    _categoryFilter = categoryFilter;
    _applyFilters();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Apply all filters
  void _applyFilters() {
    _filteredSuppliers = _suppliers.where((supplier) {
      // Search filter
      bool matchesSearch = true;
      if (_searchQuery.isNotEmpty) {
        matchesSearch = (supplier.name?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                       (supplier.phone?.contains(_searchQuery) ?? false);
      }

      // Balance filter
      bool matchesBalance = true;
      if (_balanceFilter != 'الكل') {
        switch (_balanceFilter) {
          case 'عليهم مبالغ (مدينون لنا)':
            matchesBalance = (supplier.balance ?? 0) < 0;
            break;
          case 'لهم مبالغ (دائنون لنا)':
            matchesBalance = (supplier.balance ?? 0) > 0;
            break;
          case 'رصيد صفر':
            matchesBalance = (supplier.balance ?? 0) == 0;
            break;
        }
      }

      // Category filter
      bool matchesCategory = true;
      if (_categoryFilter != 'الكل') {
        matchesCategory = supplier.category == _categoryFilter;
      }

      return matchesSearch && matchesBalance && matchesCategory;
    }).toList();
  }

  /// Clear all filters
  void clearFilters() {
    _searchQuery = '';
    _balanceFilter = 'الكل';
    _categoryFilter = 'الكل';
    _applyFilters();
    notifyListeners();
  }

  /// Get supplier by ID
  Supplier? getSupplierById(int id) {
    try {
      return _suppliers.firstWhere((supplier) => supplier.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get total suppliers count
  int get totalSuppliersCount => _suppliers.length;

  /// Get active suppliers count
  int get activeSuppliersCount {
    return _suppliers.where((supplier) => supplier.isActive).length;
  }

  /// Get total amount we owe to suppliers
  double get totalAmountOwed {
    return _suppliers.fold(0.0, (sum, supplier) {
      final balance = supplier.balance ?? 0;
      return sum + (balance > 0 ? balance : 0);
    });
  }

  /// Get total amount suppliers owe us
  double get totalAmountDue {
    return _suppliers.fold(0.0, (sum, supplier) {
      final balance = supplier.balance ?? 0;
      return sum + (balance < 0 ? balance.abs() : 0);
    });
  }

  /// Get available categories
  List<String> get availableCategories {
    final categories = _suppliers
        .where((supplier) => supplier.category != null && supplier.category!.isNotEmpty)
        .map((supplier) => supplier.category!)
        .toSet()
        .toList();
    categories.sort();
    return ['الكل', ...categories];
  }

  /// Clear all suppliers
  void clearSuppliers() {
    _suppliers.clear();
    _filteredSuppliers.clear();
    notifyListeners();
  }
}
