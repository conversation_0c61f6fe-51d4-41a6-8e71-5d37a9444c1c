import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/supplier.dart';
import 'package:inventory_management_app/services/supplier_service.dart';

/// Provider class for managing supplier state and operations
class SupplierProvider extends ChangeNotifier {
  List<Supplier> _suppliers = <Supplier>[];
  final SupplierService _supplierService = SupplierService();
  bool _isLoading = false;
  String? _error;

  /// Get the list of suppliers
  List<Supplier> get suppliers => _suppliers;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all suppliers from the database
  Future<void> fetchSuppliers() async {
    _setLoading(true);
    _clearError();

    try {
      _suppliers = await _supplierService.getAllSuppliers();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch suppliers: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new supplier
  Future<void> addSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      await _supplierService.insertSupplier(supplier);
      await fetchSuppliers();
    } catch (e) {
      _setError('Failed to add supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing supplier
  Future<void> updateSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      await _supplierService.updateSupplier(supplier);
      await fetchSuppliers();
    } catch (e) {
      _setError('Failed to update supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a supplier
  Future<void> deleteSupplier(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _supplierService.deleteSupplier(id);
      await fetchSuppliers();
    } catch (e) {
      _setError('Failed to delete supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search suppliers by name
  Future<void> searchSuppliers(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _suppliers = await _supplierService.searchSuppliersByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search suppliers: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
