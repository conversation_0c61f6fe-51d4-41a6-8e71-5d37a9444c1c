import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/enhanced_theme_provider.dart';
import '../services/notification_service.dart';
import '../services/backup_service.dart';
import 'advanced_analytics_screen.dart';

class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen> {
  bool _autoBackup = true;
  bool _dailyReports = false;
  bool _lowStockAlerts = true;
  bool _salesNotifications = true;
  String _backupFrequency = 'يومي';
  String _currency = 'ر.س';
  String _language = 'العربية';

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('الإعدادات المتقدمة'),
          backgroundColor: Colors.teal,
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              icon: const Icon(Icons.analytics),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdvancedAnalyticsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // المظهر والثيم
            _buildSectionCard(
              'المظهر والثيم',
              Icons.palette,
              Colors.purple,
              [
                Consumer<EnhancedThemeProvider>(
                  builder: (context, themeProvider, child) {
                    return SwitchListTile(
                      title: const Text('الوضع المظلم'),
                      subtitle: const Text('تفعيل الوضع المظلم للتطبيق'),
                      value: themeProvider.isDarkMode,
                      onChanged: (value) {
                        themeProvider.toggleTheme();
                      },
                      secondary: const Icon(Icons.dark_mode),
                    );
                  },
                ),
                ListTile(
                  title: const Text('اللغة'),
                  subtitle: Text(_language),
                  leading: const Icon(Icons.language),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showLanguageDialog(),
                ),
                ListTile(
                  title: const Text('العملة'),
                  subtitle: Text(_currency),
                  leading: const Icon(Icons.attach_money),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showCurrencyDialog(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الإشعارات
            _buildSectionCard(
              'الإشعارات',
              Icons.notifications,
              Colors.orange,
              [
                Consumer<NotificationProvider>(
                  builder: (context, notificationProvider, child) {
                    return SwitchListTile(
                      title: const Text('تفعيل الإشعارات'),
                      subtitle: const Text('تلقي إشعارات التطبيق'),
                      value: notificationProvider.notificationsEnabled,
                      onChanged: (value) {
                        notificationProvider.toggleNotifications(value);
                      },
                      secondary: const Icon(Icons.notifications_active),
                    );
                  },
                ),
                SwitchListTile(
                  title: const Text('تنبيهات المخزون المنخفض'),
                  subtitle: const Text('إشعار عند انخفاض المخزون'),
                  value: _lowStockAlerts,
                  onChanged: (value) {
                    setState(() {
                      _lowStockAlerts = value;
                    });
                  },
                  secondary: const Icon(Icons.warning),
                ),
                SwitchListTile(
                  title: const Text('إشعارات المبيعات'),
                  subtitle: const Text('إشعار عند إتمام عملية بيع'),
                  value: _salesNotifications,
                  onChanged: (value) {
                    setState(() {
                      _salesNotifications = value;
                    });
                  },
                  secondary: const Icon(Icons.receipt),
                ),
                SwitchListTile(
                  title: const Text('التقارير اليومية'),
                  subtitle: const Text('تلقي ملخص يومي للمبيعات'),
                  value: _dailyReports,
                  onChanged: (value) {
                    setState(() {
                      _dailyReports = value;
                    });
                  },
                  secondary: const Icon(Icons.today),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // النسخ الاحتياطي
            _buildSectionCard(
              'النسخ الاحتياطي',
              Icons.backup,
              Colors.green,
              [
                SwitchListTile(
                  title: const Text('النسخ الاحتياطي التلقائي'),
                  subtitle: const Text('إنشاء نسخة احتياطية تلقائياً'),
                  value: _autoBackup,
                  onChanged: (value) {
                    setState(() {
                      _autoBackup = value;
                    });
                  },
                  secondary: const Icon(Icons.schedule),
                ),
                ListTile(
                  title: const Text('تكرار النسخ الاحتياطي'),
                  subtitle: Text(_backupFrequency),
                  leading: const Icon(Icons.repeat),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showBackupFrequencyDialog(),
                ),
                ListTile(
                  title: const Text('إنشاء نسخة احتياطية الآن'),
                  subtitle: const Text('حفظ نسخة من البيانات الحالية'),
                  leading: const Icon(Icons.save),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _createBackupNow(),
                ),
                ListTile(
                  title: const Text('استعادة من نسخة احتياطية'),
                  subtitle: const Text('استعادة البيانات من ملف'),
                  leading: const Icon(Icons.restore),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _restoreFromBackup(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الأمان والخصوصية
            _buildSectionCard(
              'الأمان والخصوصية',
              Icons.security,
              Colors.red,
              [
                ListTile(
                  title: const Text('تغيير كلمة المرور'),
                  subtitle: const Text('تحديث كلمة مرور التطبيق'),
                  leading: const Icon(Icons.lock),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showChangePasswordDialog(),
                ),
                ListTile(
                  title: const Text('بصمة الإصبع'),
                  subtitle: const Text('تفعيل الدخول ببصمة الإصبع'),
                  leading: const Icon(Icons.fingerprint),
                  trailing: Switch(
                    value: false,
                    onChanged: (value) {
                      // TODO: تطبيق بصمة الإصبع
                    },
                  ),
                ),
                ListTile(
                  title: const Text('مسح البيانات'),
                  subtitle: const Text('حذف جميع البيانات نهائياً'),
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showClearDataDialog(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حول التطبيق
            _buildSectionCard(
              'حول التطبيق',
              Icons.info,
              Colors.blue,
              [
                const ListTile(
                  title: Text('إصدار التطبيق'),
                  subtitle: Text('1.0.0 (Build 1)'),
                  leading: Icon(Icons.info_outline),
                ),
                ListTile(
                  title: const Text('التحديثات'),
                  subtitle: const Text('البحث عن تحديثات جديدة'),
                  leading: const Icon(Icons.system_update),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _checkForUpdates(),
                ),
                ListTile(
                  title: const Text('الدعم الفني'),
                  subtitle: const Text('التواصل مع فريق الدعم'),
                  leading: const Icon(Icons.support),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _contactSupport(),
                ),
                ListTile(
                  title: const Text('تقييم التطبيق'),
                  subtitle: const Text('ساعدنا بتقييمك للتطبيق'),
                  leading: const Icon(Icons.star),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _rateApp(),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, Color color, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'العربية',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'English',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر العملة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('ريال سعودي (ر.س)'),
              value: 'ر.س',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('دولار أمريكي (\$)'),
              value: '\$',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('يورو (€)'),
              value: '€',
              groupValue: _currency,
              onChanged: (value) {
                setState(() {
                  _currency = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBackupFrequencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تكرار النسخ الاحتياطي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('يومي'),
              value: 'يومي',
              groupValue: _backupFrequency,
              onChanged: (value) {
                setState(() {
                  _backupFrequency = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('أسبوعي'),
              value: 'أسبوعي',
              groupValue: _backupFrequency,
              onChanged: (value) {
                setState(() {
                  _backupFrequency = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('شهري'),
              value: 'شهري',
              groupValue: _backupFrequency,
              onChanged: (value) {
                setState(() {
                  _backupFrequency = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _createBackupNow() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري إنشاء النسخة الاحتياطية...'),
          ],
        ),
      ),
    );

    try {
      await BackupService.instance.createAutoBackup();
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء النسخة الاحتياطية: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _restoreFromBackup() async {
    // TODO: تطبيق استعادة النسخة الاحتياطية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الاستعادة قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showChangePasswordDialog() {
    // TODO: تطبيق تغيير كلمة المرور
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة تغيير كلمة المرور قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير'),
        content: const Text('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: تطبيق مسح البيانات
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('ميزة مسح البيانات قيد التطوير'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _checkForUpdates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('أنت تستخدم أحدث إصدار من التطبيق'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الدعم الفني قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _rateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شكراً لك! ميزة التقييم قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
