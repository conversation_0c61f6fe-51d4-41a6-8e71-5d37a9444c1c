import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/backup_provider.dart';
import 'lib/providers/product_provider.dart';
import 'lib/providers/customer_provider.dart';
import 'lib/providers/supplier_provider.dart';
import 'lib/providers/sale_provider.dart';
import 'lib/providers/purchase_provider.dart';
import 'lib/providers/order_provider.dart';
import 'lib/providers/expense_provider.dart';
import 'lib/providers/activity_provider.dart';
import 'lib/providers/customer_statement_provider.dart';
import 'lib/providers/supplier_statement_provider.dart';
import 'lib/providers/internal_transfer_provider.dart';
import 'lib/providers/analytics_provider.dart';
import 'lib/providers/store_inventory_provider.dart';

void main() {
  runApp(const TestSyncApp());
}

class TestSyncApp extends StatelessWidget {
  const TestSyncApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => BackupProvider()),
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => SupplierProvider()),
        ChangeNotifierProvider(create: (_) => SaleProvider()),
        ChangeNotifierProvider(create: (_) => PurchaseProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => ExpenseProvider()),
        ChangeNotifierProvider(create: (_) => ActivityProvider()),
        ChangeNotifierProvider(create: (_) => CustomerStatementProvider()),
        ChangeNotifierProvider(create: (_) => SupplierStatementProvider()),
        ChangeNotifierProvider(create: (_) => InternalTransferProvider()),
        ChangeNotifierProvider(create: (_) => AnalyticsProvider()),
        ChangeNotifierProvider(create: (_) => StoreInventoryProvider()),
      ],
      child: MaterialApp(
        title: 'Test Sync System',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: const TestSyncScreen(),
      ),
    );
  }
}

class TestSyncScreen extends StatefulWidget {
  const TestSyncScreen({super.key});

  @override
  State<TestSyncScreen> createState() => _TestSyncScreenState();
}

class _TestSyncScreenState extends State<TestSyncScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('اختبار نظام المزامنة'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: Consumer<BackupProvider>(
          builder: (context, backupProvider, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الحالة
                  _buildStatusCard(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // أزرار التحكم
                  _buildControlButtons(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // معلومات المزامنة
                  _buildSyncInfo(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // قائمة النسخ الاحتياطية
                  _buildBackupsList(backupProvider),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusCard(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildStatusRow('تسجيل الدخول', backupProvider.isSignedIn ? 'مسجل' : 'غير مسجل'),
            _buildStatusRow('جاري التحميل', backupProvider.isLoading ? 'نعم' : 'لا'),
            _buildStatusRow('جاري المزامنة', backupProvider.isSyncing ? 'نعم' : 'لا'),
            _buildStatusRow('يمكن المزامنة', backupProvider.canSync ? 'نعم' : 'لا'),
            _buildStatusRow('النسخ المحلية', '${backupProvider.localBackups.length}'),
            _buildStatusRow('النسخ السحابية', '${backupProvider.driveBackups.length}'),
            
            if (backupProvider.currentUser != null)
              _buildStatusRow('المستخدم', backupProvider.currentUser!.email),
            
            if (backupProvider.errorMessage != null)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'خطأ: ${backupProvider.errorMessage}',
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ),
            
            if (backupProvider.successMessage != null)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'نجح: ${backupProvider.successMessage}',
                  style: const TextStyle(color: Colors.green, fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildControlButtons(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أزرار التحكم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: backupProvider.isSignedIn 
                      ? null 
                      : () => backupProvider.signInGoogle(),
                  child: const Text('تسجيل الدخول'),
                ),
                
                ElevatedButton(
                  onPressed: !backupProvider.isSignedIn 
                      ? null 
                      : () => backupProvider.signOutGoogle(),
                  child: const Text('تسجيل الخروج'),
                ),
                
                ElevatedButton(
                  onPressed: () => backupProvider.performLocalBackup(),
                  child: const Text('نسخ محلي'),
                ),
                
                ElevatedButton(
                  onPressed: !backupProvider.isSignedIn 
                      ? null 
                      : () => backupProvider.performGoogleDriveBackup(),
                  child: const Text('نسخ سحابي'),
                ),
                
                ElevatedButton(
                  onPressed: backupProvider.canSync 
                      ? () => backupProvider.performSync(context)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('مزامنة'),
                ),
                
                ElevatedButton(
                  onPressed: () => backupProvider.refreshDriveBackups(),
                  child: const Text('تحديث القوائم'),
                ),
                
                ElevatedButton(
                  onPressed: () => backupProvider.clearMessages(),
                  child: const Text('مسح الرسائل'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncInfo(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المزامنة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(backupProvider.lastSyncStatusText),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupsList(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'النسخ الاحتياطية السحابية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            if (backupProvider.driveBackups.isEmpty)
              const Text('لا توجد نسخ احتياطية سحابية')
            else
              ...backupProvider.driveBackups.map((backup) {
                final info = backupProvider.getDriveFileInfo(backup);
                return ListTile(
                  title: Text(info['name']),
                  subtitle: Text('${info['sizeFormatted']} - ${info['modifiedTime']}'),
                  trailing: ElevatedButton(
                    onPressed: () => backupProvider.restoreFromGoogleDrive(backup),
                    child: const Text('استعادة'),
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }
}
