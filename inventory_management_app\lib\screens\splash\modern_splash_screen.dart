import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_design_constants.dart';
import '../onboarding/onboarding_screen.dart';
import '../enhanced_dashboard_screen.dart';

class ModernSplashScreen extends StatefulWidget {
  const ModernSplashScreen({super.key});

  @override
  State<ModernSplashScreen> createState() => _ModernSplashScreenState();
}

class _ModernSplashScreenState extends State<ModernSplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // تحكم في حركة الشعار
    _logoController = AnimationController(
      duration: AppDesignConstants.splashAnimationDuration,
      vsync: this,
    );

    // تحكم في التلاشي
    _fadeController = AnimationController(
      duration: AppDesignConstants.mediumAnimationDuration,
      vsync: this,
    );

    // حركة الشعار من الأسفل للأعلى مع تكبير
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // تأثير التلاشي
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashSequence() async {
    // بدء الرسوم المتحركة
    await Future.delayed(const Duration(milliseconds: 300));
    _logoController.forward();
    
    await Future.delayed(const Duration(milliseconds: 500));
    _fadeController.forward();

    // انتظار مدة العرض الكاملة
    await Future.delayed(AppDesignConstants.splashDuration);
    
    // التحقق من حالة المستخدم والانتقال
    await _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasViewedOnboarding = prefs.getBool(
        AppDesignConstants.hasViewedOnboardingKey,
      ) ?? false;

      if (!mounted) return;

      if (hasViewedOnboarding) {
        // المستخدم شاهد الـ onboarding من قبل، انتقل للشاشة الرئيسية
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const EnhancedDashboardScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: AppDesignConstants.mediumAnimationDuration,
          ),
        );
      } else {
        // مستخدم جديد، انتقل لشاشة الـ onboarding
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const OnboardingScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1.0, 0.0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            },
            transitionDuration: AppDesignConstants.mediumAnimationDuration,
          ),
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، انتقل للشاشة الرئيسية
      if (!mounted) return;
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const EnhancedDashboardScreen(),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعيين شريط الحالة شفاف
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppDesignConstants.splashGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // المساحة العلوية
              const Spacer(flex: 2),
              
              // الشعار والنص الرئيسي
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // أيقونة الشعار مع حركة
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: AppDesignConstants.splashIconSize + 20,
                            height: AppDesignConstants.splashIconSize + 20,
                            decoration: BoxDecoration(
                              color: AppDesignConstants.primaryColor,
                              borderRadius: BorderRadius.circular(
                                AppDesignConstants.extraLargeBorderRadius,
                              ),
                              boxShadow: AppDesignConstants.cardShadow,
                            ),
                            child: const Icon(
                              Icons.store_outlined,
                              size: AppDesignConstants.splashIconSize,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: AppDesignConstants.extraLargePadding),
                    
                    // اسم التطبيق مع تأثير الكتابة
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: AnimatedTextKit(
                        animatedTexts: [
                          TyperAnimatedText(
                            AppDesignConstants.appName,
                            textStyle: AppDesignConstants.splashTitleStyle,
                            speed: const Duration(milliseconds: 100),
                          ),
                        ],
                        totalRepeatCount: 1,
                        displayFullTextOnTap: true,
                      ),
                    ),
                    
                    const SizedBox(height: AppDesignConstants.defaultPadding),
                    
                    // الشعار التوضيحي
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Text(
                        AppDesignConstants.appSlogan,
                        style: AppDesignConstants.splashSubtitleStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              
              // مؤشر التحميل
              Expanded(
                flex: 1,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SpinKitWave(
                        color: AppDesignConstants.primaryColor,
                        size: 30.0,
                      ),
                      const SizedBox(height: AppDesignConstants.defaultPadding),
                      Text(
                        'جاري التحميل...',
                        style: TextStyle(
                          fontSize: AppDesignConstants.captionFontSize,
                          color: AppDesignConstants.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // المساحة السفلية
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }
}
