import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/models/product.dart';

class ProductDetailsScreen extends StatefulWidget {
  final int? productId;
  final bool isReadOnly;

  const ProductDetailsScreen({
    super.key,
    this.productId,
    this.isReadOnly = false,
  });

  @override
  _ProductDetailsScreenState createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late String _name;
  late String _description;
  late double _price;
  late double? _quantity;
  Product? _product;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProduct();
  }

  void _loadProduct() {
    if (widget.productId != null) {
      setState(() {
        _isLoading = true;
      });

      final ProductProvider productProvider = context.read<ProductProvider>();
      _product = productProvider.products
          .where((Product p) => p.id == widget.productId)
          .firstOrNull;

      _name = _product?.name ?? '';
      _description = _product?.description ?? '';
      _price = _product?.price ?? 0.0;
      _quantity = _product?.quantity;

      setState(() {
        _isLoading = false;
      });
    } else {
      _name = '';
      _description = '';
      _price = 0.0;
      _quantity = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.productId == null
                ? 'إضافة منتج جديد'
                : widget.isReadOnly
                    ? 'تفاصيل المنتج'
                    : 'تعديل المنتج'
          ),
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              TextFormField(
                initialValue: _name,
                readOnly: widget.isReadOnly,
                decoration: const InputDecoration(labelText: 'اسم المنتج'),
                validator: (String? value) {
                  if (!widget.isReadOnly && (value == null || value.isEmpty)) {
                    return 'يرجى إدخال اسم المنتج';
                  }
                  return null;
                },
                onSaved: (String? value) => _name = value!,
              ),
              TextFormField(
                initialValue: _description,
                readOnly: widget.isReadOnly,
                decoration: const InputDecoration(labelText: 'الوصف'),
                onSaved: (String? value) => _description = value!,
              ),
              TextFormField(
                initialValue: _price.toString(),
                readOnly: widget.isReadOnly,
                decoration: const InputDecoration(
                  labelText: 'السعر',
                  suffixText: 'ر.س',
                ),
                keyboardType: TextInputType.number,
                onSaved: (String? value) => _price = double.parse(value!),
              ),
              TextFormField(
                initialValue: _quantity?.toString() ?? '',
                readOnly: widget.isReadOnly,
                decoration: const InputDecoration(labelText: 'الكمية'),
                keyboardType: TextInputType.number,
                onSaved: (String? value) => _quantity = double.tryParse(value!),
              ),
              if (!widget.isReadOnly)
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      _formKey.currentState!.save();
                      final Product product = Product(
                        id: _product?.id,
                        name: _name,
                        description: _description,
                        price: _price,
                        quantity: _quantity,
                      );
                      final ProductProvider productProvider =
                          Provider.of<ProductProvider>(context, listen: false);
                      if (widget.productId == null) {
                        productProvider.addProduct(product);
                      } else {
                        productProvider.updateProduct(product);
                      }
                      Navigator.pop(context);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(widget.productId == null ? 'إضافة' : 'حفظ'),
                ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}
