import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/backup_provider.dart';

void main() {
  runApp(const TestSyncSimpleApp());
}

class TestSyncSimpleApp extends StatelessWidget {
  const TestSyncSimpleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BackupProvider(),
      child: MaterialApp(
        title: 'Test Sync Simple',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: const TestSyncSimpleScreen(),
      ),
    );
  }
}

class TestSyncSimpleScreen extends StatefulWidget {
  const TestSyncSimpleScreen({super.key});

  @override
  State<TestSyncSimpleScreen> createState() => _TestSyncSimpleScreenState();
}

class _TestSyncSimpleScreenState extends State<TestSyncSimpleScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('اختبار المزامنة البسيط'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: Consumer<BackupProvider>(
          builder: (context, backupProvider, child) {
            return Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // حالة النظام
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'حالة النظام',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 12),
                          Text('تسجيل الدخول: ${backupProvider.isSignedIn ? "مسجل" : "غير مسجل"}'),
                          Text('جاري التحميل: ${backupProvider.isLoading ? "نعم" : "لا"}'),
                          Text('جاري المزامنة: ${backupProvider.isSyncing ? "نعم" : "لا"}'),
                          Text('يمكن المزامنة: ${backupProvider.canSync ? "نعم" : "لا"}'),
                          Text('النسخ السحابية: ${backupProvider.driveBackups.length}'),
                          if (backupProvider.currentUser != null)
                            Text('المستخدم: ${backupProvider.currentUser!.email}'),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // الرسائل
                  if (backupProvider.errorMessage != null)
                    Card(
                      color: Colors.red.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          'خطأ: ${backupProvider.errorMessage}',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ),
                  
                  if (backupProvider.successMessage != null)
                    Card(
                      color: Colors.green.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          'نجح: ${backupProvider.successMessage}',
                          style: const TextStyle(color: Colors.green),
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات المزامنة
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'معلومات المزامنة',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 12),
                          Text(backupProvider.lastSyncStatusText),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // الأزرار
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: backupProvider.isSignedIn 
                            ? null 
                            : () => backupProvider.signInGoogle(),
                        child: const Text('تسجيل الدخول'),
                      ),
                      
                      ElevatedButton(
                        onPressed: !backupProvider.isSignedIn 
                            ? null 
                            : () => backupProvider.signOutGoogle(),
                        child: const Text('تسجيل الخروج'),
                      ),
                      
                      ElevatedButton(
                        onPressed: !backupProvider.isSignedIn 
                            ? null 
                            : () => backupProvider.performGoogleDriveBackup(),
                        child: const Text('نسخ احتياطي'),
                      ),
                      
                      ElevatedButton(
                        onPressed: backupProvider.canSync 
                            ? () => _showSyncDialog(backupProvider)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('مزامنة'),
                      ),
                      
                      ElevatedButton(
                        onPressed: () => backupProvider.refreshDriveBackups(),
                        child: const Text('تحديث'),
                      ),
                      
                      ElevatedButton(
                        onPressed: () => backupProvider.clearMessages(),
                        child: const Text('مسح الرسائل'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // قائمة النسخ الاحتياطية
                  if (backupProvider.driveBackups.isNotEmpty)
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'النسخ الاحتياطية السحابية',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 12),
                              Expanded(
                                child: ListView.builder(
                                  itemCount: backupProvider.driveBackups.length,
                                  itemBuilder: (context, index) {
                                    final backup = backupProvider.driveBackups[index];
                                    final info = backupProvider.getDriveFileInfo(backup);
                                    return ListTile(
                                      title: Text(info['name']),
                                      subtitle: Text('${info['sizeFormatted']}'),
                                      trailing: ElevatedButton(
                                        onPressed: () => backupProvider.restoreFromGoogleDrive(backup),
                                        child: const Text('استعادة'),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _showSyncDialog(BackupProvider backupProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المزامنة'),
        content: const Text(
          'هل تريد المزامنة مع أحدث نسخة احتياطية على Google Drive؟\n\n'
          'تحذير: ستحذف جميع البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              backupProvider.performSync(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
