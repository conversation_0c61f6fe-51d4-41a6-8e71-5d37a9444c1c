import 'package:flutter/material.dart';

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String? confirmText;
  final String? cancelText;
  final Color? confirmColor;
  final IconData? icon;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText,
    this.cancelText,
    this.confirmColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Row(
          children: <Widget>[
            if (icon != null) ...<Widget>[
              Icon(
                icon,
                color: confirmColor ?? Colors.blue,
                size: 28,
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          content,
          style: const TextStyle(fontSize: 16),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              cancelText ?? 'إلغاء',
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor ?? Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(confirmText ?? 'تأكيد'),
          ),
        ],
      ),
    );
  }
}

class DeleteConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String itemName;

  const DeleteConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    required this.itemName,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: title,
      content: '$content\n\nالعنصر: $itemName\n\nلا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      confirmColor: Colors.red,
      icon: Icons.delete_forever,
    );
  }
}

class PaymentConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final double amount;

  const PaymentConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: title,
      content: '$content\n\nالمبلغ: ${amount.toStringAsFixed(2)} ر.س',
      confirmText: 'تأكيد الدفع',
      cancelText: 'إلغاء',
      confirmColor: Colors.green,
      icon: Icons.payments,
    );
  }
}

class EditConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String itemName;

  const EditConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    required this.itemName,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: title,
      content: '$content\n\nالعنصر: $itemName\n\nأي تعديل قد يؤثر على المخزون والسجلات المالية.',
      confirmText: 'متابعة التعديل',
      cancelText: 'إلغاء',
      confirmColor: Colors.orange,
      icon: Icons.edit,
    );
  }
}

class SaveConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final double? amount;

  const SaveConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return ConfirmationDialog(
      title: title,
      content: amount != null 
          ? '$content\n\nالمبلغ الإجمالي: ${amount!.toStringAsFixed(2)} ر.س'
          : content,
      confirmText: 'حفظ',
      cancelText: 'إلغاء',
      confirmColor: Colors.blue,
      icon: Icons.save,
    );
  }
}
