/// Model class representing a sale transaction
class Sale {
  /// Unique identifier for the sale
  int? id;

  /// Customer ID for this sale
  int? customerId;

  /// Date of the sale
  String? date;

  /// Total amount of the sale
  double? total;

  /// Additional notes for the sale
  String? notes;

  /// Getter for saleDate (alias for date)
  String? get saleDate => date;

  /// Getter for totalAmount (alias for total)
  double? get totalAmount => total;

  /// Constructor for creating a Sale instance
  Sale({
    this.id,
    this.customerId,
    this.date,
    this.total,
    this.notes,
  });

  /// Converts the Sale instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'customerId': customerId,
      'date': date,
      'total': total,
      'notes': notes,
    };
  }

  /// Creates a Sale instance from a Map (typically from database)
  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      date: map['date'] as String?,
      total: map['total']?.toDouble(),
      notes: map['notes'] as String?,
    );
  }

  @override
  String toString() {
    return 'Sale{id: $id, customerId: $customerId, date: $date, '
        'total: $total, notes: $notes}';
  }
}
