import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../config/app_constants.dart';
import '../utils/settings_helper.dart';
import '../utils/date_helper.dart';

/// فئة مساعدة للإشعارات
class NotificationHelper {
  static bool _isInitialized = false;

  /// تهيئة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (!kIsWeb) {
        // TODO: تهيئة إشعارات الهاتف المحمول
        // await FlutterLocalNotificationsPlugin().initialize(...);
      }
      
      _isInitialized = true;
    } catch (e) {
      debugPrint('خطأ في تهيئة الإشعارات: $e');
    }
  }

  /// إرسال إشعار فوري
  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    if (!SettingsHelper.getNotifications()) return;
    
    try {
      if (kIsWeb) {
        _showWebNotification(title, body);
      } else {
        await _showMobileNotification(title, body, payload, priority);
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار: $e');
    }
  }

  /// جدولة إشعار
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    if (!SettingsHelper.getNotifications()) return;
    
    try {
      if (!kIsWeb) {
        // TODO: جدولة إشعار للهاتف المحمول
        // await FlutterLocalNotificationsPlugin().zonedSchedule(...);
      }
    } catch (e) {
      debugPrint('خطأ في جدولة الإشعار: $e');
    }
  }

  /// إلغاء إشعار مجدول
  static Future<void> cancelNotification(int id) async {
    try {
      if (!kIsWeb) {
        // TODO: إلغاء إشعار للهاتف المحمول
        // await FlutterLocalNotificationsPlugin().cancel(id);
      }
    } catch (e) {
      debugPrint('خطأ في إلغاء الإشعار: $e');
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    try {
      if (!kIsWeb) {
        // TODO: إلغاء جميع الإشعارات للهاتف المحمول
        // await FlutterLocalNotificationsPlugin().cancelAll();
      }
    } catch (e) {
      debugPrint('خطأ في إلغاء الإشعارات: $e');
    }
  }

  /// إشعار المخزون المنخفض
  static Future<void> notifyLowStock(String productName, int currentStock, int minLevel) async {
    await showNotification(
      title: 'تنبيه: مخزون منخفض',
      body: 'المنتج "$productName" وصل إلى الحد الأدنى ($currentStock من $minLevel)',
      priority: NotificationPriority.high,
    );
  }

  /// إشعار نفاد المخزون
  static Future<void> notifyOutOfStock(String productName) async {
    await showNotification(
      title: 'تحذير: نفد المخزون',
      body: 'المنتج "$productName" نفد من المخزون',
      priority: NotificationPriority.urgent,
    );
  }

  /// إشعار بيع جديد
  static Future<void> notifyNewSale(String invoiceNumber, double amount) async {
    await showNotification(
      title: 'بيع جديد',
      body: 'تم إنشاء فاتورة بيع رقم $invoiceNumber بمبلغ ${amount.toStringAsFixed(2)} ر.س',
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار شراء جديد
  static Future<void> notifyNewPurchase(String invoiceNumber, double amount) async {
    await showNotification(
      title: 'شراء جديد',
      body: 'تم إنشاء فاتورة شراء رقم $invoiceNumber بمبلغ ${amount.toStringAsFixed(2)} ر.س',
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار تحديث متاح
  static Future<void> notifyUpdateAvailable(String version) async {
    await showNotification(
      title: 'تحديث متاح',
      body: 'يتوفر إصدار جديد ($version) من التطبيق',
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار اكتمال النسخ الاحتياطي
  static Future<void> notifyBackupCompleted(String fileName) async {
    await showNotification(
      title: 'تم إنشاء النسخة الاحتياطية',
      body: 'تم حفظ النسخة الاحتياطية: $fileName',
      priority: NotificationPriority.low,
    );
  }

  /// إشعار فشل النسخ الاحتياطي
  static Future<void> notifyBackupFailed(String error) async {
    await showNotification(
      title: 'فشل في النسخ الاحتياطي',
      body: 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: $error',
      priority: NotificationPriority.high,
    );
  }

  /// إشعار تذكير بالمهام
  static Future<void> scheduleTaskReminder({
    required int id,
    required String task,
    required DateTime reminderTime,
  }) async {
    await scheduleNotification(
      id: id,
      title: 'تذكير بالمهمة',
      body: task,
      scheduledDate: reminderTime,
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار تذكير بالدفع
  static Future<void> schedulePaymentReminder({
    required int id,
    required String customerName,
    required double amount,
    required DateTime dueDate,
  }) async {
    await scheduleNotification(
      id: id,
      title: 'تذكير بالدفع',
      body: 'استحقاق دفع من العميل $customerName بمبلغ ${amount.toStringAsFixed(2)} ر.س',
      scheduledDate: dueDate,
      priority: NotificationPriority.high,
    );
  }

  /// إشعار تذكير بإعادة الطلب
  static Future<void> scheduleReorderReminder({
    required int id,
    required String productName,
    required DateTime reminderDate,
  }) async {
    await scheduleNotification(
      id: id,
      title: 'تذكير بإعادة الطلب',
      body: 'حان وقت إعادة طلب المنتج: $productName',
      scheduledDate: reminderDate,
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار تقرير يومي
  static Future<void> scheduleDailyReport() async {
    final now = DateTime.now();
    final reportTime = DateTime(now.year, now.month, now.day, 18, 0); // 6 مساءً
    
    if (reportTime.isBefore(now)) {
      // إذا فات الوقت اليوم، جدول للغد
      final tomorrow = reportTime.add(const Duration(days: 1));
      await scheduleNotification(
        id: AppConstants.dailyReportNotificationId,
        title: 'التقرير اليومي',
        body: 'اضغط لعرض تقرير اليوم',
        scheduledDate: tomorrow,
        priority: NotificationPriority.low,
      );
    } else {
      await scheduleNotification(
        id: AppConstants.dailyReportNotificationId,
        title: 'التقرير اليومي',
        body: 'اضغط لعرض تقرير اليوم',
        scheduledDate: reportTime,
        priority: NotificationPriority.low,
      );
    }
  }

  /// إشعار تقرير أسبوعي
  static Future<void> scheduleWeeklyReport() async {
    final now = DateTime.now();
    final daysUntilSunday = (7 - now.weekday) % 7;
    final nextSunday = now.add(Duration(days: daysUntilSunday));
    final reportTime = DateTime(nextSunday.year, nextSunday.month, nextSunday.day, 9, 0);
    
    await scheduleNotification(
      id: AppConstants.weeklyReportNotificationId,
      title: 'التقرير الأسبوعي',
      body: 'اضغط لعرض تقرير الأسبوع',
      scheduledDate: reportTime,
      priority: NotificationPriority.low,
    );
  }

  /// إشعار تقرير شهري
  static Future<void> scheduleMonthlyReport() async {
    final now = DateTime.now();
    final nextMonth = DateTime(now.year, now.month + 1, 1, 9, 0);
    
    await scheduleNotification(
      id: AppConstants.monthlyReportNotificationId,
      title: 'التقرير الشهري',
      body: 'اضغط لعرض تقرير الشهر',
      scheduledDate: nextMonth,
      priority: NotificationPriority.low,
    );
  }

  /// التحقق من الأذونات
  static Future<bool> checkPermissions() async {
    try {
      if (kIsWeb) {
        return await _checkWebNotificationPermission();
      } else {
        // TODO: التحقق من أذونات الهاتف المحمول
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// طلب الأذونات
  static Future<bool> requestPermissions() async {
    try {
      if (kIsWeb) {
        return await _requestWebNotificationPermission();
      } else {
        // TODO: طلب أذونات الهاتف المحمول
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// إشعار ويب
  static void _showWebNotification(String title, String body) {
    if (kIsWeb) {
      // TODO: تنفيذ إشعارات الويب
      // if (html.Notification.supported) {
      //   html.Notification(title, body: body, icon: '/icons/icon-192.png');
      // }
    }
  }

  /// إشعار الهاتف المحمول
  static Future<void> _showMobileNotification(
    String title,
    String body,
    String? payload,
    NotificationPriority priority,
  ) async {
    // TODO: تنفيذ إشعارات الهاتف المحمول
    // const androidDetails = AndroidNotificationDetails(
    //   'default_channel',
    //   'Default Channel',
    //   importance: Importance.max,
    //   priority: Priority.high,
    // );
    // 
    // const iosDetails = IOSNotificationDetails();
    // 
    // const details = NotificationDetails(
    //   android: androidDetails,
    //   iOS: iosDetails,
    // );
    // 
    // await FlutterLocalNotificationsPlugin().show(
    //   DateTime.now().millisecondsSinceEpoch ~/ 1000,
    //   title,
    //   body,
    //   details,
    //   payload: payload,
    // );
  }

  /// التحقق من أذونات الويب
  static Future<bool> _checkWebNotificationPermission() async {
    if (kIsWeb) {
      // TODO: التحقق من أذونات إشعارات الويب
      // return html.Notification.permission == 'granted';
    }
    return false;
  }

  /// طلب أذونات الويب
  static Future<bool> _requestWebNotificationPermission() async {
    if (kIsWeb) {
      // TODO: طلب أذونات إشعارات الويب
      // final permission = await html.Notification.requestPermission();
      // return permission == 'granted';
    }
    return false;
  }

  /// الحصول على الإشعارات المعلقة
  static Future<List<PendingNotification>> getPendingNotifications() async {
    try {
      if (!kIsWeb) {
        // TODO: الحصول على الإشعارات المعلقة للهاتف المحمول
        // final pending = await FlutterLocalNotificationsPlugin().pendingNotificationRequests();
        // return pending.map((p) => PendingNotification.fromPlugin(p)).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  /// تنظيف الإشعارات القديمة
  static Future<void> cleanupOldNotifications() async {
    try {
      final pending = await getPendingNotifications();
      final now = DateTime.now();
      
      for (final notification in pending) {
        // إلغاء الإشعارات التي مر عليها أكثر من شهر
        if (notification.scheduledDate != null &&
            now.difference(notification.scheduledDate!).inDays > 30) {
          await cancelNotification(notification.id);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف الإشعارات: $e');
    }
  }

  /// تفعيل/إلغاء الإشعارات
  static Future<void> setNotificationsEnabled(bool enabled) async {
    await SettingsHelper.setNotifications(enabled);
    
    if (!enabled) {
      await cancelAllNotifications();
    }
  }

  /// التحقق من تفعيل الإشعارات
  static bool areNotificationsEnabled() {
    return SettingsHelper.getNotifications();
  }
}

/// أولوية الإشعار
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// إشعار معلق
class PendingNotification {
  final int id;
  final String title;
  final String body;
  final DateTime? scheduledDate;
  final String? payload;

  PendingNotification({
    required this.id,
    required this.title,
    required this.body,
    this.scheduledDate,
    this.payload,
  });

  @override
  String toString() {
    return 'PendingNotification(id: $id, title: $title, scheduledDate: $scheduledDate)';
  }
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool enabled;
  final bool lowStockAlerts;
  final bool saleNotifications;
  final bool purchaseNotifications;
  final bool backupNotifications;
  final bool dailyReports;
  final bool weeklyReports;
  final bool monthlyReports;
  final bool paymentReminders;
  final bool taskReminders;

  NotificationSettings({
    this.enabled = true,
    this.lowStockAlerts = true,
    this.saleNotifications = true,
    this.purchaseNotifications = true,
    this.backupNotifications = true,
    this.dailyReports = false,
    this.weeklyReports = false,
    this.monthlyReports = false,
    this.paymentReminders = true,
    this.taskReminders = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'enabled': enabled,
      'lowStockAlerts': lowStockAlerts,
      'saleNotifications': saleNotifications,
      'purchaseNotifications': purchaseNotifications,
      'backupNotifications': backupNotifications,
      'dailyReports': dailyReports,
      'weeklyReports': weeklyReports,
      'monthlyReports': monthlyReports,
      'paymentReminders': paymentReminders,
      'taskReminders': taskReminders,
    };
  }

  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      enabled: map['enabled'] ?? true,
      lowStockAlerts: map['lowStockAlerts'] ?? true,
      saleNotifications: map['saleNotifications'] ?? true,
      purchaseNotifications: map['purchaseNotifications'] ?? true,
      backupNotifications: map['backupNotifications'] ?? true,
      dailyReports: map['dailyReports'] ?? false,
      weeklyReports: map['weeklyReports'] ?? false,
      monthlyReports: map['monthlyReports'] ?? false,
      paymentReminders: map['paymentReminders'] ?? true,
      taskReminders: map['taskReminders'] ?? true,
    );
  }
}
