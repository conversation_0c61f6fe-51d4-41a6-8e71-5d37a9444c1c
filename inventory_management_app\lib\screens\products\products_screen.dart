import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/widgets/empty_state_widget.dart'
    as widgets;
import 'package:inventory_management_app/widgets/confirmation_dialog.dart';

/// Screen for displaying and managing products
class ProductsScreen extends StatelessWidget {
  /// Constructor for ProductsScreen
  const ProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ProductProvider productProvider =
        Provider.of<ProductProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Products'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.go('/products/add');
            },
          ),
        ],
      ),
      body: productProvider.isLoading
          ? const widgets.LoadingWidget(message: 'Loading products...')
          : productProvider.error != null
              ? widgets.ErrorWidget(
                  message: productProvider.error!,
                  onRetry: () => productProvider.fetchProducts(),
                )
              : productProvider.products.isEmpty
                  ? const widgets.EmptyStateWidget(
                      title: 'No Products',
                      message: 'Add your first product to get started',
                      icon: Icons.inventory,
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: productProvider.products.length,
                      itemBuilder: (BuildContext context, int index) {
                        final Product product = productProvider.products[index];
                        return Card(
                          child: ListTile(
                            title: Text(product.name),
                            subtitle: Text(product.description),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: <Widget>[
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () {
                                    context.go('/products/edit/${product.id}');
                                  },
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () async {
                                    final bool? confirmed =
                                        await ConfirmationDialog
                                            .showDeleteConfirmation(
                                      context,
                                      itemName: 'product',
                                    );
                                    if (confirmed == true) {
                                      await productProvider
                                          .deleteProduct(product.id!);
                                      if (context.mounted) {
                                        SnackBarUtils.showSuccess(context,
                                            'Product deleted successfully');
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }
}
