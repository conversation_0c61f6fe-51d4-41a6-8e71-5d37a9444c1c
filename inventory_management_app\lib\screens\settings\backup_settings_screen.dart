import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import '../../providers/backup_provider.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';

/// شاشة إعدادات النسخ الاحتياطي المتقدمة
class BackupSettingsScreen extends StatefulWidget {
  const BackupSettingsScreen({super.key});

  @override
  State<BackupSettingsScreen> createState() => _BackupSettingsScreenState();
}

class _BackupSettingsScreenState extends State<BackupSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text(
            'النسخ الاحتياطي والاستعادة',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Consumer<BackupProvider>(
          builder: (BuildContext context, BackupProvider backupProvider,
              Widget? child) {
            if (backupProvider.isLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري المعالجة...'),
                  ],
                ),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: <Widget>[
                  // رسائل النجاح والخطأ
                  if (backupProvider.successMessage != null)
                    _buildMessageCard(
                      backupProvider.successMessage!,
                      Colors.green,
                      Icons.check_circle,
                    ),

                  if (backupProvider.errorMessage != null)
                    _buildMessageCard(
                      backupProvider.errorMessage!,
                      Colors.red,
                      Icons.error,
                    ),

                  // النسخ الاحتياطي المحلي
                  _buildLocalBackupSection(backupProvider),

                  const SizedBox(height: 20),

                  // Google Drive
                  _buildGoogleDriveSection(backupProvider),

                  const SizedBox(height: 20),

                  // النسخ الاحتياطي التلقائي
                  _buildAutoBackupSection(backupProvider),

                  const SizedBox(height: 20),

                  // المزامنة عبر Google Drive
                  if (backupProvider.isSignedIn)
                    _buildSyncSection(backupProvider),

                  if (backupProvider.isSignedIn) const SizedBox(height: 20),

                  // النسخ الاحتياطية المحلية
                  _buildLocalBackupsSection(backupProvider),

                  const SizedBox(height: 20),

                  // النسخ الاحتياطية على Google Drive
                  if (backupProvider.isSignedIn)
                    _buildDriveBackupsSection(backupProvider),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMessageCard(String message, Color color, IconData icon) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: <Widget>[
          Icon(icon, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(color: color.withOpacity(0.8)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              context.read<BackupProvider>().clearMessages();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLocalBackupSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.storage, color: Colors.blue[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطي المحلي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'إنشاء نسخة احتياطية من قاعدة البيانات على الجهاز المحلي',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: <Widget>[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => backupProvider.performLocalBackup(),
                    icon: const Icon(Icons.save),
                    label: const Text('إنشاء نسخة احتياطية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleDriveSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.cloud, color: Colors.green[700]),
                const SizedBox(width: 12),
                const Text(
                  'Google Drive',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (!backupProvider.isSignedIn) ...<Widget>[
              const Text(
                'قم بتسجيل الدخول إلى Google Drive لحفظ النسخ الاحتياطية في السحابة',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Row(
                children: <Widget>[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => backupProvider.signInGoogle(),
                      icon: const Icon(Icons.login),
                      label: const Text('تسجيل الدخول'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...<Widget>[
              Text(
                'مسجل الدخول: ${backupProvider.currentUser?.email ?? "غير معروف"}',
                style: const TextStyle(color: Colors.green),
              ),
              const SizedBox(height: 16),
              Row(
                children: <Widget>[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () =>
                          backupProvider.performGoogleDriveBackup(),
                      icon: const Icon(Icons.cloud_upload),
                      label: const Text('رفع نسخة احتياطية'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: () => backupProvider.signOutGoogle(),
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAutoBackupSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.schedule, color: Colors.orange[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطي التلقائي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخ احتياطية تلقائياً بشكل دوري'),
              value: backupProvider.autoBackupEnabled,
              onChanged: (bool value) =>
                  backupProvider.setAutoBackupEnabled(value),
              activeColor: Colors.orange,
            ),
            if (backupProvider.autoBackupEnabled) ...<Widget>[
              const Divider(),
              ListTile(
                title: const Text('تردد النسخ الاحتياطي'),
                subtitle: Text(backupProvider.autoBackupFrequency.displayName),
                trailing: DropdownButton<BackupFrequency>(
                  value: backupProvider.autoBackupFrequency,
                  onChanged: (Object? frequency) {
                    if (frequency != null) {
                      backupProvider.setAutoBackupFrequency(frequency);
                    }
                  },
                  items: BackupFrequency.values.map((frequency) {
                    return DropdownMenuItem(
                      value: frequency,
                      child: Text(frequency.displayName),
                    );
                  }).toList(),
                ),
              ),
              SwitchListTile(
                title: const Text('النسخ الاحتياطي للسحابة'),
                subtitle: const Text(
                    'رفع النسخ الاحتياطية التلقائية إلى Google Drive'),
                value: backupProvider.autoBackupToCloud,
                onChanged: backupProvider.isSignedIn
                    ? (bool value) => backupProvider.setAutoBackupToCloud(value)
                    : null,
                activeColor: Colors.green,
              ),
              if (!backupProvider.isSignedIn &&
                  backupProvider.autoBackupToCloud)
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'يجب تسجيل الدخول إلى Google Drive أولاً',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.sync, color: Colors.orange[700]),
                const SizedBox(width: 12),
                const Text(
                  'المزامنة عبر Google Drive',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            const Text(
              'مزامنة البيانات مع أحدث نسخة احتياطية على Google Drive',
              style: TextStyle(color: Colors.grey),
            ),

            const SizedBox(height: 12),

            // حالة المزامنة الأخيرة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: <Widget>[
                  Icon(Icons.info_outline, color: Colors.orange[700], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      backupProvider.lastSyncStatusText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // زر المزامنة
            Row(
              children: <Widget>[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: backupProvider.canSync
                        ? () => backupProvider.performSync(context)
                        : null,
                    icon: backupProvider.isSyncing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.sync),
                    label: Text(
                      backupProvider.isSyncing
                          ? 'جاري المزامنة...'
                          : 'مزامنة الآن',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: () => backupProvider.resetSyncStatus(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // تحذير
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Icon(Icons.warning, color: Colors.red[700], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'تحذير: المزامنة ستحذف جميع البيانات الحالية وتستبدلها بأحدث نسخة احتياطية من Google Drive',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalBackupsSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.folder, color: Colors.blue[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطية المحلية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => backupProvider.refreshLocalBackups(),
                  tooltip: 'تحديث القائمة',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (backupProvider.localBackups.isEmpty) ...<Widget>[
              const Center(
                child: Column(
                  children: <Widget>[
                    Icon(Icons.folder_open, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'لا توجد نسخ احتياطية محلية',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ] else ...<Widget>[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: backupProvider.localBackups.length,
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemBuilder: (BuildContext context, int index) {
                  final File backup = backupProvider.localBackups[index];
                  final Map<String, dynamic> info =
                      backupProvider.getBackupInfo(backup);

                  return ListTile(
                    leading: const Icon(Icons.file_copy, color: Colors.blue),
                    title: Text(info['name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text('الحجم: ${info['sizeFormatted']}'),
                        Text(
                            'التاريخ: ${Formatters.formatDateTime(info['lastModified'])}'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (String action) => _handleLocalBackupAction(
                        action,
                        backup,
                        backupProvider,
                      ),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        const PopupMenuItem(
                          value: 'restore',
                          child: ListTile(
                            leading: Icon(Icons.restore, color: Colors.green),
                            title: Text('استعادة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDriveBackupsSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(Icons.cloud_queue, color: Colors.green[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطية على Google Drive',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => backupProvider.refreshDriveBackups(),
                  tooltip: 'تحديث القائمة',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (backupProvider.driveBackups.isEmpty) ...<Widget>[
              const Center(
                child: Column(
                  children: <Widget>[
                    Icon(Icons.cloud_off, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'لا توجد نسخ احتياطية على Google Drive',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ] else ...<Widget>[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: backupProvider.driveBackups.length,
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemBuilder: (BuildContext context, int index) {
                  final drive.File backup = backupProvider.driveBackups[index];
                  final Map<String, dynamic> info =
                      backupProvider.getDriveFileInfo(backup);

                  return ListTile(
                    leading: const Icon(Icons.cloud, color: Colors.green),
                    title: Text(info['name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text('الحجم: ${info['sizeFormatted']}'),
                        if (info['modifiedTime'] != null)
                          Text(
                              'التاريخ: ${Formatters.formatDateTime(info['modifiedTime'])}'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (String action) => _handleDriveBackupAction(
                        action,
                        backup,
                        backupProvider,
                      ),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        const PopupMenuItem(
                          value: 'restore',
                          child: ListTile(
                            leading:
                                Icon(Icons.cloud_download, color: Colors.green),
                            title: Text('استعادة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleLocalBackupAction(
      String action, File backup, BackupProvider backupProvider) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(
            () => backupProvider.restoreLocalBackup(backup));
        break;
      case 'delete':
        _showDeleteConfirmation(() => backupProvider.deleteLocalBackup(backup));
        break;
    }
  }

  void _handleDriveBackupAction(
      String action, drive.File backup, BackupProvider backupProvider) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(
            () => backupProvider.restoreFromGoogleDrive(backup));
        break;
      case 'delete':
        _showDeleteConfirmation(
            () => backupProvider.deleteFromGoogleDrive(backup.id!));
        break;
    }
  }

  void _showRestoreConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (BuildContext context) => EnhancedConfirmationDialog(
        title: 'تأكيد الاستعادة',
        message:
            'تحذير: استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية وتستبدلها ببيانات النسخة الاحتياطية.\n\nهل أنت متأكد من المتابعة؟',
        confirmText: 'استعادة',
        cancelText: 'إلغاء',
        isDanger: true,
        onConfirm: onConfirm,
      ),
    );
  }

  void _showDeleteConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (BuildContext context) => EnhancedConfirmationDialog(
        title: 'تأكيد الحذف',
        message:
            'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟\n\nلا يمكن التراجع عن هذا الإجراء.',
        confirmText: 'حذف',
        cancelText: 'إلغاء',
        isDanger: true,
        onConfirm: onConfirm,
      ),
    );
  }
}
