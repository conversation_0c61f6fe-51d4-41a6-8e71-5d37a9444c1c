import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import '../../providers/backup_provider.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';

/// شاشة إعدادات النسخ الاحتياطي المتقدمة
class BackupSettingsScreen extends StatefulWidget {
  const BackupSettingsScreen({super.key});

  @override
  State<BackupSettingsScreen> createState() => _BackupSettingsScreenState();
}

class _BackupSettingsScreenState extends State<BackupSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BackupProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text(
            'النسخ الاحتياطي والاستعادة',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Consumer<BackupProvider>(
          builder: (context, backupProvider, child) {
            if (backupProvider.isLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري المعالجة...'),
                  ],
                ),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // رسائل النجاح والخطأ
                  if (backupProvider.successMessage != null)
                    _buildMessageCard(
                      backupProvider.successMessage!,
                      Colors.green,
                      Icons.check_circle,
                    ),
                  
                  if (backupProvider.errorMessage != null)
                    _buildMessageCard(
                      backupProvider.errorMessage!,
                      Colors.red,
                      Icons.error,
                    ),

                  // النسخ الاحتياطي المحلي
                  _buildLocalBackupSection(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // Google Drive
                  _buildGoogleDriveSection(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // النسخ الاحتياطي التلقائي
                  _buildAutoBackupSection(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // النسخ الاحتياطية المحلية
                  _buildLocalBackupsSection(backupProvider),
                  
                  const SizedBox(height: 20),
                  
                  // النسخ الاحتياطية على Google Drive
                  if (backupProvider.isSignedIn)
                    _buildDriveBackupsSection(backupProvider),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMessageCard(String message, Color color, IconData icon) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(color: color.withOpacity(0.8)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              context.read<BackupProvider>().clearMessages();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLocalBackupSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: Colors.blue[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطي المحلي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'إنشاء نسخة احتياطية من قاعدة البيانات على الجهاز المحلي',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => backupProvider.performLocalBackup(),
                    icon: const Icon(Icons.save),
                    label: const Text('إنشاء نسخة احتياطية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleDriveSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: Colors.green[700]),
                const SizedBox(width: 12),
                const Text(
                  'Google Drive',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (!backupProvider.isSignedIn) ...[
              const Text(
                'قم بتسجيل الدخول إلى Google Drive لحفظ النسخ الاحتياطية في السحابة',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => backupProvider.signInGoogle(),
                      icon: const Icon(Icons.login),
                      label: const Text('تسجيل الدخول'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Text(
                'مسجل الدخول: ${backupProvider.currentUser?.email ?? "غير معروف"}',
                style: const TextStyle(color: Colors.green),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => backupProvider.performGoogleDriveBackup(),
                      icon: const Icon(Icons.cloud_upload),
                      label: const Text('رفع نسخة احتياطية'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: () => backupProvider.signOutGoogle(),
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAutoBackupSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.orange[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطي التلقائي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('تفعيل النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخ احتياطية تلقائياً بشكل دوري'),
              value: backupProvider.autoBackupEnabled,
              onChanged: (value) => backupProvider.setAutoBackupEnabled(value),
              activeColor: Colors.orange,
            ),
            
            if (backupProvider.autoBackupEnabled) ...[
              const Divider(),
              ListTile(
                title: const Text('تردد النسخ الاحتياطي'),
                subtitle: Text(backupProvider.autoBackupFrequency.displayName),
                trailing: DropdownButton<BackupFrequency>(
                  value: backupProvider.autoBackupFrequency,
                  onChanged: (frequency) {
                    if (frequency != null) {
                      backupProvider.setAutoBackupFrequency(frequency);
                    }
                  },
                  items: BackupFrequency.values.map((frequency) {
                    return DropdownMenuItem(
                      value: frequency,
                      child: Text(frequency.displayName),
                    );
                  }).toList(),
                ),
              ),
              
              SwitchListTile(
                title: const Text('النسخ الاحتياطي للسحابة'),
                subtitle: const Text('رفع النسخ الاحتياطية التلقائية إلى Google Drive'),
                value: backupProvider.autoBackupToCloud,
                onChanged: backupProvider.isSignedIn 
                    ? (value) => backupProvider.setAutoBackupToCloud(value)
                    : null,
                activeColor: Colors.green,
              ),
              
              if (!backupProvider.isSignedIn && backupProvider.autoBackupToCloud)
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'يجب تسجيل الدخول إلى Google Drive أولاً',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocalBackupsSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.folder, color: Colors.blue[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطية المحلية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => backupProvider.refreshLocalBackups(),
                  tooltip: 'تحديث القائمة',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (backupProvider.localBackups.isEmpty) ...[
              const Center(
                child: Column(
                  children: [
                    Icon(Icons.folder_open, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'لا توجد نسخ احتياطية محلية',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ] else ...[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: backupProvider.localBackups.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final backup = backupProvider.localBackups[index];
                  final info = backupProvider.getBackupInfo(backup);

                  return ListTile(
                    leading: const Icon(Icons.file_copy, color: Colors.blue),
                    title: Text(info['name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الحجم: ${info['sizeFormatted']}'),
                        Text('التاريخ: ${Formatters.formatDateTime(info['lastModified'])}'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (action) => _handleLocalBackupAction(
                        action,
                        backup,
                        backupProvider,
                      ),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'restore',
                          child: ListTile(
                            leading: Icon(Icons.restore, color: Colors.green),
                            title: Text('استعادة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDriveBackupsSection(BackupProvider backupProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud_queue, color: Colors.green[700]),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطية على Google Drive',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => backupProvider.refreshDriveBackups(),
                  tooltip: 'تحديث القائمة',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (backupProvider.driveBackups.isEmpty) ...[
              const Center(
                child: Column(
                  children: [
                    Icon(Icons.cloud_off, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'لا توجد نسخ احتياطية على Google Drive',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ] else ...[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: backupProvider.driveBackups.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final backup = backupProvider.driveBackups[index];
                  final info = backupProvider.getDriveFileInfo(backup);

                  return ListTile(
                    leading: const Icon(Icons.cloud, color: Colors.green),
                    title: Text(info['name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الحجم: ${info['sizeFormatted']}'),
                        if (info['modifiedTime'] != null)
                          Text('التاريخ: ${Formatters.formatDateTime(info['modifiedTime'])}'),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (action) => _handleDriveBackupAction(
                        action,
                        backup,
                        backupProvider,
                      ),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'restore',
                          child: ListTile(
                            leading: Icon(Icons.cloud_download, color: Colors.green),
                            title: Text('استعادة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleLocalBackupAction(String action, File backup, BackupProvider backupProvider) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(() => backupProvider.restoreLocalBackup(backup));
        break;
      case 'delete':
        _showDeleteConfirmation(() => backupProvider.deleteLocalBackup(backup));
        break;
    }
  }

  void _handleDriveBackupAction(String action, drive.File backup, BackupProvider backupProvider) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(() => backupProvider.restoreFromGoogleDrive(backup));
        break;
      case 'delete':
        _showDeleteConfirmation(() => backupProvider.deleteFromGoogleDrive(backup.id!));
        break;
    }
  }

  void _showRestoreConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => EnhancedConfirmationDialog(
        title: 'تأكيد الاستعادة',
        content: 'تحذير: استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية وتستبدلها ببيانات النسخة الاحتياطية.\n\nهل أنت متأكد من المتابعة؟',
        confirmText: 'استعادة',
        cancelText: 'إلغاء',
        isDestructive: true,
        onConfirm: onConfirm,
      ),
    );
  }

  void _showDeleteConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => EnhancedConfirmationDialog(
        title: 'تأكيد الحذف',
        content: 'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟\n\nلا يمكن التراجع عن هذا الإجراء.',
        confirmText: 'حذف',
        cancelText: 'إلغاء',
        isDestructive: true,
        onConfirm: onConfirm,
      ),
    );
  }
}
