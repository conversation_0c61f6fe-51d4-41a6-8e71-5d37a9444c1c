import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../../utils/excel_parser.dart';

class ImportProductsExcelDialog extends StatefulWidget {
  final Function(List<Map<String, dynamic>>) onProductsSelected;

  const ImportProductsExcelDialog({
    super.key,
    required this.onProductsSelected,
  });

  @override
  State<ImportProductsExcelDialog> createState() => _ImportProductsExcelDialogState();
}

class _ImportProductsExcelDialogState extends State<ImportProductsExcelDialog> {
  bool _isLoading = false;
  String? _errorMessage;
  String? _fileName;
  List<String> _headers = <String>[];
  List<Map<String, dynamic>> _products = <Map<String, dynamic>>[];
  Map<String, String> _headerMapping = <String, String>{};

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          height: MediaQuery.of(context).size.height * 0.85,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: <Widget>[
              // العنوان
              Row(
                children: <Widget>[
                  const Icon(Icons.upload_file, color: Colors.blue, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'استيراد المنتجات من إكسل',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              
              const Divider(),
              const SizedBox(height: 16),
              
              // المحتوى الرئيسي
              Expanded(
                child: _buildMainContent(),
              ),
              
              // الأزرار السفلية
              if (_products.isNotEmpty) _buildBottomButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحليل الملف...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return _buildErrorWidget();
    }

    if (_products.isEmpty) {
      return _buildFileSelectionWidget();
    }

    return _buildDataPreviewWidget();
  }

  Widget _buildFileSelectionWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.cloud_upload_outlined,
            size: 80,
            color: Colors.blue,
          ),
          const SizedBox(height: 24),
          const Text(
            'اختر ملف إكسل لاستيراد المنتجات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'الملفات المدعومة: Excel (.xlsx, .xls) أو CSV (.csv)',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          
          // زر اختيار الملف
          ElevatedButton.icon(
            onPressed: _pickFile,
            icon: const Icon(Icons.folder_open),
            label: const Text('اختيار ملف إكسل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // زر تحميل ملف نموذجي
          OutlinedButton.icon(
            onPressed: _downloadSampleFile,
            icon: const Icon(Icons.download),
            label: const Text('تحميل ملف نموذجي'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          
          const SizedBox(height: 32),
          
          // تعليمات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'تعليمات مهمة:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  '• يجب أن يحتوي الملف على رؤوس أعمدة في الصف الأول\n'
                  '• الأعمدة المطلوبة: الاسم، الوحدة، الأسعار، الكميات\n'
                  '• سيتم تحديث المنتجات الموجودة أو إضافة منتجات جديدة\n'
                  '• تأكد من صحة البيانات قبل الاستيراد',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحليل الملف',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Text(
              _errorMessage ?? 'خطأ غير معروف',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              OutlinedButton(
                onPressed: _resetDialog,
                child: const Text('اختيار ملف آخر'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataPreviewWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // معلومات الملف
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Row(
            children: <Widget>[
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'تم تحليل الملف بنجاح: $_fileName',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'عدد المنتجات: ${_products.length}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: _resetDialog,
                child: const Text('اختيار ملف آخر'),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // معاينة البيانات
        const Text(
          'معاينة البيانات (أول 10 منتجات):',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // جدول البيانات
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  headingRowColor: WidgetStateProperty.all(Colors.blue.shade50),
                  border: TableBorder.all(color: Colors.grey.shade300),
                  columns: _headers.map((String header) {
                    return DataColumn(
                      label: Text(
                        header,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }).toList(),
                  rows: _products.take(10).map((Map<String, dynamic> product) {
                    return DataRow(
                      cells: _headers.map((String header) {
                        final value = product[header];
                        return DataCell(
                          Text(
                            value?.toString() ?? '',
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }).toList(),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
        
        if (_products.length > 10)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'وسيتم استيراد ${_products.length - 10} منتج إضافي...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        children: <Widget>[
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _confirmImport,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                'تأكيد الاستيراد (${_products.length})',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickFile() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: <String>['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final PlatformFile file = result.files.first;
        await _parseFile(file);
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'خطأ في اختيار الملف: $e';
      });
    }
  }

  Future<void> _parseFile(PlatformFile file) async {
    try {
      final Map<String, dynamic> result = await ExcelParser.parseExcelFile(file);

      if (result['success'] == true) {
        final List<String> headers = result['headers'] as List<String>;
        final List<Map<String, dynamic>> products = result['products'] as List<Map<String, dynamic>>;

        // التحقق من وجود رؤوس أعمدة مطلوبة
        final Map<String, String> mapping = ExcelParser.validateHeaders(headers);

        if (!mapping.containsKey('name')) {
          throw Exception('لم يتم العثور على عمود الاسم. تأكد من وجود عمود "الاسم" أو "name"');
        }

        setState(() {
          _fileName = file.name;
          _headers = headers;
          _products = products;
          _headerMapping = mapping;
          _isLoading = false;
          _errorMessage = null;
        });
      } else {
        throw Exception('فشل في تحليل الملف');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  void _confirmImport() {
    if (_products.isNotEmpty) {
      // تحويل البيانات إلى تنسيق مناسب للاستيراد
      final List<Map<String, dynamic>> processedProducts = <Map<String, dynamic>>[];

      for (final Map<String, dynamic> product in _products) {
        final Map<String, dynamic> processedProduct = <String, dynamic>{};

        // تعيين الحقول بناءً على التطابق
        for (final MapEntry<String, String> entry in _headerMapping.entries) {
          final String fieldName = entry.key;
          final String headerName = entry.value;

          if (product.containsKey(headerName)) {
            processedProduct[fieldName] = product[headerName];
          }
        }

        // إضافة الحقول المباشرة
        for (final MapEntry<String, dynamic> entry in product.entries) {
          if (!_headerMapping.containsValue(entry.key)) {
            processedProduct[entry.key] = entry.value;
          }
        }

        processedProducts.add(processedProduct);
      }

      widget.onProductsSelected(processedProducts);
      Navigator.pop(context);
    }
  }

  void _resetDialog() {
    setState(() {
      _isLoading = false;
      _errorMessage = null;
      _fileName = null;
      _headers.clear();
      _products.clear();
      _headerMapping.clear();
    });
  }

  Future<void> _downloadSampleFile() async {
    try {
      // إنشاء ملف نموذجي
      final Uint8List bytes = await ExcelParser.createSampleExcelFile();

      // هنا يمكن إضافة منطق لحفظ الملف أو مشاركته
      // للبساطة، سنعرض رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء الملف النموذجي. يمكنك إنشاء ملف مشابه بنفس التنسيق.'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء الملف النموذجي: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
