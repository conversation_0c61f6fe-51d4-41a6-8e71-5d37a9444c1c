import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:inventory_management_app/services/reports_service.dart';
import 'package:inventory_management_app/widgets/custom_app_bar.dart';
import 'package:inventory_management_app/widgets/empty_state_widget.dart'
    as widgets;
import 'package:intl/intl.dart';

/// Screen for displaying various reports and analytics
class ReportsScreen extends StatefulWidget {
  /// Constructor for ReportsScreen
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ReportsService _reportsService = ReportsService();

  bool _isLoading = false;
  Map<String, dynamic>? _dashboardData;
  Map<String, dynamic>? _salesData;
  Map<String, dynamic>? _inventoryData;
  Map<String, dynamic>? _financialData;

  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDashboardData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final Map<String, dynamic> dashboard =
          await _reportsService.getDashboardSummary();
      final Map<String, dynamic> sales = await _reportsService.getSalesReport(
        startDate: _startDate,
        endDate: _endDate,
      );
      final Map<String, dynamic> inventory =
          await _reportsService.getInventoryReport();
      final Map<String, dynamic> financial =
          await _reportsService.getFinancialReport(
        startDate: _startDate,
        endDate: _endDate,
      );

      setState(() {
        _dashboardData = dashboard;
        _salesData = sales;
        _inventoryData = inventory;
        _financialData = financial;
      });
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to load reports: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      await _loadDashboardData();
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Reports & Analytics',
        showBackButton: true,
        actions: <Widget>[
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'Select Date Range',
          ),
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const widgets.LoadingWidget(message: 'Loading reports...')
          : Column(
              children: <Widget>[
                _buildDateRangeHeader(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: <Widget>[
                      _buildDashboardTab(),
                      _buildSalesTab(),
                      _buildInventoryTab(),
                      _buildFinancialTab(),
                    ],
                  ),
                ),
              ],
            ),
      bottomNavigationBar: TabBar(
        controller: _tabController,
        tabs: const <Tab>[
          Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
          Tab(icon: Icon(Icons.trending_up), text: 'Sales'),
          Tab(icon: Icon(Icons.inventory), text: 'Inventory'),
          Tab(icon: Icon(Icons.account_balance), text: 'Financial'),
        ],
      ),
    );
  }

  Widget _buildDateRangeHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      child: Row(
        children: <Widget>[
          Icon(Icons.calendar_today, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          Text(
            'Period: ${DateFormat('MMM dd, yyyy').format(_startDate)} - ${DateFormat('MMM dd, yyyy').format(_endDate)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.edit),
            label: const Text('Change'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    if (_dashboardData == null) {
      return const widgets.EmptyStateWidget(
        title: 'No Data',
        message: 'Dashboard data is not available',
        icon: Icons.dashboard,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Overview',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildOverviewCards(),
          const SizedBox(height: 24),
          Text(
            'Financial Summary',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildFinancialSummaryChart(),
        ],
      ),
    );
  }

  Widget _buildOverviewCards() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: <Widget>[
        _buildStatCard(
          'Products',
          '${_dashboardData!['totalProducts']}',
          Icons.inventory,
          Colors.blue,
        ),
        _buildStatCard(
          'Customers',
          '${_dashboardData!['totalCustomers']}',
          Icons.people,
          Colors.green,
        ),
        _buildStatCard(
          'Suppliers',
          '${_dashboardData!['totalSuppliers']}',
          Icons.business,
          Colors.orange,
        ),
        _buildStatCard(
          'Low Stock',
          '${_dashboardData!['lowStockProducts']}',
          Icons.warning,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryChart() {
    final double totalSales = (_dashboardData!['totalSales'] as double?) ?? 0.0;
    final double totalPurchases =
        (_dashboardData!['totalPurchases'] as double?) ?? 0.0;
    final double totalExpenses =
        (_dashboardData!['totalExpenses'] as double?) ?? 0.0;
    final double profit = (_dashboardData!['profit'] as double?) ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: <Widget>[
            Text(
              'Financial Overview (Last 30 Days)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: <double>[
                        totalSales,
                        totalPurchases,
                        totalExpenses,
                        profit.abs()
                      ].reduce((double a, double b) => a > b ? a : b) *
                      1.2,
                  barTouchData: BarTouchData(enabled: false),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          switch (value.toInt()) {
                            case 0:
                              return const Text('Sales');
                            case 1:
                              return const Text('Purchases');
                            case 2:
                              return const Text('Expenses');
                            case 3:
                              return const Text('Profit');
                            default:
                              return const Text('');
                          }
                        },
                      ),
                    ),
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: <BarChartGroupData>[
                    BarChartGroupData(
                      x: 0,
                      barRods: <BarChartRodData>[
                        BarChartRodData(
                          toY: totalSales,
                          color: Colors.green,
                          width: 20,
                        ),
                      ],
                    ),
                    BarChartGroupData(
                      x: 1,
                      barRods: <BarChartRodData>[
                        BarChartRodData(
                          toY: totalPurchases,
                          color: Colors.orange,
                          width: 20,
                        ),
                      ],
                    ),
                    BarChartGroupData(
                      x: 2,
                      barRods: <BarChartRodData>[
                        BarChartRodData(
                          toY: totalExpenses,
                          color: Colors.red,
                          width: 20,
                        ),
                      ],
                    ),
                    BarChartGroupData(
                      x: 3,
                      barRods: <BarChartRodData>[
                        BarChartRodData(
                          toY: profit.abs(),
                          color: profit >= 0 ? Colors.blue : Colors.red,
                          width: 20,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                _buildFinancialSummaryItem('Sales', totalSales, Colors.green),
                _buildFinancialSummaryItem(
                    'Purchases', totalPurchases, Colors.orange),
                _buildFinancialSummaryItem(
                    'Expenses', totalExpenses, Colors.red),
                _buildFinancialSummaryItem(
                    'Profit', profit, profit >= 0 ? Colors.blue : Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryItem(String label, double value, Color color) {
    return Column(
      children: <Widget>[
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        Text(
          '\$${value.toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  Widget _buildSalesTab() {
    if (_salesData == null) {
      return const widgets.EmptyStateWidget(
        title: 'No Sales Data',
        message: 'Sales data is not available for the selected period',
        icon: Icons.trending_up,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildSalesSummaryCards(),
          const SizedBox(height: 24),
          _buildTopProductsSection(),
        ],
      ),
    );
  }

  Widget _buildSalesSummaryCards() {
    final Map<String, dynamic> summary =
        _salesData!['summary'] as Map<String, dynamic>;

    return Row(
      children: <Widget>[
        Expanded(
          child: _buildSummaryCard(
            'Total Sales',
            '\$${(summary['totalSales'] as double).toStringAsFixed(2)}',
            Icons.attach_money,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'Total Orders',
            '${summary['totalOrders']}',
            Icons.shopping_cart,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: <Widget>[
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProductsSection() {
    final List<dynamic> topProducts =
        _salesData!['topProducts'] as List<dynamic>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Top Selling Products',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (topProducts.isEmpty)
              const Text('No sales data available')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: topProducts.length,
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemBuilder: (BuildContext context, int index) {
                  final Map<String, dynamic> product =
                      topProducts[index] as Map<String, dynamic>;
                  return ListTile(
                    leading: CircleAvatar(
                      child: Text('${index + 1}'),
                    ),
                    title: Text(product['name'] as String? ?? 'Unknown'),
                    subtitle:
                        Text('Quantity: ${product['quantity_sold'] ?? 0}'),
                    trailing: Text(
                      '\$${((product['total_sales'] as double?) ?? 0.0).toStringAsFixed(2)}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryTab() {
    if (_inventoryData == null) {
      return const widgets.EmptyStateWidget(
        title: 'No Inventory Data',
        message: 'Inventory data is not available',
        icon: Icons.inventory,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildInventorySummary(),
          const SizedBox(height: 24),
          _buildLowStockSection(),
        ],
      ),
    );
  }

  Widget _buildInventorySummary() {
    final Map<String, dynamic> summary =
        _inventoryData!['summary'] as Map<String, dynamic>;

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: <Widget>[
        _buildStatCard(
          'Total Products',
          '${summary['totalProducts']}',
          Icons.inventory,
          Colors.blue,
        ),
        _buildStatCard(
          'Stock Value',
          '\$${(summary['totalStockValue'] as double).toStringAsFixed(0)}',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatCard(
          'Low Stock',
          '${summary['lowStockCount']}',
          Icons.warning,
          Colors.orange,
        ),
        _buildStatCard(
          'Out of Stock',
          '${summary['outOfStockCount']}',
          Icons.error,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildLowStockSection() {
    final List<dynamic> lowStockProducts =
        _inventoryData!['lowStockProducts'] as List<dynamic>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Low Stock Products',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (lowStockProducts.isEmpty)
              const Text('No low stock products')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: lowStockProducts.length,
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemBuilder: (BuildContext context, int index) {
                  final Map<String, dynamic> product =
                      lowStockProducts[index] as Map<String, dynamic>;
                  final double quantity =
                      (product['quantity'] as double?) ?? 0.0;

                  return ListTile(
                    leading: Icon(
                      Icons.warning,
                      color: quantity <= 0 ? Colors.red : Colors.orange,
                    ),
                    title: Text(product['name'] as String? ?? 'Unknown'),
                    subtitle:
                        Text('Category: ${product['category_name'] ?? 'N/A'}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        Text(
                          quantity.toStringAsFixed(0),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: quantity <= 0 ? Colors.red : Colors.orange,
                          ),
                        ),
                        Text(
                          product['unit_name'] as String? ?? 'units',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialTab() {
    if (_financialData == null) {
      return const widgets.EmptyStateWidget(
        title: 'No Financial Data',
        message: 'Financial data is not available for the selected period',
        icon: Icons.account_balance,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildFinancialSummary(),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    final Map<String, dynamic> summary =
        _financialData!['summary'] as Map<String, dynamic>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Financial Summary',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildFinancialRow(
                'Total Sales', summary['totalSales'] as double, Colors.green),
            _buildFinancialRow('Total Purchases',
                summary['totalPurchases'] as double, Colors.orange),
            _buildFinancialRow('Total Expenses',
                summary['totalExpenses'] as double, Colors.red),
            const Divider(),
            _buildFinancialRow(
                'Gross Profit', summary['grossProfit'] as double, Colors.blue),
            _buildFinancialRow(
                'Net Profit',
                summary['netProfit'] as double,
                (summary['netProfit'] as double) >= 0
                    ? Colors.green
                    : Colors.red),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text('Profit Margin:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Text(
                  '${(summary['profitMargin'] as double).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: (summary['profitMargin'] as double) >= 0
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialRow(String label, double value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(label),
          Text(
            '\$${value.toStringAsFixed(2)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
