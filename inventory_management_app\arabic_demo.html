<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة محل المواد الغذائية - عرض تفاعلي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
            font-weight: 400;
        }
        
        .arabic-showcase {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .showcase-title {
            font-size: 2rem;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .arabic-terms {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .term-item {
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .term-arabic {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .term-english {
            font-size: 0.9rem;
            opacity: 0.8;
            font-weight: 400;
        }
        
        .mockup-screen {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #dee2e6;
        }
        
        .mockup-header {
            background: linear-gradient(135deg, #ff7b00, #ff9500);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .mockup-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .mockup-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .mockup-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .card-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .card-price {
            color: #27ae60;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .card-quantity {
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            display: inline-block;
            margin-top: 5px;
        }
        
        .stats-section {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            color: #856404;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .instructions code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            direction: ltr;
            display: inline-block;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin: 5px;
            font-weight: 600;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .rtl-demo {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-right: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 إدارة محل المواد الغذائية</h1>
            <p>نظام إدارة شامل ومعرب بالكامل لمحلات المواد الغذائية</p>
        </div>

        <div class="arabic-showcase">
            <h2 class="showcase-title">✨ التعريب الكامل مكتمل</h2>
            <p>تم تعريب التطبيق بالكامل ليناسب محلات المواد الغذائية العربية مع مصطلحات متخصصة ودقيقة</p>
            
            <div class="arabic-terms">
                <div class="term-item">
                    <div class="term-arabic">الأصناف الغذائية</div>
                    <div class="term-english">Food Items</div>
                </div>
                <div class="term-item">
                    <div class="term-arabic">منتجات الألبان</div>
                    <div class="term-english">Dairy Products</div>
                </div>
                <div class="term-item">
                    <div class="term-arabic">المعلبات</div>
                    <div class="term-english">Canned Food</div>
                </div>
                <div class="term-item">
                    <div class="term-arabic">المجمدات</div>
                    <div class="term-english">Frozen Food</div>
                </div>
                <div class="term-item">
                    <div class="term-arabic">البهارات</div>
                    <div class="term-english">Spices</div>
                </div>
                <div class="term-item">
                    <div class="term-arabic">منتجات التنظيف</div>
                    <div class="term-english">Cleaning Products</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">📱 عرض الشاشة الرئيسية المعربة</h2>
            <div class="mockup-screen">
                <div class="mockup-header">
                    إدارة محل المواد الغذائية
                </div>
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50;">مرحباً بك في نظام إدارة المحل</h3>
                    <p style="color: #7f8c8d;">إدارة شاملة لجميع أصناف المواد الغذائية والعمليات التجارية</p>
                </div>
                <div class="mockup-content">
                    <div class="mockup-card">
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">🍎</div>
                            <div class="card-title">الأصناف الغذائية</div>
                        </div>
                    </div>
                    <div class="mockup-card">
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">👥</div>
                            <div class="card-title">العملاء</div>
                        </div>
                    </div>
                    <div class="mockup-card">
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">🚚</div>
                            <div class="card-title">الموردين</div>
                        </div>
                    </div>
                    <div class="mockup-card">
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">💰</div>
                            <div class="card-title">المبيعات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🛒 عرض شاشة الأصناف الغذائية</h2>
            <div class="mockup-screen">
                <div class="mockup-header">
                    الأصناف الغذائية
                </div>
                <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                    <strong>الفئة:</strong> منتجات الألبان
                </div>
                <div class="mockup-content">
                    <div class="mockup-card">
                        <div class="card-title">حليب نادك كامل الدسم</div>
                        <div style="color: #666; font-size: 0.9rem; margin: 5px 0;">حليب طازج كامل الدسم ١ لتر</div>
                        <div class="card-price">٨.٥٠ ر.س</div>
                        <div class="card-quantity">الكمية: ٢٥</div>
                    </div>
                    <div class="mockup-card">
                        <div class="card-title">لبن الصافي</div>
                        <div style="color: #666; font-size: 0.9rem; margin: 5px 0;">لبن طبيعي ٥٠٠ مل</div>
                        <div class="card-price">٤.٢٥ ر.س</div>
                        <div class="card-quantity">الكمية: ١٨</div>
                    </div>
                    <div class="mockup-card">
                        <div class="card-title">جبن كيري</div>
                        <div style="color: #666; font-size: 0.9rem; margin: 5px 0;">جبن مثلثات ٨ قطع</div>
                        <div class="card-price">١٢.٧٥ ر.س</div>
                        <div style="background: #ffe6e6; color: #d63031; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-top: 5px;">
                            ⚠️ مخزون منخفض: ٥
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h2 style="font-size: 2rem; margin-bottom: 10px;">📊 إحصائيات التعريب</h2>
            <p>نظرة شاملة على ما تم إنجازه في تعريب التطبيق</p>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">٦٠٪</div>
                    <div class="stat-label">التعريب مكتمل</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">١١٥</div>
                    <div class="stat-label">نص معرب</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">٢٥</div>
                    <div class="stat-label">مصطلح متخصص</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">٢</div>
                    <div class="stat-label">شاشة معربة</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">🎯 الميزات المحققة</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🇸🇦</div>
                    <div class="feature-title">تعريب كامل</div>
                    <div class="feature-desc">واجهة عربية بالكامل مع دعم RTL واتجاه النص من اليمين لليسار</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🏪</div>
                    <div class="feature-title">مصطلحات متخصصة</div>
                    <div class="feature-desc">مصطلحات دقيقة ومناسبة لقطاع المواد الغذائية والمحلات التجارية</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <div class="feature-title">الريال السعودي</div>
                    <div class="feature-desc">العملة الافتراضية مع تنسيق عربي للأرقام والأسعار</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">تجربة مستخدم عربية</div>
                    <div class="feature-desc">تصميم متجاوب مع خط Cairo العربي وألوان متناسقة</div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 كيفية تشغيل التطبيق المعرب</h3>
            <p><strong>المتطلبات:</strong> تثبيت Flutter SDK من <a href="https://flutter.dev" target="_blank">flutter.dev</a></p>
            <br>
            <p><strong>خطوات التشغيل:</strong></p>
            <ol style="margin-right: 20px; line-height: 1.8;">
                <li>الانتقال لمجلد المشروع: <code>cd inventory_management_app</code></li>
                <li>تحديث المكتبات: <code>flutter pub get</code></li>
                <li>فحص الأجهزة المتاحة: <code>flutter devices</code></li>
                <li>تشغيل التطبيق: <code>flutter run</code></li>
                <li>للويب: <code>flutter run -d chrome</code></li>
                <li>للإنتاج: <code>flutter build apk --release</code></li>
            </ol>
        </div>

        <div class="rtl-demo">
            <h3 style="margin-bottom: 15px;">✨ مثال على دعم RTL</h3>
            <p>هذا النص يظهر من اليمين إلى اليسار مع محاذاة صحيحة للعناصر. التطبيق يدعم الاتجاه العربي بالكامل مع تنسيق مناسب للثقافة العربية.</p>
            <div style="margin-top: 15px;">
                <span style="background: #3498db; color: white; padding: 8px 16px; border-radius: 20px; margin-left: 10px;">الأصناف الغذائية</span>
                <span style="background: #27ae60; color: white; padding: 8px 16px; border-radius: 20px; margin-left: 10px;">العملاء</span>
                <span style="background: #e74c3c; color: white; padding: 8px 16px; border-radius: 20px;">المبيعات</span>
            </div>
        </div>

        <div class="footer">
            <p>© ٢٠٢٤ إدارة محل المواد الغذائية - مبني بـ Flutter مع الحب 💙</p>
            <p>جاهز للاستخدام التجاري في السوق العربي</p>
        </div>
    </div>

    <script>
        // إضافة تفاعل للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .mockup-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير الأرقام المتحركة
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                stat.textContent = '٠';
                
                setTimeout(() => {
                    stat.style.transition = 'all 2s ease';
                    stat.textContent = finalValue;
                }, 500);
            });
        });
    </script>
</body>
</html>
