import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_constants.dart';
import '../../widgets/enhanced_dashboard_widgets.dart';
import '../../widgets/custom_buttons.dart';
import '../../providers/product_provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/purchase_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/supplier_provider.dart';

/// الشاشة الرئيسية العصرية مع تصميم محسن
class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({super.key});

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: CustomScrollView(
          slivers: [
            // شريط التطبيق المرن
            _buildSliverAppBar(),
            
            // المحتوى الرئيسي
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بطاقات الإحصائيات
                    _buildStatsSection(),
                    
                    const SizedBox(height: AppDimensions.paddingL),
                    
                    // الإجراءات السريعة
                    _buildQuickActionsSection(),
                    
                    const SizedBox(height: AppDimensions.paddingL),
                    
                    // التنبيهات
                    _buildAlertsSection(),
                    
                    const SizedBox(height: AppDimensions.paddingL),
                    
                    // النشاط الأخير
                    _buildRecentActivitySection(),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        // الزر العائم
        floatingActionButton: CustomFloatingActionButton(
          onPressed: () => _showQuickAddMenu(context),
          icon: Icons.add,
          tooltip: 'إضافة سريعة',
        ),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'لوحة القيادة',
          style: AppStyles.titleLarge.copyWith(
            color: AppColors.textOnPrimary,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
          child: Stack(
            children: [
              // خلفية زخرفية
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
              ),
              
              // معلومات المستخدم
              Positioned(
                bottom: 60,
                right: 20,
                left: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً بك',
                      style: AppStyles.headlineSmall.copyWith(
                        color: AppColors.textOnPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      AppConstants.appDescription,
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // TODO: إضافة شاشة الإشعارات
          },
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            // TODO: إضافة البحث العام
          },
        ),
      ],
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات',
          style: AppStyles.titleLarge,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Consumer5<ProductProvider, SaleProvider, PurchaseProvider,
            CustomerProvider, SupplierProvider>(
          builder: (context, productProvider, saleProvider, purchaseProvider,
              customerProvider, supplierProvider, child) {
            return GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.1,
              crossAxisSpacing: AppDimensions.paddingM,
              mainAxisSpacing: AppDimensions.paddingM,
              children: [
                EnhancedStatCard(
                  title: 'إجمالي المنتجات',
                  value: '${productProvider.products.length}',
                  icon: Icons.inventory_2,
                  color: AppColors.primary,
                  subtitle: 'منتج متاح',
                  showTrend: true,
                  trendValue: 5.2,
                  isPositiveTrend: true,
                  onTap: () => context.go('/products'),
                ),
                EnhancedStatCard(
                  title: 'إجمالي العملاء',
                  value: '${customerProvider.customers.length}',
                  icon: Icons.people,
                  color: AppColors.accent,
                  subtitle: 'عميل نشط',
                  showTrend: true,
                  trendValue: 2.1,
                  isPositiveTrend: true,
                  onTap: () => context.go('/customers'),
                ),
                EnhancedStatCard(
                  title: 'المبيعات اليوم',
                  value: '${saleProvider.sales.length}',
                  icon: Icons.point_of_sale,
                  color: AppColors.success,
                  subtitle: 'عملية بيع',
                  showTrend: true,
                  trendValue: 12.5,
                  isPositiveTrend: true,
                  onTap: () => context.go('/sales'),
                ),
                EnhancedStatCard(
                  title: 'الموردين',
                  value: '${supplierProvider.suppliers.length}',
                  icon: Icons.local_shipping,
                  color: AppColors.info,
                  subtitle: 'مورد متاح',
                  onTap: () => context.go('/suppliers'),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الإجراءات السريعة',
              style: AppStyles.titleLarge,
            ),
            CustomTextButton(
              text: 'عرض الكل',
              onPressed: () {
                // TODO: عرض جميع الإجراءات
              },
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingM),
        GridView.count(
          crossAxisCount: 3,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 0.9,
          crossAxisSpacing: AppDimensions.paddingS,
          mainAxisSpacing: AppDimensions.paddingS,
          children: [
            EnhancedQuickActionCard(
              title: 'إضافة منتج',
              subtitle: 'منتج جديد',
              icon: Icons.add_box,
              color: AppColors.primary,
              onTap: () => context.go('/products/add'),
            ),
            EnhancedQuickActionCard(
              title: 'فاتورة بيع',
              subtitle: 'بيع جديد',
              icon: Icons.receipt_long,
              color: AppColors.success,
              onTap: () => context.go('/sales/add'),
            ),
            EnhancedQuickActionCard(
              title: 'فاتورة شراء',
              subtitle: 'شراء جديد',
              icon: Icons.shopping_cart,
              color: AppColors.accent,
              onTap: () => context.go('/purchases/add'),
            ),
            EnhancedQuickActionCard(
              title: 'عميل جديد',
              subtitle: 'إضافة عميل',
              icon: Icons.person_add,
              color: AppColors.info,
              onTap: () => context.go('/customers/add'),
            ),
            EnhancedQuickActionCard(
              title: 'التقارير',
              subtitle: 'عرض التقارير',
              icon: Icons.analytics,
              color: AppColors.secondary,
              onTap: () => context.go('/reports'),
            ),
            EnhancedQuickActionCard(
              title: 'الإعدادات',
              subtitle: 'إعدادات التطبيق',
              icon: Icons.settings,
              color: AppColors.textSecondary,
              onTap: () => context.go('/settings'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAlertsSection() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final lowStockProducts = productProvider.products
            .where((product) => (product.quantity ?? 0) < 10)
            .toList();

        if (lowStockProducts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التنبيهات',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            EnhancedAlertCard(
              title: 'مخزون منخفض',
              message: 'يوجد ${lowStockProducts.length} منتج بحاجة إعادة تخزين',
              icon: Icons.warning,
              color: AppColors.warning,
              onTap: () => context.go('/products?filter=low_stock'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النشاط الأخير',
              style: AppStyles.titleLarge,
            ),
            CustomTextButton(
              text: 'عرض الكل',
              onPressed: () {
                // TODO: عرض جميع الأنشطة
              },
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Column(
          children: [
            EnhancedActivityCard(
              title: 'تم إضافة منتج جديد',
              subtitle: 'أرز بسمتي - 5 كيلو',
              time: 'منذ ساعتين',
              icon: Icons.add_box,
              color: AppColors.primary,
            ),
            EnhancedActivityCard(
              title: 'عملية بيع جديدة',
              subtitle: 'فاتورة رقم #1234 - 150 ر.س',
              time: 'منذ 3 ساعات',
              icon: Icons.point_of_sale,
              color: AppColors.success,
            ),
            EnhancedActivityCard(
              title: 'تم إضافة عميل جديد',
              subtitle: 'أحمد محمد علي',
              time: 'منذ 5 ساعات',
              icon: Icons.person_add,
              color: AppColors.info,
            ),
          ],
        ),
      ],
    );
  }

  void _showQuickAddMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إضافة سريعة',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.2,
              children: [
                _buildQuickAddItem(
                  'منتج',
                  Icons.add_box,
                  AppColors.primary,
                  () => context.go('/products/add'),
                ),
                _buildQuickAddItem(
                  'فاتورة بيع',
                  Icons.receipt_long,
                  AppColors.success,
                  () => context.go('/sales/add'),
                ),
                _buildQuickAddItem(
                  'فاتورة شراء',
                  Icons.shopping_cart,
                  AppColors.accent,
                  () => context.go('/purchases/add'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAddItem(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              icon,
              color: color,
              size: AppDimensions.iconL,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            title,
            style: AppStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
