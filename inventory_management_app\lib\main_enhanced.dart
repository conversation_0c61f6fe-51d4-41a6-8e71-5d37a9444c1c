import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/core/enhanced_app_router.dart';

/// Enhanced Main App with full features
/// 
/// This version includes:
/// - Full SQLite database integration
/// - Advanced routing with go_router
/// - Enhanced navigation and deep linking
/// - Complete CRUD operations with database persistence
/// - Advanced UI components and features
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SQLite for web
  if (kIsWeb) {
    databaseFactory = databaseFactoryFfiWeb;
  } else {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize database
  await DatabaseService.instance.database;

  runApp(const EnhancedInventoryApp());
}

class EnhancedInventoryApp extends StatelessWidget {
  const EnhancedInventoryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => SaleProvider()),
      ],
      child: MaterialApp.router(
        title: 'نظام إدارة المخزون المحسن',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.light,
          ),
          appBarTheme: const AppBarTheme(
            centerTitle: true,
            elevation: 0,
            scrolledUnderElevation: 0,
          ),
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ),
        routerConfig: EnhancedAppRouter.router,
      ),
    );
  }
}

/// Features comparison between Simple and Enhanced versions:
/// 
/// SIMPLE VERSION (main.dart):
/// ✅ Basic UI with BottomNavigationBar
/// ✅ In-memory data storage (Provider only)
/// ✅ Basic CRUD operations
/// ✅ Simple navigation
/// ✅ Arabic RTL support
/// ✅ Search and filter functionality
/// ✅ Basic forms and validation
/// 
/// ENHANCED VERSION (main_enhanced.dart):
/// ✅ All Simple version features PLUS:
/// ✅ Full SQLite database integration
/// ✅ Advanced routing with go_router
/// ✅ Deep linking support
/// ✅ URL-based navigation
/// ✅ Enhanced UI components
/// ✅ Material 3 design system
/// ✅ Advanced theming
/// ✅ Detailed screens for each entity
/// ✅ Better error handling
/// ✅ Database persistence
/// ✅ Professional navigation structure
/// 
/// TO USE ENHANCED VERSION:
/// 1. Rename main.dart to main_simple.dart
/// 2. Rename main_enhanced.dart to main.dart
/// 3. Run: flutter pub get
/// 4. Run: flutter run -d chrome
/// 
/// FEATURES RESTORED:
/// 🔄 Database Integration: Full SQLite with persistence
/// 🔄 Advanced Routing: go_router with deep linking
/// 🔄 Enhanced Navigation: URL-based routing
/// 🔄 Detailed Screens: Dedicated CRUD screens
/// 🔄 Material 3: Modern design system
/// 🔄 Professional Structure: Scalable architecture
/// 
/// ADDITIONAL FEATURES TO ADD:
/// 🚀 Theme Provider: Dark/Light mode switching
/// 🚀 Settings Persistence: SharedPreferences integration
/// 🚀 Advanced Reports: Charts and analytics
/// 🚀 Export/Import: Data backup and restore
/// 🚀 Notifications: Local notifications system
/// 🚀 Multi-language: Internationalization support
/// 🚀 Offline Support: Sync when online
/// 🚀 Print Support: Invoice printing
/// 🚀 Barcode Scanner: Product scanning
/// 🚀 Cloud Sync: Firebase integration
