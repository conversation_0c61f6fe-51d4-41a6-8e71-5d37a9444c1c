import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../config/app_constants.dart';

/// فئة مساعدة لإدارة الإعدادات
class SettingsHelper {
  static SharedPreferences? _prefs;

  /// تهيئة الإعدادات
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// الحصول على مثيل SharedPreferences
  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('يجب استدعاء SettingsHelper.init() أولاً');
    }
    return _prefs!;
  }

  // مفاتيح الإعدادات
  static const String _themeMode = 'theme_mode';
  static const String _language = 'language';
  static const String _currency = 'currency';
  static const String _dateFormat = 'date_format';
  static const String _timeFormat = 'time_format';
  static const String _lowStockThreshold = 'low_stock_threshold';
  static const String _autoBackup = 'auto_backup';
  static const String _backupFrequency = 'backup_frequency';
  static const String _showNotifications = 'show_notifications';
  static const String _soundEnabled = 'sound_enabled';
  static const String _vibrationEnabled = 'vibration_enabled';
  static const String _companyName = 'company_name';
  static const String _companyAddress = 'company_address';
  static const String _companyPhone = 'company_phone';
  static const String _companyEmail = 'company_email';
  static const String _taxNumber = 'tax_number';
  static const String _commercialRegister = 'commercial_register';
  static const String _defaultPaymentMethod = 'default_payment_method';
  static const String _printAfterSale = 'print_after_sale';
  static const String _showCustomerInInvoice = 'show_customer_in_invoice';
  static const String _roundPrices = 'round_prices';
  static const String _lastBackupDate = 'last_backup_date';
  static const String _firstRun = 'first_run';
  static const String _appVersion = 'app_version';

  /// إعدادات المظهر
  static ThemeMode getThemeMode() {
    final value = prefs.getString(_themeMode) ?? 'system';
    switch (value) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      default:
        return ThemeMode.system;
    }
  }

  static Future<void> setThemeMode(ThemeMode mode) async {
    String value;
    switch (mode) {
      case ThemeMode.light:
        value = 'light';
        break;
      case ThemeMode.dark:
        value = 'dark';
        break;
      default:
        value = 'system';
    }
    await prefs.setString(_themeMode, value);
  }

  /// إعدادات اللغة
  static String getLanguage() {
    return prefs.getString(_language) ?? 'ar';
  }

  static Future<void> setLanguage(String language) async {
    await prefs.setString(_language, language);
  }

  /// إعدادات العملة
  static String getCurrency() {
    return prefs.getString(_currency) ?? AppConstants.defaultCurrency;
  }

  static Future<void> setCurrency(String currency) async {
    await prefs.setString(_currency, currency);
  }

  /// إعدادات التاريخ والوقت
  static String getDateFormat() {
    return prefs.getString(_dateFormat) ?? AppConstants.displayDateFormat;
  }

  static Future<void> setDateFormat(String format) async {
    await prefs.setString(_dateFormat, format);
  }

  static String getTimeFormat() {
    return prefs.getString(_timeFormat) ?? AppConstants.timeFormat;
  }

  static Future<void> setTimeFormat(String format) async {
    await prefs.setString(_timeFormat, format);
  }

  /// إعدادات المخزون
  static int getLowStockThreshold() {
    return prefs.getInt(_lowStockThreshold) ??
        AppConstants.defaultMinStockLevel;
  }

  static Future<void> setLowStockThreshold(int threshold) async {
    await prefs.setInt(_lowStockThreshold, threshold);
  }

  /// إعدادات النسخ الاحتياطي
  static bool getAutoBackup() {
    return prefs.getBool(_autoBackup) ?? false;
  }

  static Future<void> setAutoBackup(bool enabled) async {
    await prefs.setBool(_autoBackup, enabled);
  }

  static String getBackupFrequency() {
    return prefs.getString(_backupFrequency) ?? 'weekly';
  }

  static Future<void> setBackupFrequency(String frequency) async {
    await prefs.setString(_backupFrequency, frequency);
  }

  static DateTime? getLastBackupDate() {
    final timestamp = prefs.getInt(_lastBackupDate);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  static Future<void> setLastBackupDate(DateTime date) async {
    await prefs.setInt(_lastBackupDate, date.millisecondsSinceEpoch);
  }

  /// إعدادات الإشعارات
  static bool getShowNotifications() {
    return prefs.getBool(_showNotifications) ?? true;
  }

  static Future<void> setShowNotifications(bool enabled) async {
    await prefs.setBool(_showNotifications, enabled);
  }

  static bool getSoundEnabled() {
    return prefs.getBool(_soundEnabled) ?? true;
  }

  static Future<void> setSoundEnabled(bool enabled) async {
    await prefs.setBool(_soundEnabled, enabled);
  }

  static bool getVibrationEnabled() {
    return prefs.getBool(_vibrationEnabled) ?? true;
  }

  static Future<void> setVibrationEnabled(bool enabled) async {
    await prefs.setBool(_vibrationEnabled, enabled);
  }

  /// إعدادات الشركة
  static String getCompanyName() {
    return prefs.getString(_companyName) ?? '';
  }

  static Future<void> setCompanyName(String name) async {
    await prefs.setString(_companyName, name);
  }

  static String getCompanyAddress() {
    return prefs.getString(_companyAddress) ?? '';
  }

  static Future<void> setCompanyAddress(String address) async {
    await prefs.setString(_companyAddress, address);
  }

  static String getCompanyPhone() {
    return prefs.getString(_companyPhone) ?? '';
  }

  static Future<void> setCompanyPhone(String phone) async {
    await prefs.setString(_companyPhone, phone);
  }

  static String getCompanyEmail() {
    return prefs.getString(_companyEmail) ?? '';
  }

  static Future<void> setCompanyEmail(String email) async {
    await prefs.setString(_companyEmail, email);
  }

  static String getTaxNumber() {
    return prefs.getString(_taxNumber) ?? '';
  }

  static Future<void> setTaxNumber(String taxNumber) async {
    await prefs.setString(_taxNumber, taxNumber);
  }

  static String getCommercialRegister() {
    return prefs.getString(_commercialRegister) ?? '';
  }

  static Future<void> setCommercialRegister(String register) async {
    await prefs.setString(_commercialRegister, register);
  }

  /// إعدادات الفواتير
  static String getDefaultPaymentMethod() {
    return prefs.getString(_defaultPaymentMethod) ?? 'نقداً';
  }

  static Future<void> setDefaultPaymentMethod(String method) async {
    await prefs.setString(_defaultPaymentMethod, method);
  }

  static bool getPrintAfterSale() {
    return prefs.getBool(_printAfterSale) ?? false;
  }

  static Future<void> setPrintAfterSale(bool enabled) async {
    await prefs.setBool(_printAfterSale, enabled);
  }

  static bool getShowCustomerInInvoice() {
    return prefs.getBool(_showCustomerInInvoice) ?? true;
  }

  static Future<void> setShowCustomerInInvoice(bool enabled) async {
    await prefs.setBool(_showCustomerInInvoice, enabled);
  }

  static bool getRoundPrices() {
    return prefs.getBool(_roundPrices) ?? false;
  }

  static Future<void> setRoundPrices(bool enabled) async {
    await prefs.setBool(_roundPrices, enabled);
  }

  /// إعدادات التطبيق
  static bool isFirstRun() {
    return prefs.getBool(_firstRun) ?? true;
  }

  static Future<void> setFirstRun(bool isFirst) async {
    await prefs.setBool(_firstRun, isFirst);
  }

  static String getAppVersion() {
    return prefs.getString(_appVersion) ?? '';
  }

  static Future<void> setAppVersion(String version) async {
    await prefs.setString(_appVersion, version);
  }

  /// إعادة تعيين جميع الإعدادات
  static Future<void> resetAllSettings() async {
    await prefs.clear();
  }

  /// إعادة تعيين إعدادات محددة
  static Future<void> resetToDefaults() async {
    await setThemeMode(ThemeMode.system);
    await setLanguage('ar');
    await setCurrency(AppConstants.defaultCurrency);
    await setDateFormat(AppConstants.displayDateFormat);
    await setTimeFormat(AppConstants.timeFormat);
    await setLowStockThreshold(AppConstants.defaultMinStockLevel);
    await setAutoBackup(false);
    await setBackupFrequency('weekly');
    await setShowNotifications(true);
    await setSoundEnabled(true);
    await setVibrationEnabled(true);
    await setDefaultPaymentMethod('نقداً');
    await setPrintAfterSale(false);
    await setShowCustomerInInvoice(true);
    await setRoundPrices(false);
  }

  /// تصدير الإعدادات
  static Map<String, dynamic> exportSettings() {
    return {
      'theme_mode': prefs.getString(_themeMode),
      'language': prefs.getString(_language),
      'currency': prefs.getString(_currency),
      'date_format': prefs.getString(_dateFormat),
      'time_format': prefs.getString(_timeFormat),
      'low_stock_threshold': prefs.getInt(_lowStockThreshold),
      'auto_backup': prefs.getBool(_autoBackup),
      'backup_frequency': prefs.getString(_backupFrequency),
      'show_notifications': prefs.getBool(_showNotifications),
      'sound_enabled': prefs.getBool(_soundEnabled),
      'vibration_enabled': prefs.getBool(_vibrationEnabled),
      'company_name': prefs.getString(_companyName),
      'company_address': prefs.getString(_companyAddress),
      'company_phone': prefs.getString(_companyPhone),
      'company_email': prefs.getString(_companyEmail),
      'tax_number': prefs.getString(_taxNumber),
      'commercial_register': prefs.getString(_commercialRegister),
      'default_payment_method': prefs.getString(_defaultPaymentMethod),
      'print_after_sale': prefs.getBool(_printAfterSale),
      'show_customer_in_invoice': prefs.getBool(_showCustomerInInvoice),
      'round_prices': prefs.getBool(_roundPrices),
    };
  }

  /// استيراد الإعدادات
  static Future<void> importSettings(Map<String, dynamic> settings) async {
    for (final entry in settings.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) continue;

      if (value is String) {
        await prefs.setString(key, value);
      } else if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      }
    }
  }

  /// التحقق من وجود إعداد
  static bool hasSetting(String key) {
    return prefs.containsKey(key);
  }

  /// حذف إعداد محدد
  static Future<void> removeSetting(String key) async {
    await prefs.remove(key);
  }

  // دوال إضافية مطلوبة
  static String? getEncryptionKey() {
    return prefs.getString('encryption_key');
  }

  static Future<void> setEncryptionKey(String key) async {
    await prefs.setString('encryption_key', key);
  }

  static List<Map<String, dynamic>> getLoginAttempts() {
    final jsonList = prefs.getStringList('login_attempts') ?? [];
    return jsonList
        .map((item) => json.decode(item) as Map<String, dynamic>)
        .toList();
  }

  static Future<void> setLoginAttempts(
      List<Map<String, dynamic>> attempts) async {
    final jsonList = attempts.map((item) => json.encode(item)).toList();
    await prefs.setStringList('login_attempts', jsonList);
  }

  static DateTime? getAccountLockTime() {
    final value = prefs.getString('account_lock_time');
    return value != null ? DateTime.parse(value) : null;
  }

  static Future<void> setAccountLockTime(DateTime time) async {
    await prefs.setString('account_lock_time', time.toIso8601String());
  }

  static Future<void> clearAccountLockTime() async {
    await prefs.remove('account_lock_time');
  }

  static bool getAnalytics() {
    return prefs.getBool('analytics_enabled') ?? true;
  }

  static Future<void> setAnalytics(bool enabled) async {
    await prefs.setBool('analytics_enabled', enabled);
  }

  static Map<String, dynamic>? getAnalyticsData() {
    final value = prefs.getString('analytics_data');
    return value != null ? json.decode(value) as Map<String, dynamic> : null;
  }

  static Future<void> setAnalyticsData(Map<String, dynamic> data) async {
    await prefs.setString('analytics_data', json.encode(data));
  }

  static bool getPerformanceMonitoring() {
    return prefs.getBool('performance_monitoring') ?? false;
  }

  static Future<void> setPerformanceMonitoring(bool enabled) async {
    await prefs.setBool('performance_monitoring', enabled);
  }

  static Future<void> resetSettings() async {
    await resetToDefaults();
  }

  // دوال إضافية للإشعارات
  static bool getNotifications() {
    return prefs.getBool('notifications_enabled') ?? true;
  }

  static Future<void> setNotifications(bool enabled) async {
    await prefs.setBool('notifications_enabled', enabled);
  }
}
