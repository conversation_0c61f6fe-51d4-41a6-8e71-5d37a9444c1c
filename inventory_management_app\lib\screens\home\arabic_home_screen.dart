import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// الشاشة الرئيسية لإدارة محل المواد الغذائية
class ArabicHomeScreen extends StatelessWidget {
  const ArabicHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'إدارة محل المواد الغذائية',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.green[700],
          foregroundColor: Colors.white,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // ترحيب
              Card(
                elevation: 4,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: <Color>[Colors.green[600]!, Colors.green[800]!],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    ),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'مرحباً بك في نظام إدارة المحل',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'إدارة شاملة لجميع أصناف المواد الغذائية والعمليات التجارية',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // الأقسام الرئيسية
              const Text(
                'الأقسام الرئيسية',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              
              const SizedBox(height: 16),
              
              // شبكة الأقسام
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
                children: <Widget>[
                  _buildSectionCard(
                    context,
                    'الأصناف الغذائية',
                    Icons.fastfood,
                    Colors.orange,
                    '/products',
                  ),
                  _buildSectionCard(
                    context,
                    'العملاء',
                    Icons.people,
                    Colors.blue,
                    '/customers',
                  ),
                  _buildSectionCard(
                    context,
                    'الموردين',
                    Icons.local_shipping,
                    Colors.purple,
                    '/suppliers',
                  ),
                  _buildSectionCard(
                    context,
                    'المبيعات',
                    Icons.point_of_sale,
                    Colors.green,
                    '/sales',
                  ),
                  _buildSectionCard(
                    context,
                    'المشتريات',
                    Icons.shopping_cart,
                    Colors.indigo,
                    '/purchases',
                  ),
                  _buildSectionCard(
                    context,
                    'المصروفات',
                    Icons.money_off,
                    Colors.red,
                    '/expenses',
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // الإدارة والتقارير
              const Text(
                'الإدارة والتقارير',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
              ),
              
              const SizedBox(height: 16),
              
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'التقارير',
                      Icons.analytics,
                      Colors.teal,
                      '/reports',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'النسخ الاحتياطي',
                      Icons.backup,
                      Colors.brown,
                      '/backup',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'الفئات',
                      Icons.category,
                      Colors.pink,
                      '/categories',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionCard(
                      context,
                      'الإعدادات',
                      Icons.settings,
                      Colors.grey,
                      '/settings',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // إحصائيات سريعة
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const Text(
                        'إحصائيات سريعة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: <Widget>[
                          _buildStatItem('الأصناف', '٠', Colors.orange),
                          _buildStatItem('العملاء', '٠', Colors.blue),
                          _buildStatItem('الموردين', '٠', Colors.purple),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String route,
  ) {
    return Card(
      elevation: 3,
      child: InkWell(
        onTap: () => context.go(route),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: <Color>[color.withOpacity(0.1), color.withOpacity(0.2)],
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'Cairo',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String route,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => context.go(route),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: <Widget>[
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: <Widget>[
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
            fontFamily: 'Cairo',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontFamily: 'Cairo',
          ),
        ),
      ],
    );
  }
}
