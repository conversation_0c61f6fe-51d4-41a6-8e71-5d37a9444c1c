/// Model class representing an item in a customer order
class OrderItem {
  /// Unique identifier for the order item
  int? id;

  /// Order ID that this item belongs to
  int? orderId;

  /// Product ID for this order item
  int? productId;

  /// Quantity of the product ordered
  double? quantity;

  /// Price per unit for this order item
  double? price;

  /// Constructor for creating an OrderItem instance
  OrderItem({
    this.id,
    this.orderId,
    this.productId,
    this.quantity,
    this.price,
  });

  /// Converts the OrderItem instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'orderId': orderId,
      'productId': productId,
      'quantity': quantity,
      'price': price,
    };
  }

  /// Creates an OrderItem instance from a Map (typically from database)
  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'] as int?,
      orderId: map['orderId'] as int?,
      productId: map['productId'] as int?,
      quantity: map['quantity']?.toDouble(),
      price: map['price']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'OrderItem{id: $id, orderId: $orderId, productId: $productId, '
        'quantity: $quantity, price: $price}';
  }
}
