import 'dart:io';
import 'package:flutter/material.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:provider/provider.dart';
import '../services/backup_service.dart';
import '../services/database_service.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/supplier_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/order_provider.dart';
import '../providers/expense_provider.dart';
import '../providers/activity_provider.dart';
import '../providers/customer_statement_provider.dart';
import '../providers/supplier_statement_provider.dart';
import '../providers/internal_transfer_provider.dart';
import '../providers/analytics_provider.dart';
import '../providers/store_inventory_provider.dart';

/// مزود المزامنة عبر Google Drive
class SyncProvider extends ChangeNotifier {
  final BackupService _backupService = BackupService();

  // إعدادات المزامنة
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  String? _lastSyncStatus;
  String? _errorMessage;
  String? _successMessage;

  // Getters
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get lastSyncStatus => _lastSyncStatus;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;

  /// تنفيذ المزامنة مع Google Drive
  Future<void> performSync(BuildContext context, List<drive.File> driveBackups) async {
    try {
      _isSyncing = true;
      _clearMessages();
      notifyListeners();

      debugPrint('🔄 بدء عملية المزامنة...');

      if (driveBackups.isEmpty) {
        _setError('لا توجد نسخ احتياطية على Google Drive للمزامنة معها');
        _lastSyncStatus = 'فشل - لا توجد نسخ احتياطية';
        return;
      }

      // تحديد أحدث نسخة احتياطية
      final drive.File latestBackup = _findLatestBackup(driveBackups);
      final Map<String, dynamic> backupInfo = _getDriveFileInfo(latestBackup);

      debugPrint('📁 أحدث نسخة احتياطية: ${backupInfo['name']}');

      // عرض حوار التأكيد
      final bool? confirmed = await _showSyncConfirmationDialog(context, backupInfo);
      
      if (confirmed != true) {
        _setError('تم إلغاء المزامنة من قبل المستخدم');
        _lastSyncStatus = 'ملغاة';
        return;
      }

      // تنزيل واستعادة النسخة الاحتياطية
      await _backupService.downloadDatabaseFromGoogleDrive(latestBackup.id!);
      
      // إعادة تحميل جميع البيانات في التطبيق
      await _reloadAllProviders(context);

      // تحديث حالة المزامنة
      _lastSyncTime = DateTime.now();
      _lastSyncStatus = 'نجحت';

      _setSuccess('تم إجراء المزامنة بنجاح مع النسخة الاحتياطية: ${backupInfo['name']}');
      debugPrint('✅ تمت المزامنة بنجاح');

    } catch (e) {
      _lastSyncStatus = 'فشلت: $e';
      _setError('فشل في المزامنة: $e');
      debugPrint('❌ خطأ في المزامنة: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// تحديد أحدث نسخة احتياطية من القائمة
  drive.File _findLatestBackup(List<drive.File> backups) {
    drive.File latest = backups.first;
    
    for (final drive.File backup in backups) {
      final DateTime? backupTime = backup.modifiedTime;
      final DateTime? latestTime = latest.modifiedTime;
      
      if (backupTime != null && latestTime != null) {
        if (backupTime.isAfter(latestTime)) {
          latest = backup;
        }
      }
    }
    
    return latest;
  }

  /// عرض حوار تأكيد المزامنة
  Future<bool?> _showSyncConfirmationDialog(BuildContext context, Map<String, dynamic> backupInfo) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: AlertDialog(
            title: const Row(
              children: <Widget>[
                Icon(Icons.warning, color: Colors.orange, size: 28),
                SizedBox(width: 12),
                Text('تأكيد المزامنة'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'تحذير: المزامنة ستحذف جميع البيانات الحالية على هذا الجهاز وتستبدلها ببيانات النسخة الاحتياطية الأحدث من Google Drive.',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 16),
                const Text('معلومات النسخة الاحتياطية الأحدث:'),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text('الاسم: ${backupInfo['name']}'),
                      Text('الحجم: ${backupInfo['sizeFormatted']}'),
                      if (backupInfo['modifiedTime'] != null)
                        Text('التاريخ: ${_formatDateTime(backupInfo['modifiedTime'])}'),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'هل أنت متأكد من رغبتك في المتابعة؟',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تأكيد المزامنة'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// إعادة تحميل جميع البيانات في التطبيق بعد المزامنة
  Future<void> _reloadAllProviders(BuildContext context) async {
    try {
      debugPrint('🔄 إعادة تحميل جميع البيانات...');

      // الحصول على جميع المزودات من السياق
      final ProductProvider productProvider = Provider.of<ProductProvider>(context, listen: false);
      final CustomerProvider customerProvider = Provider.of<CustomerProvider>(context, listen: false);
      final SupplierProvider supplierProvider = Provider.of<SupplierProvider>(context, listen: false);
      final SaleProvider saleProvider = Provider.of<SaleProvider>(context, listen: false);
      final PurchaseProvider purchaseProvider = Provider.of<PurchaseProvider>(context, listen: false);
      final OrderProvider orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final ExpenseProvider expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
      final ActivityProvider activityProvider = Provider.of<ActivityProvider>(context, listen: false);
      final CustomerStatementProvider customerStatementProvider = Provider.of<CustomerStatementProvider>(context, listen: false);
      final SupplierStatementProvider supplierStatementProvider = Provider.of<SupplierStatementProvider>(context, listen: false);
      final InternalTransferProvider internalTransferProvider = Provider.of<InternalTransferProvider>(context, listen: false);
      final AnalyticsProvider analyticsProvider = Provider.of<AnalyticsProvider>(context, listen: false);
      final StoreInventoryProvider storeInventoryProvider = Provider.of<StoreInventoryProvider>(context, listen: false);

      // إعادة تحميل البيانات لكل مزود
      await Future.wait(<dynamic>[
        productProvider.fetchProducts(),
        customerProvider.fetchCustomers(),
        supplierProvider.fetchSuppliers(),
        saleProvider.fetchSales(),
        purchaseProvider.fetchPurchases(),
        orderProvider.fetchOrders(),
        expenseProvider.fetchExpenses(),
        activityProvider.fetchActivities(),
        customerStatementProvider.refreshStatements(),
        supplierStatementProvider.refreshStatements(),
        internalTransferProvider.fetchTransfers(),
        analyticsProvider.refreshAnalytics(),
        storeInventoryProvider.fetchAdjustments(),
      ]);

      debugPrint('✅ تم إعادة تحميل جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
      // لا نرمي الخطأ هنا لأن المزامنة نجحت، فقط إعادة التحميل فشلت
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على معلومات ملف Drive
  Map<String, dynamic> _getDriveFileInfo(drive.File driveFile) {
    return <String, dynamic>{
      'name': driveFile.name ?? 'Unknown',
      'id': driveFile.id ?? '',
      'modifiedTime': driveFile.modifiedTime,
      'size': driveFile.size != null ? int.parse(driveFile.size!) : 0,
      'sizeFormatted': driveFile.size != null
          ? _backupService.formatFileSize(int.parse(driveFile.size!))
          : 'Unknown',
    };
  }

  /// الحصول على نص حالة المزامنة الأخيرة
  String get lastSyncStatusText {
    if (_lastSyncTime == null) {
      return 'لم يتم إجراء مزامنة من قبل';
    }

    final String timeText = _formatDateTime(_lastSyncTime!);
    final String statusText = _lastSyncStatus ?? 'غير معروف';
    
    return 'آخر مزامنة: $timeText ($statusText)';
  }

  /// إعادة تعيين حالة المزامنة
  void resetSyncStatus() {
    _lastSyncTime = null;
    _lastSyncStatus = null;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    _successMessage = null;
    notifyListeners();
  }

  void _setSuccess(String? message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }
}
