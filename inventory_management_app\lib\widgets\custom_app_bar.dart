import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onAdd;
  final VoidCallback? onSearch;
  final VoidCallback? onFilter;
  final bool showBackButton;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.onAdd,
    this.onSearch,
    this.onFilter,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 4,
      automaticallyImplyLeading: showBackButton,
      actions: _buildActions(),
    );
  }

  List<Widget> _buildActions() {
    final List<Widget> actionsList = <Widget>[];

    if (onSearch != null) {
      actionsList.add(IconButton(icon: const Icon(Icons.search), onPressed: onSearch, tooltip: 'Search'));
    }

    if (onFilter != null) {
      actionsList.add(IconButton(icon: const Icon(Icons.filter_list), onPressed: onFilter, tooltip: 'Filter'));
    }

    if (onAdd != null) {
      actionsList.add(IconButton(icon: const Icon(Icons.add), onPressed: onAdd, tooltip: 'Add'));
    }

    if (actions != null) {
      actionsList.addAll(actions!);
    }

    return actionsList;
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
