import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';

/// شاشة قائمة المنتجات المحسنة
class EnhancedProductListScreen extends StatefulWidget {
  const EnhancedProductListScreen({super.key});

  @override
  State<EnhancedProductListScreen> createState() => _EnhancedProductListScreenState();
}

class _EnhancedProductListScreenState extends State<EnhancedProductListScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  String _filterBy = 'all';

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadProducts() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductProvider>().loadProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            _buildSearchAndFilters(),
            Expanded(child: _buildProductList()),
          ],
        ),
        floatingActionButton: CustomFloatingActionButton(
          onPressed: () => context.go('/products/add'),
          icon: Icons.add,
          tooltip: 'إضافة منتج جديد',
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('المنتجات'),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _toggleSearch,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.file_download),
                title: Text('تصدير'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.file_upload),
                title: Text('استيراد'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'low_stock',
              child: ListTile(
                leading: Icon(Icons.warning, color: AppColors.warning),
                title: Text('المخزون المنخفض'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextFormField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن منتج...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              filled: true,
              fillColor: AppColors.surfaceVariant,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                borderSide: BorderSide.none,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: AppDimensions.paddingM),
          
          // أزرار التصفية والترتيب
          Row(
            children: [
              Expanded(
                child: _buildFilterChips(),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              _buildSortButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          _buildFilterChip('in_stock', 'متوفر'),
          _buildFilterChip('low_stock', 'مخزون منخفض'),
          _buildFilterChip('out_of_stock', 'نفد المخزون'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _filterBy == value;
    return Padding(
      padding: const EdgeInsets.only(left: AppDimensions.paddingS),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _filterBy = value;
          });
        },
        backgroundColor: AppColors.surfaceVariant,
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
        labelStyle: TextStyle(
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      icon: Icon(
        _sortAscending ? Icons.sort : Icons.sort,
        color: AppColors.primary,
      ),
      onSelected: (value) {
        setState(() {
          if (_sortBy == value) {
            _sortAscending = !_sortAscending;
          } else {
            _sortBy = value;
            _sortAscending = true;
          }
        });
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'name',
          child: Text('ترتيب حسب الاسم'),
        ),
        const PopupMenuItem(
          value: 'quantity',
          child: Text('ترتيب حسب الكمية'),
        ),
        const PopupMenuItem(
          value: 'price',
          child: Text('ترتيب حسب السعر'),
        ),
        const PopupMenuItem(
          value: 'date',
          child: Text('ترتيب حسب التاريخ'),
        ),
      ],
    );
  }

  Widget _buildProductList() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final filteredProducts = _getFilteredProducts(productProvider.products);

        if (filteredProducts.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            await productProvider.loadProducts();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            itemCount: filteredProducts.length,
            itemBuilder: (context, index) {
              final product = filteredProducts[index];
              return _buildProductCard(product);
            },
          ),
        );
      },
    );
  }

  Widget _buildProductCard(Product product) {
    final isLowStock = (product.quantity ?? 0) <= (product.minLevel ?? 10);
    final isOutOfStock = (product.quantity ?? 0) <= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: AppDimensions.elevationS,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        side: isOutOfStock
            ? const BorderSide(color: AppColors.error, width: 1)
            : isLowStock
                ? const BorderSide(color: AppColors.warning, width: 1)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => context.go('/products/details/${product.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name ?? '',
                          style: AppStyles.titleMedium,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (product.description?.isNotEmpty == true) ...[
                          const SizedBox(height: AppDimensions.paddingXS),
                          Text(
                            product.description!,
                            style: AppStyles.bodySmall,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStockStatusBadge(product),
                ],
              ),
              
              const SizedBox(height: AppDimensions.paddingM),
              
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'الكمية',
                      '${product.quantity ?? 0} ${product.unit ?? ''}',
                      Icons.inventory_2,
                      isOutOfStock
                          ? AppColors.error
                          : isLowStock
                              ? AppColors.warning
                              : AppColors.success,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'سعر البيع',
                      Formatters.formatCurrency(product.salePrice ?? 0),
                      Icons.sell,
                      AppColors.primary,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppDimensions.paddingM),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      if (product.category?.isNotEmpty == true)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: AppDimensions.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                          ),
                          child: Text(
                            product.category!,
                            style: AppStyles.labelSmall.copyWith(
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                    ],
                  ),
                  ListActionButtons(
                    onEdit: () => context.go('/products/edit/${product.id}'),
                    onDelete: () => _deleteProduct(product),
                    onView: () => context.go('/products/details/${product.id}'),
                    showView: true,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStockStatusBadge(Product product) {
    final quantity = product.quantity ?? 0;
    final minLevel = product.minLevel ?? 10;
    
    Color color;
    String text;
    IconData icon;
    
    if (quantity <= 0) {
      color = AppColors.error;
      text = 'نفد';
      icon = Icons.error;
    } else if (quantity <= minLevel) {
      color = AppColors.warning;
      text = 'منخفض';
      icon = Icons.warning;
    } else {
      color = AppColors.success;
      text = 'متوفر';
      icon = Icons.check_circle;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: AppStyles.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: AppDimensions.paddingXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppStyles.labelSmall,
              ),
              Text(
                value,
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            'لا توجد منتجات',
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            'ابدأ بإضافة منتجات جديدة',
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          CustomPrimaryButton(
            text: 'إضافة منتج',
            onPressed: () => context.go('/products/add'),
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  List<Product> _getFilteredProducts(List<Product> products) {
    var filtered = products.where((product) {
      // تصفية البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final name = (product.name ?? '').toLowerCase();
        final description = (product.description ?? '').toLowerCase();
        final category = (product.category ?? '').toLowerCase();
        
        if (!name.contains(query) && 
            !description.contains(query) && 
            !category.contains(query)) {
          return false;
        }
      }
      
      // تصفية حسب الحالة
      final quantity = product.quantity ?? 0;
      final minLevel = product.minLevel ?? 10;
      
      switch (_filterBy) {
        case 'in_stock':
          return quantity > minLevel;
        case 'low_stock':
          return quantity > 0 && quantity <= minLevel;
        case 'out_of_stock':
          return quantity <= 0;
        default:
          return true;
      }
    }).toList();
    
    // ترتيب النتائج
    filtered.sort((a, b) {
      int comparison = 0;
      
      switch (_sortBy) {
        case 'name':
          comparison = (a.name ?? '').compareTo(b.name ?? '');
          break;
        case 'quantity':
          comparison = (a.quantity ?? 0).compareTo(b.quantity ?? 0);
          break;
        case 'price':
          comparison = (a.salePrice ?? 0).compareTo(b.salePrice ?? 0);
          break;
        case 'date':
          comparison = (a.createdAt ?? DateTime.now())
              .compareTo(b.createdAt ?? DateTime.now());
          break;
      }
      
      return _sortAscending ? comparison : -comparison;
    });
    
    return filtered;
  }

  void _toggleSearch() {
    // TODO: تنفيذ البحث المتقدم
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportProducts();
        break;
      case 'import':
        _importProducts();
        break;
      case 'low_stock':
        _showLowStockProducts();
        break;
    }
  }

  void _exportProducts() {
    // TODO: تنفيذ تصدير المنتجات
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ التصدير قريباً');
  }

  void _importProducts() {
    // TODO: تنفيذ استيراد المنتجات
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ الاستيراد قريباً');
  }

  void _showLowStockProducts() {
    setState(() {
      _filterBy = 'low_stock';
    });
  }

  void _deleteProduct(Product product) async {
    final confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: product.name ?? 'المنتج',
    );

    if (confirmed == true) {
      try {
        await context.read<ProductProvider>().deleteProduct(product.id!);
        SnackBarHelper.showSuccess(context, 'تم حذف المنتج بنجاح');
      } catch (e) {
        SnackBarHelper.showError(context, 'فشل في حذف المنتج: $e');
      }
    }
  }
}
