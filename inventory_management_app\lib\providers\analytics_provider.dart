import 'package:flutter/material.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/expense_provider.dart';
import '../providers/internal_transfer_provider.dart';
import '../providers/product_provider.dart';
import '../providers/store_inventory_provider.dart';
import '../models/sale.dart';
import '../models/purchase.dart';
import '../models/expense.dart';
import '../models/internal_transfer.dart';

/// Provider class for managing analytics and reports
class AnalyticsProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // KPI Data
  double _totalWholesaleSales = 0.0;
  double _totalRetailSales = 0.0;
  double _totalWholesaleProfit = 0.0;
  double _potentialRetailProfit = 0.0;
  double _actualRetailProfit = 0.0;
  int _totalWarehouseQuantity = 0;
  int _totalStoreQuantity = 0;
  double _totalExpenses = 0.0;
  double _totalPurchases = 0.0;
  int _totalInternalTransfers = 0;
  double _totalInventoryAdjustmentValue = 0.0;
  double _totalInventoryLosses = 0.0;

  // Chart Data
  List<Map<String, dynamic>> _salesDistributionData = <Map<String, dynamic>>[];
  List<Map<String, dynamic>> _inventoryDistributionData =
      <Map<String, dynamic>>[];
  List<Map<String, dynamic>> _profitTrendData = <Map<String, dynamic>>[];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime get startDate => _startDate;
  DateTime get endDate => _endDate;

  // KPI Getters
  double get totalWholesaleSales => _totalWholesaleSales;
  double get totalRetailSales => _totalRetailSales;
  double get totalWholesaleProfit => _totalWholesaleProfit;
  double get potentialRetailProfit => _potentialRetailProfit;
  double get actualRetailProfit => _actualRetailProfit;
  int get totalWarehouseQuantity => _totalWarehouseQuantity;
  int get totalStoreQuantity => _totalStoreQuantity;
  double get totalExpenses => _totalExpenses;
  double get totalPurchases => _totalPurchases;
  int get totalInternalTransfers => _totalInternalTransfers;
  double get totalInventoryAdjustmentValue => _totalInventoryAdjustmentValue;
  double get totalInventoryLosses => _totalInventoryLosses;

  // Chart Data Getters
  List<Map<String, dynamic>> get salesDistributionData =>
      _salesDistributionData;
  List<Map<String, dynamic>> get inventoryDistributionData =>
      _inventoryDistributionData;
  List<Map<String, dynamic>> get profitTrendData => _profitTrendData;

  // Formatted Getters
  String get formattedTotalWholesaleSales =>
      '${_totalWholesaleSales.toStringAsFixed(2)} ر.س';
  String get formattedTotalRetailSales =>
      '${_totalRetailSales.toStringAsFixed(2)} ر.س';
  String get formattedTotalWholesaleProfit =>
      '${_totalWholesaleProfit.toStringAsFixed(2)} ر.س';
  String get formattedPotentialRetailProfit =>
      '${_potentialRetailProfit.toStringAsFixed(2)} ر.س';
  String get formattedActualRetailProfit =>
      '${_actualRetailProfit.toStringAsFixed(2)} ر.س';
  String get formattedTotalExpenses =>
      '${_totalExpenses.toStringAsFixed(2)} ر.س';
  String get formattedTotalPurchases =>
      '${_totalPurchases.toStringAsFixed(2)} ر.س';
  String get formattedTotalInventoryAdjustmentValue =>
      '${_totalInventoryAdjustmentValue.toStringAsFixed(2)} ر.س';
  String get formattedTotalInventoryLosses =>
      '${_totalInventoryLosses.toStringAsFixed(2)} ر.س';

  // Calculated Getters
  double get totalSales => _totalWholesaleSales + _totalRetailSales;
  double get totalProfit => _totalWholesaleProfit + _actualRetailProfit;
  double get totalPotentialProfit =>
      _totalWholesaleProfit + _potentialRetailProfit;
  double get netProfit => totalProfit - _totalExpenses;
  double get netPotentialProfit => totalPotentialProfit - _totalExpenses;
  String get formattedTotalSales => '${totalSales.toStringAsFixed(2)} ر.س';
  String get formattedTotalProfit => '${totalProfit.toStringAsFixed(2)} ر.س';
  String get formattedTotalPotentialProfit =>
      '${totalPotentialProfit.toStringAsFixed(2)} ر.س';
  String get formattedNetProfit => '${netProfit.toStringAsFixed(2)} ر.س';
  String get formattedNetPotentialProfit =>
      '${netPotentialProfit.toStringAsFixed(2)} ر.س';

  /// Set date range for analytics
  void setDateRange(DateTime start, DateTime end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  /// Initialize analytics data
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة AnalyticsProvider...');
      await loadAnalyticsData();
      debugPrint('✅ تم تهيئة AnalyticsProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة AnalyticsProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل بيانات التحليلات: $e';
      notifyListeners();
    }
  }

  /// Load all analytics data
  Future<void> loadAnalyticsData() async {
    _setLoading(true);
    _clearError();

    try {
      // Reset all data
      _resetData();

      // Load data from all providers (this would need to be injected)
      // For now, we'll simulate the data loading
      await Future.delayed(const Duration(milliseconds: 500));

      // Calculate KPIs
      await _calculateKPIs();

      // Prepare chart data
      _prepareChartData();

      notifyListeners();
      debugPrint('📊 تم تحميل بيانات التحليلات بنجاح');
    } catch (e) {
      _setError('فشل في تحميل بيانات التحليلات: $e');
      debugPrint('❌ خطأ في تحميل التحليلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load sales data for the specified date range
  Future<void> fetchSalesData(SaleProvider saleProvider) async {
    try {
      // This would fetch sales data from the provider
      // For now, we'll use the existing data
      final List<Sale> sales = saleProvider.sales;

      _totalWholesaleSales = 0.0;
      _totalRetailSales = 0.0;

      for (final Sale sale in sales) {
        if (_isInDateRange(sale.date)) {
          _totalWholesaleSales += sale.totalWholesaleAmount ?? 0.0;
          _totalRetailSales += sale.totalRetailAmount ?? 0.0;
        }
      }

      debugPrint('📈 مبيعات الجملة: $_totalWholesaleSales');
      debugPrint('📈 مبيعات التجزئة: $_totalRetailSales');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المبيعات: $e');
    }
  }

  /// Load internal transfer data
  Future<void> fetchInternalTransferData(
      InternalTransferProvider transferProvider) async {
    try {
      final List<InternalTransfer> transfers = transferProvider.transfers;

      _potentialRetailProfit = 0.0;
      _totalInternalTransfers = 0;

      for (final InternalTransfer transfer in transfers) {
        if (_isInDateRange(transfer.transferDate.toIso8601String())) {
          _potentialRetailProfit +=
              (transfer.totalRetailValue - transfer.totalWholesaleValue);
          _totalInternalTransfers++;
        }
      }

      debugPrint('💰 الربح المحتمل من التجزئة: $_potentialRetailProfit');
      debugPrint('🔄 عدد التحويلات الداخلية: $_totalInternalTransfers');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات التحويلات: $e');
    }
  }

  /// Load inventory data
  Future<void> fetchInventoryData(ProductProvider productProvider) async {
    try {
      _totalWarehouseQuantity = productProvider.getTotalWarehouseQuantity();
      _totalStoreQuantity = productProvider.getTotalStoreQuantity();

      debugPrint('📦 إجمالي المخزن: $_totalWarehouseQuantity');
      debugPrint('🏪 إجمالي البقالة: $_totalStoreQuantity');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المخزون: $e');
    }
  }

  /// Load expense data
  Future<void> fetchExpenseData(ExpenseProvider expenseProvider) async {
    try {
      final List<Expense> expenses = expenseProvider.expenses;

      _totalExpenses = 0.0;

      for (final Expense expense in expenses) {
        if (_isInDateRange(expense.expenseDate?.toIso8601String())) {
          _totalExpenses += expense.amount ?? 0.0;
        }
      }

      debugPrint('💸 إجمالي المصروفات: $_totalExpenses');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المصروفات: $e');
    }
  }

  /// Load store inventory adjustment data
  Future<void> fetchStoreInventoryAdjustmentData(
      StoreInventoryProvider storeInventoryProvider) async {
    try {
      // Get total adjustment value for the date range
      _totalInventoryAdjustmentValue = await storeInventoryProvider
          .getTotalAdjustmentValue(_startDate, _endDate);

      // Get negative adjustment value (losses) for the date range
      _totalInventoryLosses = await storeInventoryProvider
          .getNegativeAdjustmentValue(_startDate, _endDate);

      debugPrint(
          '📊 إجمالي قيمة تعديلات المخزون: $_totalInventoryAdjustmentValue');
      debugPrint('📉 إجمالي خسائر المخزون: $_totalInventoryLosses');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات تعديلات المخزون: $e');
    }
  }

  /// Calculate KPIs
  Future<void> _calculateKPIs() async {
    // Calculate wholesale profit (this would need cost data)
    // For now, we'll estimate it as 20% of wholesale sales
    _totalWholesaleProfit = _totalWholesaleSales * 0.2;

    // Calculate actual retail profit
    // Actual retail profit = Potential retail profit - Inventory losses
    _actualRetailProfit = _potentialRetailProfit - _totalInventoryLosses.abs();

    // Ensure actual retail profit is not negative
    if (_actualRetailProfit < 0) {
      _actualRetailProfit = 0.0;
    }

    debugPrint('📊 تم حساب مؤشرات الأداء');
    debugPrint('💰 الربح المحتمل من التجزئة: $_potentialRetailProfit');
    debugPrint('💰 الربح الفعلي من التجزئة: $_actualRetailProfit');
    debugPrint('📉 خسائر المخزون: $_totalInventoryLosses');
  }

  /// Prepare chart data
  void _prepareChartData() {
    // Sales Distribution Chart Data
    _salesDistributionData = <Map<String, dynamic>>[
      <String, dynamic>{
        'label': 'مبيعات الجملة',
        'value': _totalWholesaleSales,
        'color': Colors.blue,
      },
      <String, dynamic>{
        'label': 'مبيعات التجزئة',
        'value': _totalRetailSales,
        'color': Colors.orange,
      },
    ];

    // Inventory Distribution Chart Data
    _inventoryDistributionData = <Map<String, dynamic>>[
      <String, dynamic>{
        'label': 'المخزن',
        'value': _totalWarehouseQuantity.toDouble(),
        'color': Colors.green,
      },
      <String, dynamic>{
        'label': 'البقالة',
        'value': _totalStoreQuantity.toDouble(),
        'color': Colors.purple,
      },
    ];

    // Profit Trend Data (simplified)
    _profitTrendData = <Map<String, dynamic>>[
      <String, dynamic>{
        'period': 'الأسبوع الماضي',
        'profit': _totalWholesaleProfit * 0.7,
      },
      <String, dynamic>{
        'period': 'هذا الأسبوع',
        'profit': _totalWholesaleProfit,
      },
    ];

    debugPrint('📈 تم تجهيز بيانات الرسوم البيانية');
  }

  /// Check if date is in the specified range
  bool _isInDateRange(String? dateString) {
    if (dateString == null) return false;

    try {
      final DateTime date = DateTime.parse(dateString);
      return date.isAfter(_startDate.subtract(const Duration(days: 1))) &&
          date.isBefore(_endDate.add(const Duration(days: 1)));
    } catch (e) {
      return false;
    }
  }

  /// Reset all data
  void _resetData() {
    _totalWholesaleSales = 0.0;
    _totalRetailSales = 0.0;
    _totalWholesaleProfit = 0.0;
    _potentialRetailProfit = 0.0;
    _actualRetailProfit = 0.0;
    _totalWarehouseQuantity = 0;
    _totalStoreQuantity = 0;
    _totalExpenses = 0.0;
    _totalPurchases = 0.0;
    _totalInternalTransfers = 0;
    _totalInventoryAdjustmentValue = 0.0;
    _totalInventoryLosses = 0.0;
    _salesDistributionData.clear();
    _inventoryDistributionData.clear();
    _profitTrendData.clear();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
