/// Model class for Supplier (المورد)
class Supplier {
  /// Unique identifier for the supplier
  int? id;

  /// Name of the supplier
  String? name;

  /// Phone number of the supplier
  String? phone;

  /// Email address of the supplier
  String? email;

  /// Address of the supplier
  String? address;

  /// Additional notes about the supplier
  String? notes;

  /// Current balance of the supplier (positive = we owe them, negative = they owe us)
  double? balance;

  /// Opening balance when the supplier was first added
  double? openingBalance;

  /// Date when the supplier was created
  String? createdDate;

  /// Category or type of supplier (optional)
  String? category;

  /// Status of the supplier (active, inactive)
  String? status;

  /// Constructor for creating a Supplier instance
  Supplier({
    this.id,
    this.name,
    this.phone,
    this.email,
    this.address,
    this.notes,
    this.balance,
    this.openingBalance,
    this.createdDate,
    this.category,
    this.status,
  });

  /// Converts the Supplier instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'notes': notes,
      'balance': balance,
      'openingBalance': openingBalance,
      'createdDate': createdDate,
      'category': category,
      'status': status,
    };
  }

  /// Creates a Supplier instance from a Map (typically from database)
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'] as int?,
      name: map['name'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      notes: map['notes'] as String?,
      balance: map['balance']?.toDouble(),
      openingBalance: map['openingBalance']?.toDouble(),
      createdDate: map['createdDate'] as String?,
      category: map['category'] as String?,
      status: map['status'] as String?,
    );
  }

  /// Copy with method for creating modified copies
  Supplier copyWith({
    int? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    String? notes,
    double? balance,
    double? openingBalance,
    String? createdDate,
    String? category,
    String? status,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      balance: balance ?? this.balance,
      openingBalance: openingBalance ?? this.openingBalance,
      createdDate: createdDate ?? this.createdDate,
      category: category ?? this.category,
      status: status ?? this.status,
    );
  }

  /// Get balance status for display
  String get balanceStatus {
    if (balance == null || balance == 0) return 'رصيد صفر';
    if (balance! > 0) return 'لهم مبالغ (دائنون لنا)';
    return 'عليهم مبالغ (مدينون لنا)';
  }

  /// Get balance color for display
  String get balanceColor {
    if (balance == null || balance == 0) return 'grey';
    if (balance! > 0) return 'green';
    return 'red';
  }

  /// Get formatted balance for display
  String get formattedBalance {
    if (balance == null) return '0.00 ر.س';
    return '${balance!.abs().toStringAsFixed(2)} ر.س';
  }

  /// Check if supplier is active
  bool get isActive => status == 'active' || status == null;

  @override
  String toString() {
    return 'Supplier{id: $id, name: $name, phone: $phone, email: $email, '
        'address: $address, notes: $notes, balance: $balance, '
        'openingBalance: $openingBalance, createdDate: $createdDate, '
        'category: $category, status: $status}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Supplier && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
