/// Model class representing a supplier
class Supplier {
  /// Unique identifier for the supplier
  int? id;

  /// Name of the supplier
  String name;

  /// Email address of the supplier
  String? email;

  /// Phone number of the supplier
  String? phone;

  /// Address of the supplier
  String? address;

  /// Constructor for creating a Supplier instance
  Supplier({
    this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
  });

  /// Converts the Supplier instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
    };
  }

  /// Creates a Supplier instance from a Map (typically from database)
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
    );
  }

  @override
  String toString() {
    return 'Supplier{id: $id, name: $name, email: $email, '
        'phone: $phone, address: $address}';
  }
}
