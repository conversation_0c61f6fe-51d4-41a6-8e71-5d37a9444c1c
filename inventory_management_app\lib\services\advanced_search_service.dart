import 'package:flutter/material.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';

/// خدمة البحث المتقدم
class AdvancedSearchService {
  static final AdvancedSearchService _instance = AdvancedSearchService._internal();
  factory AdvancedSearchService() => _instance;
  AdvancedSearchService._internal();

  static AdvancedSearchService get instance => _instance;

  /// البحث في المنتجات مع فلاتر متقدمة
  List<Product> searchProducts({
    required List<Product> products,
    String? query,
    double? minPrice,
    double? maxPrice,
    double? minQuantity,
    double? maxQuantity,
    String? category,
    bool? lowStock,
  }) {
    var filteredProducts = products.where((product) {
      // البحث النصي
      if (query != null && query.isNotEmpty) {
        final searchQuery = query.toLowerCase();
        final productName = product.name.toLowerCase();
        final productDescription = (product.description ?? '').toLowerCase();
        
        if (!productName.contains(searchQuery) && 
            !productDescription.contains(searchQuery)) {
          return false;
        }
      }

      // فلتر السعر
      if (minPrice != null && (product.price ?? 0) < minPrice) return false;
      if (maxPrice != null && (product.price ?? 0) > maxPrice) return false;

      // فلتر الكمية
      if (minQuantity != null && (product.quantity ?? 0) < minQuantity) return false;
      if (maxQuantity != null && (product.quantity ?? 0) > maxQuantity) return false;

      // فلتر المخزون المنخفض
      if (lowStock == true && (product.quantity ?? 0) > 10) return false;
      if (lowStock == false && (product.quantity ?? 0) <= 10) return false;

      return true;
    }).toList();

    // ترتيب النتائج حسب الصلة
    if (query != null && query.isNotEmpty) {
      filteredProducts.sort((a, b) {
        final aScore = _calculateRelevanceScore(a, query);
        final bScore = _calculateRelevanceScore(b, query);
        return bScore.compareTo(aScore);
      });
    }

    return filteredProducts;
  }

  /// البحث في العملاء مع فلاتر متقدمة
  List<Customer> searchCustomers({
    required List<Customer> customers,
    String? query,
    String? city,
    bool? hasPhone,
    bool? hasEmail,
  }) {
    return customers.where((customer) {
      // البحث النصي
      if (query != null && query.isNotEmpty) {
        final searchQuery = query.toLowerCase();
        final customerName = customer.name.toLowerCase();
        final customerPhone = (customer.phone ?? '').toLowerCase();
        final customerEmail = (customer.email ?? '').toLowerCase();
        final customerAddress = (customer.address ?? '').toLowerCase();
        
        if (!customerName.contains(searchQuery) && 
            !customerPhone.contains(searchQuery) &&
            !customerEmail.contains(searchQuery) &&
            !customerAddress.contains(searchQuery)) {
          return false;
        }
      }

      // فلتر وجود الهاتف
      if (hasPhone == true && (customer.phone == null || customer.phone!.isEmpty)) return false;
      if (hasPhone == false && customer.phone != null && customer.phone!.isNotEmpty) return false;

      // فلتر وجود البريد الإلكتروني
      if (hasEmail == true && (customer.email == null || customer.email!.isEmpty)) return false;
      if (hasEmail == false && customer.email != null && customer.email!.isNotEmpty) return false;

      return true;
    }).toList();
  }

  /// البحث في المبيعات مع فلاتر متقدمة
  List<Sale> searchSales({
    required List<Sale> sales,
    String? query,
    double? minAmount,
    double? maxAmount,
    DateTime? startDate,
    DateTime? endDate,
    int? customerId,
  }) {
    return sales.where((sale) {
      // البحث النصي (رقم الفاتورة)
      if (query != null && query.isNotEmpty) {
        final saleId = sale.id?.toString() ?? '';
        if (!saleId.contains(query)) return false;
      }

      // فلتر المبلغ
      if (minAmount != null && (sale.total ?? 0) < minAmount) return false;
      if (maxAmount != null && (sale.total ?? 0) > maxAmount) return false;

      // فلتر التاريخ
      if (sale.date != null) {
        final saleDate = DateTime.parse(sale.date!);
        if (startDate != null && saleDate.isBefore(startDate)) return false;
        if (endDate != null && saleDate.isAfter(endDate)) return false;
      }

      // فلتر العميل
      if (customerId != null && sale.customerId != customerId) return false;

      return true;
    }).toList();
  }

  /// البحث الشامل في جميع البيانات
  Map<String, List<dynamic>> globalSearch({
    required String query,
    required List<Product> products,
    required List<Customer> customers,
    required List<Sale> sales,
  }) {
    final results = <String, List<dynamic>>{};

    // البحث في المنتجات
    results['products'] = searchProducts(
      products: products,
      query: query,
    );

    // البحث في العملاء
    results['customers'] = searchCustomers(
      customers: customers,
      query: query,
    );

    // البحث في المبيعات
    results['sales'] = searchSales(
      sales: sales,
      query: query,
    );

    return results;
  }

  /// اقتراحات البحث الذكية
  List<String> getSearchSuggestions({
    required String query,
    required List<Product> products,
    required List<Customer> customers,
  }) {
    final suggestions = <String>[];
    final queryLower = query.toLowerCase();

    // اقتراحات من أسماء المنتجات
    for (final product in products) {
      if (product.name.toLowerCase().contains(queryLower)) {
        suggestions.add(product.name);
      }
    }

    // اقتراحات من أسماء العملاء
    for (final customer in customers) {
      if (customer.name.toLowerCase().contains(queryLower)) {
        suggestions.add(customer.name);
      }
    }

    // إزالة التكرارات وترتيب النتائج
    final uniqueSuggestions = suggestions.toSet().toList();
    uniqueSuggestions.sort();

    return uniqueSuggestions.take(10).toList();
  }

  /// حفظ عمليات البحث الأخيرة
  static final List<String> _recentSearches = [];

  void addRecentSearch(String query) {
    if (query.trim().isEmpty) return;
    
    _recentSearches.remove(query);
    _recentSearches.insert(0, query);
    
    // الاحتفاظ بآخر 20 بحث فقط
    if (_recentSearches.length > 20) {
      _recentSearches.removeRange(20, _recentSearches.length);
    }
  }

  List<String> getRecentSearches() {
    return List.from(_recentSearches);
  }

  void clearRecentSearches() {
    _recentSearches.clear();
  }

  /// حساب درجة الصلة للمنتج
  int _calculateRelevanceScore(Product product, String query) {
    int score = 0;
    final queryLower = query.toLowerCase();
    final nameLower = product.name.toLowerCase();
    final descriptionLower = (product.description ?? '').toLowerCase();

    // تطابق كامل في الاسم
    if (nameLower == queryLower) score += 100;
    
    // يبدأ بالاستعلام
    if (nameLower.startsWith(queryLower)) score += 50;
    
    // يحتوي على الاستعلام في الاسم
    if (nameLower.contains(queryLower)) score += 25;
    
    // يحتوي على الاستعلام في الوصف
    if (descriptionLower.contains(queryLower)) score += 10;

    return score;
  }

  /// فلترة متقدمة للمنتجات حسب معايير متعددة
  List<Product> advancedProductFilter({
    required List<Product> products,
    Map<String, dynamic>? filters,
  }) {
    if (filters == null || filters.isEmpty) return products;

    return products.where((product) {
      // فلتر النطاق السعري
      if (filters.containsKey('priceRange')) {
        final priceRange = filters['priceRange'] as Map<String, double>;
        final price = product.price ?? 0;
        if (price < priceRange['min']! || price > priceRange['max']!) {
          return false;
        }
      }

      // فلتر حالة المخزون
      if (filters.containsKey('stockStatus')) {
        final stockStatus = filters['stockStatus'] as String;
        final quantity = product.quantity ?? 0;
        
        switch (stockStatus) {
          case 'in_stock':
            if (quantity <= 0) return false;
            break;
          case 'low_stock':
            if (quantity > 10) return false;
            break;
          case 'out_of_stock':
            if (quantity > 0) return false;
            break;
        }
      }

      // فلتر الفئة
      if (filters.containsKey('category')) {
        final category = filters['category'] as String;
        // في المستقبل يمكن إضافة فئات للمنتجات
      }

      return true;
    }).toList();
  }

  /// ترتيب النتائج حسب معايير مختلفة
  List<Product> sortProducts({
    required List<Product> products,
    required String sortBy,
    bool ascending = true,
  }) {
    final sortedProducts = List<Product>.from(products);

    switch (sortBy) {
      case 'name':
        sortedProducts.sort((a, b) => ascending 
            ? a.name.compareTo(b.name)
            : b.name.compareTo(a.name));
        break;
      case 'price':
        sortedProducts.sort((a, b) {
          final priceA = a.price ?? 0;
          final priceB = b.price ?? 0;
          return ascending 
              ? priceA.compareTo(priceB)
              : priceB.compareTo(priceA);
        });
        break;
      case 'quantity':
        sortedProducts.sort((a, b) {
          final quantityA = a.quantity ?? 0;
          final quantityB = b.quantity ?? 0;
          return ascending 
              ? quantityA.compareTo(quantityB)
              : quantityB.compareTo(quantityA);
        });
        break;
      case 'value':
        sortedProducts.sort((a, b) {
          final valueA = (a.price ?? 0) * (a.quantity ?? 0);
          final valueB = (b.price ?? 0) * (b.quantity ?? 0);
          return ascending 
              ? valueA.compareTo(valueB)
              : valueB.compareTo(valueA);
        });
        break;
    }

    return sortedProducts;
  }

  /// إحصائيات البحث
  Map<String, dynamic> getSearchStatistics({
    required List<Product> products,
    required List<Customer> customers,
    required List<Sale> sales,
    String? query,
  }) {
    final stats = <String, dynamic>{};

    if (query != null && query.isNotEmpty) {
      final productResults = searchProducts(products: products, query: query);
      final customerResults = searchCustomers(customers: customers, query: query);
      final saleResults = searchSales(sales: sales, query: query);

      stats['total_results'] = productResults.length + customerResults.length + saleResults.length;
      stats['product_results'] = productResults.length;
      stats['customer_results'] = customerResults.length;
      stats['sale_results'] = saleResults.length;
      stats['search_query'] = query;
    } else {
      stats['total_results'] = products.length + customers.length + sales.length;
      stats['product_results'] = products.length;
      stats['customer_results'] = customers.length;
      stats['sale_results'] = sales.length;
      stats['search_query'] = null;
    }

    return stats;
  }
}

/// Provider للبحث المتقدم
class AdvancedSearchProvider extends ChangeNotifier {
  final AdvancedSearchService _service = AdvancedSearchService.instance;
  
  String _currentQuery = '';
  Map<String, dynamic> _currentFilters = {};
  List<String> _searchSuggestions = [];
  List<String> _recentSearches = [];
  Map<String, List<dynamic>> _searchResults = {};
  bool _isSearching = false;

  String get currentQuery => _currentQuery;
  Map<String, dynamic> get currentFilters => _currentFilters;
  List<String> get searchSuggestions => _searchSuggestions;
  List<String> get recentSearches => _recentSearches;
  Map<String, List<dynamic>> get searchResults => _searchResults;
  bool get isSearching => _isSearching;

  /// تحديث استعلام البحث
  void updateQuery(String query) {
    _currentQuery = query;
    notifyListeners();
  }

  /// تحديث الفلاتر
  void updateFilters(Map<String, dynamic> filters) {
    _currentFilters = filters;
    notifyListeners();
  }

  /// تنفيذ البحث الشامل
  Future<void> performGlobalSearch({
    required List<Product> products,
    required List<Customer> customers,
    required List<Sale> sales,
  }) async {
    _isSearching = true;
    notifyListeners();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // محاكاة البحث

      _searchResults = _service.globalSearch(
        query: _currentQuery,
        products: products,
        customers: customers,
        sales: sales,
      );

      if (_currentQuery.isNotEmpty) {
        _service.addRecentSearch(_currentQuery);
        _recentSearches = _service.getRecentSearches();
      }
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// الحصول على اقتراحات البحث
  void updateSearchSuggestions({
    required List<Product> products,
    required List<Customer> customers,
  }) {
    _searchSuggestions = _service.getSearchSuggestions(
      query: _currentQuery,
      products: products,
      customers: customers,
    );
    notifyListeners();
  }

  /// مسح البحث
  void clearSearch() {
    _currentQuery = '';
    _searchResults = {};
    _searchSuggestions = [];
    notifyListeners();
  }

  /// مسح عمليات البحث الأخيرة
  void clearRecentSearches() {
    _service.clearRecentSearches();
    _recentSearches = [];
    notifyListeners();
  }
}
