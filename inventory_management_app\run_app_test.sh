#!/bin/bash

# 🚀 سكريبت تشغيل تطبيق أسامة ماركت - اختبار شامل
# هذا السكريبت يطبق جميع الإصلاحات ويحاول تشغيل التطبيق

echo "🔧 بدء تشخيص وإصلاح مشاكل تطبيق أسامة ماركت..."
echo "=================================================="

# التحقق من وجود Flutter
echo "1️⃣ التحقق من تثبيت Flutter..."
if command -v flutter &> /dev/null; then
    echo "✅ Flutter متاح"
    flutter --version
else
    echo "❌ Flutter غير متاح. يرجى تثبيت Flutter أولاً."
    echo "📖 راجع: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# التحقق من حالة Flutter
echo ""
echo "2️⃣ فحص حالة Flutter..."
flutter doctor

# التحقق من الأجهزة المتاحة
echo ""
echo "3️⃣ فحص الأجهزة المتاحة..."
flutter devices

# تنظيف المشروع
echo ""
echo "4️⃣ تنظيف المشروع..."
flutter clean

# جلب التبعيات
echo ""
echo "5️⃣ جلب التبعيات المحدثة..."
flutter pub get

# فحص التبعيات القديمة
echo ""
echo "6️⃣ فحص التبعيات القديمة..."
flutter pub outdated

# محاولة البناء أولاً
echo ""
echo "7️⃣ محاولة بناء التطبيق..."
flutter build apk --debug

# إذا نجح البناء، جرب التشغيل
if [ $? -eq 0 ]; then
    echo ""
    echo "8️⃣ البناء نجح! محاولة تشغيل التطبيق..."
    flutter run --verbose
else
    echo ""
    echo "❌ فشل البناء. جاري تجربة حلول إضافية..."
    
    # تنظيف إضافي
    echo "🧹 تنظيف إضافي..."
    cd android
    ./gradlew clean
    cd ..
    
    # إعادة المحاولة
    echo "🔄 إعادة محاولة البناء..."
    flutter pub get
    flutter build apk --debug
    
    if [ $? -eq 0 ]; then
        echo "✅ البناء نجح بعد التنظيف الإضافي!"
        flutter run --verbose
    else
        echo "❌ البناء فشل مرة أخرى. راجع الأخطاء أعلاه."
        echo ""
        echo "🔍 حلول إضافية:"
        echo "- تأكد من تحديث Android SDK"
        echo "- تأكد من تحديث Android Studio"
        echo "- جرب تشغيل: flutter doctor --android-licenses"
        echo "- جرب تشغيل على الويب: flutter run -d chrome"
    fi
fi

echo ""
echo "=================================================="
echo "🏁 انتهى التشخيص والاختبار"
