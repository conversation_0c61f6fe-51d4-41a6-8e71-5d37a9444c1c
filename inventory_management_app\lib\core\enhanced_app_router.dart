import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../screens/enhanced_dashboard_screen.dart';
import '../screens/simple_products_screen.dart';
import '../screens/simple_customers_screen.dart';
import '../screens/simple_sales_screen.dart';
import '../screens/simple_settings_screen.dart';
import '../screens/products/product_details_screen.dart';
import '../screens/customers/customer_details_screen.dart';
import '../screens/sales/sale_details_screen.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';

/// Enhanced App Router with go_router for advanced navigation
class EnhancedAppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/',
    routes: [
      // Shell Route for Bottom Navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          // Home/Dashboard
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const EnhancedDashboardScreen(),
          ),
          
          // Products Routes
          GoRoute(
            path: '/products',
            name: 'products',
            builder: (context, state) => const SimpleProductsScreen(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-product',
                builder: (context, state) => const ProductDetailsScreen(),
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'edit-product',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return ProductDetailsScreen(productId: id);
                },
              ),
              GoRoute(
                path: '/details/:id',
                name: 'product-details',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return ProductDetailsScreen(productId: id, isReadOnly: true);
                },
              ),
            ],
          ),
          
          // Customers Routes
          GoRoute(
            path: '/customers',
            name: 'customers',
            builder: (context, state) => const SimpleCustomersScreen(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-customer',
                builder: (context, state) => const CustomerDetailsScreen(),
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'edit-customer',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return CustomerDetailsScreen(customerId: id);
                },
              ),
              GoRoute(
                path: '/details/:id',
                name: 'customer-details',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return CustomerDetailsScreen(customerId: id, isReadOnly: true);
                },
              ),
            ],
          ),
          
          // Sales Routes
          GoRoute(
            path: '/sales',
            name: 'sales',
            builder: (context, state) => const SimpleSalesScreen(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-sale',
                builder: (context, state) => const SaleDetailsScreen(),
              ),
              GoRoute(
                path: '/details/:id',
                name: 'sale-details',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return SaleDetailsScreen(saleId: id, isReadOnly: true);
                },
              ),
            ],
          ),
          
          // Settings
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SimpleSettingsScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => const ErrorScreen(),
  );

  static GoRouter get router => _router;
}

/// Main Shell with Bottom Navigation
class MainShell extends StatefulWidget {
  final Widget child;
  
  const MainShell({super.key, required this.child});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/sales');
        break;
      case 4:
        context.go('/settings');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Update selected index based on current route
    final String location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/products')) {
      _selectedIndex = 1;
    } else if (location.startsWith('/customers')) {
      _selectedIndex = 2;
    } else if (location.startsWith('/sales')) {
      _selectedIndex = 3;
    } else if (location.startsWith('/settings')) {
      _selectedIndex = 4;
    } else {
      _selectedIndex = 0;
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: widget.child,
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: Colors.blue,
          unselectedItemColor: Colors.grey,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2),
              label: 'المنتجات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people),
              label: 'العملاء',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.receipt),
              label: 'المبيعات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}

/// Error Screen for invalid routes
class ErrorScreen extends StatelessWidget {
  const ErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('خطأ'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'الصفحة غير موجودة',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'عذراً، الصفحة التي تبحث عنها غير موجودة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.go('/'),
          child: const Icon(Icons.home),
        ),
      ),
    );
  }
}
