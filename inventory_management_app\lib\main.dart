import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/providers/notification_provider.dart';
import 'package:inventory_management_app/providers/backup_provider.dart';
import 'package:inventory_management_app/providers/sync_provider.dart';
import 'package:inventory_management_app/providers/auto_backup_provider.dart';
import 'package:inventory_management_app/providers/customer_statement_provider.dart';
import 'package:inventory_management_app/providers/supplier_statement_provider.dart';
import 'package:inventory_management_app/providers/activity_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/enhanced_theme_provider.dart';
import 'package:inventory_management_app/providers/internal_transfer_provider.dart';
import 'package:inventory_management_app/providers/analytics_provider.dart';
import 'package:inventory_management_app/providers/store_inventory_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/backup_service.dart';


import 'package:inventory_management_app/screens/simple_products_screen.dart';
import 'package:inventory_management_app/screens/simple_customers_screen.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_list_screen.dart';
import 'package:inventory_management_app/screens/simple_settings_screen.dart';
import 'package:inventory_management_app/screens/enhanced_dashboard_screen.dart';
import 'package:inventory_management_app/screens/loading_screen.dart';

/// Workmanager callback for background tasks
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      print('🔄 Executing background task: $task');

      switch (task) {
        case 'autoBackupTask':
          await _performAutoBackup(inputData);
          break;
        default:
          print('❌ Unknown task: $task');
          return Future.value(false);
      }

      print('✅ Background task completed: $task');
      return Future.value(true);
    } catch (e) {
      print('❌ Background task failed: $task - $e');
      return Future.value(false);
    }
  });
}

/// Perform automatic backup
Future<void> _performAutoBackup(Map<String, dynamic>? inputData) async {
  try {
    final BackupService backupService = BackupService();

    // Create local backup
    await backupService.createLocalDatabaseBackup();
    print('✅ Auto backup: Local backup created');

    // Upload to cloud if enabled
    final bool backupToCloud = inputData?['backup_to_cloud'] ?? false;
    if (backupToCloud && backupService.isSignedIn) {
      await backupService.uploadDatabaseToGoogleDrive();
      print('✅ Auto backup: Cloud backup uploaded');
    }

    // Clean old backups
    await backupService.cleanOldLocalDatabaseBackups();
    print('✅ Auto backup: Old backups cleaned');

  } catch (e) {
    print('❌ Auto backup failed: $e');
    rethrow;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Workmanager
  try {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
    print('✅ Workmanager initialized successfully');
  } catch (e) {
    print('❌ Workmanager initialization error: $e');
  }

  // Initialize database
  try {
    await DatabaseService.instance.database;
    print('✅ Database initialized successfully');
  } catch (e) {
    print('❌ Database initialization error: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => EnhancedThemeProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SyncProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => AutoBackupProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ProductProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SupplierProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PurchaseProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => OrderProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ExpenseProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => NotificationProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => BackupProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerStatementProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SupplierStatementProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ActivityProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SaleProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => InternalTransferProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => AnalyticsProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => StoreInventoryProvider(),
        ),
      ],
      child: Consumer<EnhancedThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'إدارة المخزون المحسن',
            debugShowCheckedModeBanner: false,
            theme: EnhancedThemeProvider.lightTheme,
            darkTheme: EnhancedThemeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Widget _getSelectedWidget() {
    try {
      switch (_selectedIndex) {
        case 0:
          return _buildHomeTab();
        case 1:
          return _buildProductsTab();
        case 2:
          return _buildCustomersTab();
        case 3:
          return _buildSuppliersTab();
        case 4:
          return _buildSettingsTab();
        default:
          return _buildHomeTab();
      }
    } catch (e, s) {
      debugPrint('❌ خطأ في التبويب $_selectedIndex: $e');
      debugPrint('Stack trace: $s');
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'حدث خطأ في تحميل التبويب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تفاصيل الخطأ: $e',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedIndex = 0; // العودة للصفحة الرئيسية
                  });
                },
                icon: const Icon(Icons.home),
                label: const Text('العودة للرئيسية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildHomeTab() {
    try {
      return const EnhancedDashboardScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الصفحة الرئيسية: $e');
      return _buildErrorWidget('الصفحة الرئيسية', e.toString());
    }
  }

  Widget _buildProductsTab() {
    try {
      return const SimpleProductsScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة المنتجات: $e');
      return _buildErrorWidget('المنتجات', e.toString());
    }
  }

  Widget _buildCustomersTab() {
    try {
      return const SimpleCustomersScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة العملاء: $e');
      return _buildErrorWidget('العملاء', e.toString());
    }
  }

  Widget _buildSuppliersTab() {
    try {
      return const SupplierListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الموردين: $e');
      return _buildErrorWidget('الموردين', e.toString());
    }
  }

  Widget _buildSettingsTab() {
    try {
      return const SimpleSettingsScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الإعدادات: $e');
      return _buildErrorWidget('الإعدادات', e.toString());
    }
  }

  Widget _buildErrorWidget(String screenName, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل شاشة $screenName',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // إعادة بناء الشاشة
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('إدارة المخزون'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: _getSelectedWidget(),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: Colors.blue,
          unselectedItemColor: Colors.grey,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2),
              label: 'المنتجات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people),
              label: 'العملاء',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.local_shipping),
              label: 'الموردون',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}