import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/enhanced_theme_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/notification_service.dart';
import 'package:inventory_management_app/services/print_export_service.dart';
import 'package:inventory_management_app/services/mobile_features_service.dart';
import 'package:inventory_management_app/services/advanced_search_service.dart';
import 'package:inventory_management_app/services/gamification_service.dart';
import 'package:inventory_management_app/screens/simple_products_screen.dart';
import 'package:inventory_management_app/screens/simple_customers_screen.dart';
import 'package:inventory_management_app/screens/simple_sales_screen.dart';
import 'package:inventory_management_app/screens/simple_settings_screen.dart';
import 'package:inventory_management_app/screens/enhanced_dashboard_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SQLite for web
  if (kIsWeb) {
    databaseFactory = databaseFactoryFfiWeb;
  } else {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize database
  await DatabaseService.instance.database;

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) {
            final provider = EnhancedThemeProvider();
            provider.initialize(); // Load theme preferences
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = NotificationProvider();
            provider.initialize(); // Initialize notifications
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) => PrintExportProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => MobileFeaturesProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => AdvancedSearchProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = GamificationProvider();
            provider.initialize(); // Initialize gamification data
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = ProductProvider();
            provider.initialize(); // Load products from database
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = CustomerProvider();
            provider.initialize(); // Load customers from database
            return provider;
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            final provider = SaleProvider();
            provider.initialize(); // Load sales from database
            return provider;
          },
        ),
      ],
      child: Consumer<EnhancedThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'إدارة المخزون المحسن',
            debugShowCheckedModeBanner: false,
            theme: EnhancedThemeProvider.lightTheme,
            darkTheme: EnhancedThemeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Widget _getSelectedWidget() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildProductsTab();
      case 2:
        return _buildCustomersTab();
      case 3:
        return _buildSalesTab();
      case 4:
        return _buildSettingsTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return const EnhancedDashboardScreen();
  }

  Widget _buildProductsTab() {
    return const SimpleProductsScreen();
  }

  Widget _buildCustomersTab() {
    return const SimpleCustomersScreen();
  }

  Widget _buildSalesTab() {
    return const SimpleSalesScreen();
  }

  Widget _buildSettingsTab() {
    return const SimpleSettingsScreen();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('إدارة المخزون'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: _getSelectedWidget(),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: Colors.blue,
          unselectedItemColor: Colors.grey,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2),
              label: 'المنتجات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people),
              label: 'العملاء',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.receipt),
              label: 'المبيعات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}