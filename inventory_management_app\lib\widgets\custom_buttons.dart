import 'package:flutter/material.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';

/// زر أساسي مخصص للإجراءات الرئيسية
class CustomPrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final double? width;
  final double? height;

  const CustomPrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    Widget buttonChild;
    
    if (isLoading) {
      buttonChild = const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    return SizedBox(
      width: isFullWidth ? double.infinity : width,
      height: height ?? AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: AppDecorations.primaryButtonStyle,
        child: buttonChild,
      ),
    );
  }
}

/// زر ثانوي مخصص للإجراءات الثانوية
class CustomSecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isFullWidth;
  final double? width;
  final double? height;

  const CustomSecondaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isFullWidth = false,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    Widget buttonChild;
    
    if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    return SizedBox(
      width: isFullWidth ? double.infinity : width,
      height: height ?? AppDimensions.buttonHeight,
      child: OutlinedButton(
        onPressed: onPressed,
        style: AppDecorations.secondaryButtonStyle,
        child: buttonChild,
      ),
    );
  }
}

/// زر نصي مخصص للإجراءات البسيطة
class CustomTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final Color? textColor;

  const CustomTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    Widget buttonChild;
    
    if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: textColor ?? AppColors.primary),
          const SizedBox(width: 6),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    return TextButton(
      onPressed: onPressed,
      style: AppDecorations.textButtonStyle.copyWith(
        foregroundColor: MaterialStateProperty.all(textColor ?? AppColors.primary),
      ),
      child: buttonChild,
    );
  }
}

/// زر خطر للحذف والإجراءات الحساسة
class CustomDangerButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;

  const CustomDangerButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget buttonChild;
    
    if (isLoading) {
      buttonChild = const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: AppStyles.buttonTextStyle,
        ),
        child: buttonChild,
      ),
    );
  }
}

/// زر أيقونة دائري
class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final String? tooltip;

  const CustomIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size ?? 40,
      height: size ?? 40,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular((size ?? 40) / 2),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: iconColor ?? AppColors.textSecondary,
          size: (size ?? 40) * 0.5,
        ),
        tooltip: tooltip,
      ),
    );
  }
}

/// زر عائم مخصص للإضافة
class CustomFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final bool mini;

  const CustomFloatingActionButton({
    super.key,
    this.onPressed,
    this.icon = Icons.add,
    this.tooltip,
    this.backgroundColor,
    this.mini = false,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: backgroundColor ?? AppColors.accent,
      foregroundColor: Colors.white,
      tooltip: tooltip ?? 'إضافة',
      mini: mini,
      child: Icon(icon),
    );
  }
}

/// صف أزرار للإجراءات (حفظ/إلغاء)
class ActionButtonsRow extends StatelessWidget {
  final String? primaryText;
  final String? secondaryText;
  final VoidCallback? onPrimaryPressed;
  final VoidCallback? onSecondaryPressed;
  final bool isPrimaryLoading;
  final bool isFullWidth;

  const ActionButtonsRow({
    super.key,
    this.primaryText,
    this.secondaryText,
    this.onPrimaryPressed,
    this.onSecondaryPressed,
    this.isPrimaryLoading = false,
    this.isFullWidth = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          if (secondaryText != null) ...[
            Expanded(
              child: CustomSecondaryButton(
                text: secondaryText!,
                onPressed: onSecondaryPressed,
                isFullWidth: isFullWidth,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
          ],
          Expanded(
            child: CustomPrimaryButton(
              text: primaryText ?? 'حفظ',
              onPressed: onPrimaryPressed,
              isLoading: isPrimaryLoading,
              isFullWidth: isFullWidth,
            ),
          ),
        ],
      ),
    );
  }
}

/// أزرار الإجراءات للقوائم (تعديل/حذف)
class ListActionButtons extends StatelessWidget {
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onView;
  final bool showView;

  const ListActionButtons({
    super.key,
    this.onEdit,
    this.onDelete,
    this.onView,
    this.showView = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showView && onView != null)
          CustomIconButton(
            icon: Icons.visibility,
            onPressed: onView,
            iconColor: AppColors.info,
            tooltip: 'عرض',
            size: 36,
          ),
        if (onEdit != null) ...[
          if (showView && onView != null) const SizedBox(width: 4),
          CustomIconButton(
            icon: Icons.edit,
            onPressed: onEdit,
            iconColor: AppColors.primary,
            tooltip: 'تعديل',
            size: 36,
          ),
        ],
        if (onDelete != null) ...[
          const SizedBox(width: 4),
          CustomIconButton(
            icon: Icons.delete,
            onPressed: onDelete,
            iconColor: AppColors.error,
            tooltip: 'حذف',
            size: 36,
          ),
        ],
      ],
    );
  }
}
